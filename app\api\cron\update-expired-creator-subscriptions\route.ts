import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { creatorSubscriptions } from '@/lib/db/schema';
import { and, eq, lt } from 'drizzle-orm';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(request: Request) {
  console.log('Cron Job started to reset expired creator subscriptions');
  try {
    // Verify the request is from a trusted source (e.g., Vercel Cron)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    console.log('Auth Debug:', {
      receivedHeader: authHeader,
      expectedHeader: `Bearer ${cronSecret}`,
      matches: authHeader === `Bearer ${cronSecret}`,
      cronSecretExists: !!cronSecret
    });

    if (!cronSecret) {
      return new NextResponse('Server configuration error - CRON_SECRET not set', { status: 500 });
    }

    if (authHeader !== `Bearer ${cronSecret}`) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get current date at start of day (midnight) in UTC
    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);

    // Update expired subscriptions
    const result = await db
      .update(creatorSubscriptions)
      .set({
        isPaid: false,
        expiry_date: null,
        updated_at: new Date()
      })
      .where(
        and(
          eq(creatorSubscriptions.isPaid, true),
          lt(creatorSubscriptions.expiry_date, today.toISOString())
        )
      )
      .returning();

    return NextResponse.json({
      success: true,
      updatedSubscriptions: result.length
    });

  } catch (error) {
    console.error('Error in creator subscription expiry cron:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
