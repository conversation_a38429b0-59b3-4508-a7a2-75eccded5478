import type { NextAuthConfig } from 'next-auth';

export const authConfig = {
  pages: {
    signIn: '/login',
    newUser: '/dashboard',
  },
  providers: [
    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js
    // while this file is also used in non-Node.js environments
  ],
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnLogin = nextUrl.pathname.startsWith('/login');
      const isOnRegister = nextUrl.pathname.startsWith('/register');
      const isProtectedApi = nextUrl.pathname.startsWith('/api/chat');

      // Redirect logged-in users from auth pages
      if (isLoggedIn && (isOnLogin || isOnRegister)) {
        return Response.redirect(new URL('/dashboard', nextUrl as unknown as URL));
      }

      // Always allow access to auth pages
      if (isOnRegister || isOnLogin) {
        return true;
      }

      // Protect chat API routes
      if (isProtectedApi) {
        return isLoggedIn;
      }

      // Allow access to all other routes
      return true;
    },
  },
} satisfies NextAuthConfig;
