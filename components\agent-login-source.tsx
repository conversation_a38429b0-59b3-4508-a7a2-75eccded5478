'use client';

import { useEffect } from 'react';

export function AgentLoginSource() {
  useEffect(() => {
    // Set login source to 'agent' when this component is mounted
    fetch('/api/auth/login-source', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ source: 'agent' }),
    }).catch(error => {
      console.error('Error setting login source:', error);
    });
  }, []);

  // This component doesn't render anything
  return null;
}

/**
 * This is a utility function that can be called directly from any component
 * that needs to set the login source to 'agent'
 */
export function setAgentLoginSource() {
  fetch('/api/auth/login-source', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ source: 'agent' }),
  }).catch(error => {
    console.error('Error setting login source:', error);
  });
}
