'use client';

import { useState, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { chatModels } from '@/lib/ai/models';
import { modelRegistry } from '@/lib/ai/model-registry';

import { CheckCircleFillIcon, ChevronDownIcon } from './icons';
import { Brain as BrainIcon } from 'lucide-react';

/**
 * ModelSelector component that handles model selection with reasoning options
 * based on the model's capabilities
 */
export function ModelSelector({
  selectedModelId,
  onModelChange,
}: {
  selectedModelId: string;
  onModelChange?: (modelId: string) => void;
}) {
  // State for dropdown open/close
  const [open, setOpen] = useState(false);

  // Use local state to track if reasoning is enabled
  const [isReasoningEnabled, setIsReasoningEnabled] = useState(
    selectedModelId.endsWith('-reasoning'),
  );

  // Get the base model ID without the reasoning suffix
  const baseModelId = selectedModelId.endsWith('-reasoning')
    ? selectedModelId.replace(/-reasoning$/, '')
    : selectedModelId;

  // Find the model info
  const selectedChatModel = chatModels.find(
    (model) => model.id === baseModelId,
  );
  const modelInfo = selectedChatModel ? modelRegistry[baseModelId] : undefined;

  // Determine model capabilities
  const supportsReasoning = modelInfo?.supportsReasoning === true;
  const hasBuiltInReasoning = modelInfo?.reasoningBuiltIn === true;

  // Track the display name in state
  const [displayName, setDisplayName] = useState(
    selectedModelId.endsWith('-reasoning') && selectedChatModel
      ? `${selectedChatModel.name} (with reasoning)`
      : selectedChatModel?.name || baseModelId,
  );

  // Update local state when selectedModelId changes
  useEffect(() => {
    const hasReasoning = selectedModelId.endsWith('-reasoning');
    setIsReasoningEnabled(hasReasoning);

    if (selectedChatModel) {
      setDisplayName(
        hasReasoning
          ? `${selectedChatModel.name} (with reasoning)`
          : selectedChatModel.name,
      );
    }
  }, [selectedModelId, selectedChatModel]);

  // Event handlers for reasoning options
  const enableReasoning = () => {
    if (onModelChange) {
      // Update the model ID with reasoning suffix
      const reasoningModelId = `${baseModelId}-reasoning`;
      onModelChange(reasoningModelId);

      // Immediately update local state for responsive UI
      setIsReasoningEnabled(true);
      setDisplayName(
        `${selectedChatModel?.name || baseModelId} (with reasoning)`,
      );
    }
    setOpen(false);
  };

  const disableReasoning = () => {
    if (onModelChange) {
      // Update the model ID to base model without reasoning
      onModelChange(baseModelId);

      // Immediately update local state for responsive UI
      setIsReasoningEnabled(false);
      setDisplayName(selectedChatModel?.name || baseModelId);
    }
    setOpen(false);
  };

  // Early return if no model found
  if (!selectedChatModel) return null;

  // MODEL TYPE 1: Models with built-in reasoning (single disabled dropdown option)
  if (hasBuiltInReasoning) {
    return (
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            data-testid="model-selector"
            variant="outline"
            className="md:px-2 md:h-[34px] flex items-center gap-1"
          >
            <BrainIcon className="size-3 text-black ml-1" />
            {selectedChatModel.name}
            <span className="text-xs text-muted-foreground ml-1">
              (reasoning built-in)
            </span>
            <ChevronDownIcon />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem
            disabled
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-2">
              {selectedChatModel.name}
              <span className="text-xs text-muted-foreground">
                (reasoning built-in)
              </span>
            </div>
            <CheckCircleFillIcon />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // MODEL TYPE 2: Models with optional reasoning (two dropdown options)
  if (supportsReasoning) {
    return (
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            data-testid="model-selector"
            variant="outline"
            className="md:px-2 md:h-[34px] flex items-center gap-1"
          >
            {isReasoningEnabled && (
              <BrainIcon className="size-3 text-black ml-1" />
            )}
            {displayName}

            <ChevronDownIcon />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          {/* Without reasoning option */}
          <DropdownMenuItem
            onClick={disableReasoning}
            className="flex items-center justify-between"
          >
            <span>{selectedChatModel.name}</span>
            {!isReasoningEnabled && <CheckCircleFillIcon />}
          </DropdownMenuItem>

          {/* With reasoning option */}
          <DropdownMenuItem
            onClick={enableReasoning}
            className="flex items-center justify-between"
          >
            <span>{selectedChatModel.name} (with reasoning)</span>
            {isReasoningEnabled && <CheckCircleFillIcon />}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // MODEL TYPE 3: Models without reasoning support (simple button)
  return (
    <Button
      data-testid="model-selector"
      variant="outline"
      className="md:px-2 md:h-[34px] flex items-center gap-1"
    >
      {selectedChatModel.name}
    </Button>
  );
}
