'use client';

import { Fragment, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, Transition } from '@headlessui/react';
import { useSession, signIn } from 'next-auth/react';
import { toast } from 'sonner';
import { sendLoginCode, verifyLoginCode } from '@/lib/actions/auth.actions';
import AuthForm from '@/components/forms/AuthForm';
import { EmailSchema, CodeSchema } from '@/lib/validations';

export function AuthModal({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) {
  const [step, setStep] = useState<'email' | 'verify'>('email');
  const [email, setEmail] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendTimer, setResendTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const { update } = useSession();

  // Initialize and handle countdown timer for resend button
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (resendTimer > 0 && isResendDisabled && step === 'verify') {
      intervalId = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            setIsResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [resendTimer, isResendDisabled, step]);

  // Reset states when modal closes
  useEffect(() => {
    if (!isOpen) {
      setStep('email');
      setEmail('');
      setIsVerifying(false);
      setResendTimer(60);
      setIsResendDisabled(true);
    }
  }, [isOpen]);

  // Handle resend code
  const handleResendCode = async () => {
    try {
      setIsResendDisabled(true);
      setResendTimer(60);

      const res = await sendLoginCode({ email });

      if (!res.success) {
        toast.error(res.message || 'Failed to resend login code');
        // Re-enable button on error
        setIsResendDisabled(false);
        setResendTimer(0);
      }
    } catch (error) {
      console.error('Error resending code:', error);
      toast.error('Failed to resend login code');
      // Re-enable button on error
      setIsResendDisabled(false);
      setResendTimer(0);
    }
  };

  // Format email for display - mask middle part if too long
  const displayEmail =
    email.length > 25
      ? `${email.substring(0, 10)}...${email.substring(email.indexOf('@') - 2)}`
      : email;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" />
        </Transition.Child>

        {/* Show overlay when verifying */}
        {isVerifying && (
          <div className="fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50">
            <div className="animate-spin rounded-full size-8 border-b-2 border-primary" />
          </div>
        )}

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md overflow-hidden rounded-lg shadow-lg">
                <style jsx>{`
                  .auth-modal-form-container :global(.flex.h-screen.w-screen) {
                    height: auto !important;
                    width: auto !important;
                    min-height: unset !important;
                  }
                  .auth-modal-form-container :global(.w-full.max-w-md) {
                    box-shadow: none !important;
                  }
                `}</style>

                {/* Email step */}
                {step === 'email' && (
                  <div className="auth-modal-form-container overflow-hidden">
                    <AuthForm
                      schema={EmailSchema}
                      defaultValues={{ email: '' }}
                      submitButtonLabel="Continue"
                      formType="SIGN_IN"
                      showFooterLink={false}
                      preventRedirect={true}
                      hideLogo={true}
                      suppressToasts={true}
                      onSubmit={async ({ email }: { email: string }) => {
                        try {
                          const res = await sendLoginCode({ email });

                          if (res.success) {
                            // Save email and transition to verify step
                            setEmail(email);
                            setResendTimer(60); // Reset resend timer
                            setIsResendDisabled(true);
                            setStep('verify');
                            return { success: true };
                          } else {
                            toast.error(
                              res.message || 'Failed to send login code',
                            );
                            return { success: false, message: res.message };
                          }
                        } catch (error) {
                          console.error('Error sending login code:', error);
                          toast.error('An error occurred. Please try again.');
                          return {
                            success: false,
                            message: 'An error occurred',
                          };
                        }
                      }}
                    />
                  </div>
                )}

                {/* Verify step */}
                {step === 'verify' && (
                  <div className="auth-modal-form-container overflow-hidden relative">
                    <div className="absolute top-8 left-8 z-10">
                      <button
                        onClick={() => setStep('email')}
                        type="button"
                        className="inline-flex items-center justify-center size-6 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polyline points="15 18 9 12 15 6" />
                        </svg>
                      </button>
                    </div>
                    <AuthForm
                      schema={CodeSchema}
                      defaultValues={{ code: '' }}
                      submitButtonLabel="Verify Code"
                      formType="VERIFY"
                      subtitle={`Please enter the 6 digit code we sent to ${displayEmail}.`}
                      showFooterLink={false}
                      preventRedirect={true}
                      hideLogo={true}
                      suppressToasts={true}
                      footerContent={
                        <div className="flex justify-end mt-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleResendCode}
                            disabled={isResendDisabled}
                            className="text-sm text-muted-foreground hover:text-primary"
                          >
                            {isResendDisabled
                              ? `Resend code in ${resendTimer}s`
                              : 'Resend code'}
                          </Button>
                        </div>
                      }
                      onSubmit={async ({ code }: { code: string }) => {
                        try {
                          setIsVerifying(true);
                          // First verify the code is valid
                          const res = await verifyLoginCode({ email, code });

                          if (!res.success) {
                            setIsVerifying(false);
                            // Return the error in the format expected by ActionResponse
                            return {
                              success: false,
                              error: {
                                message: res.message || 'Verification failed',
                              },
                            };
                          }

                          try {
                            // Sign in with Next Auth credentials provider
                            const result = await signIn('credentials', {
                              email,
                              code,
                              redirect: false,
                            });

                            if (result?.error) {
                              throw new Error(result.error);
                            }

                            // Update the session to reflect the new login state
                            await update();
                            window.location.reload();

                            // Call the success callback if provided
                            if (onSuccess) {
                              await onSuccess();
                            }

                            setIsVerifying(false);
                            onClose();

                            // The components will re-render based on the auth state change
                            // triggered by the onSuccess callback
                          } catch (error) {
                            console.error(
                              'Error finalizing authentication:',
                              error,
                            );
                            setIsVerifying(false);
                            return {
                              success: false,
                              error: {
                                message:
                                  'Error finalizing authentication. Please try again.',
                              },
                            };
                          }

                          return { success: true };
                        } catch (error) {
                          console.error('Verification error:', error);
                          setIsVerifying(false);
                          toast.error(
                            'An error occurred during verification. Please try again.',
                          );
                          return {
                            success: false,
                            message:
                              'An error occurred during verification. Please try again.',
                          };
                        }
                      }}
                    />
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
