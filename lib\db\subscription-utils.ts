import { db } from './client';
import { creatorPlans, creatorSubscriptions, subscriptionBalance, creatorPayments, userSubscriptions, userCancelationRequest, agents } from './schema';
import { and, eq, ne } from 'drizzle-orm';
import { getTotalKnowledgebaseSize, getTotalAgentsCreatedByUser } from './agent-queries';

type SubscriptionType = 'trial' | 'monthly' | 'yearly';
type SubscriptionStatus = 'active' | 'inactive' | 'scheduled' | 'created';

interface CreateSubscriptionParams {
  userId: string;
  planId: string;
  subscription_type: SubscriptionType;
  expiry_date: string;
  isPaid?: boolean;
  amount_paid?: number;
  status?: SubscriptionStatus;
  effective_date?: Date;
}

interface CreateBalanceParams {
  userId: string;
  subscription_id: string;
  expiry_date: string;
  plan: {
    tokensAllowed: number;
    agentsAllowed: number;
    knowledgeBaseSize: string;
  };
}


/**
 * Find a creator plan by its name
 * @param productName The name of the plan/product
 * @returns The plan details or null if not found
 */
export async function findPlanByName(productName: string) {
  const plan = await db
    .select()
    .from(creatorPlans)
    .where(eq(creatorPlans.name, productName))
    .limit(1)
    .then(rows => rows[0]);

  return plan || null;
}


/**
 * Create a new creator subscription
 * @param params Subscription parameters
 * @returns The newly created subscription
 */
export async function createSubscription(params: CreateSubscriptionParams) {
  const {
    userId,
    planId,
    subscription_type,
    expiry_date,
    isPaid = false,
    amount_paid = 0,
    status = 'created',
    effective_date = new Date()
  } = params;

  const newSubscription = await db
    .insert(creatorSubscriptions)
    .values({
      userId,
      planId,
      isPaid,
      subscription_type,
      expiry_date,
      effective_date,
      amount_paid,
      status
    })
    .returning()
    .then((res) => res[0]);

  return newSubscription;
}


/**
 * Create a subscription balance record with initial values from plan
 * @param params Balance parameters including subscription ID and plan details
 * @returns The newly created balance record
 */
export async function createSubscriptionBalance(params: CreateBalanceParams) {
  const { subscription_id, plan, expiry_date, userId } = params;

  const [knowledgebaseSizeUsed, totalAgentsCreated] = await Promise.all([
    getTotalKnowledgebaseSize(userId),
    getTotalAgentsCreatedByUser(userId)
  ]);

  const balance = await db
    .insert(subscriptionBalance)
    .values({
      subscription_id,
      total_tokens: plan.tokensAllowed,
      total_agents: plan.agentsAllowed,
      total_knowledgebase: parseInt(plan.knowledgeBaseSize) || 0,
      tokensLeft: plan.tokensAllowed,
      agentsLeft: plan.agentsAllowed - totalAgentsCreated,
      knowledgeBaseLeft: parseInt(plan.knowledgeBaseSize) - knowledgebaseSizeUsed || 0,
      expiry_date: expiry_date.split('T')[0]
    })
    .returning()
    .then((res) => res[0]);

  return balance;
}

interface UpdateSubscriptionWhereParams {
  userId: string;
  planId: string;
  status: SubscriptionStatus;
}

interface UpdateSubscriptionSetParams {
  expiry_date?: string;
  subscription_type?: SubscriptionType;
  status?: SubscriptionStatus;
  isPaid?: boolean;
  amount_paid?: number;
  effective_date?: Date;
}

/**
 * Update a subscription with the given parameters
 * @param whereParams Parameters for the where clause
 * @param setParams Parameters to update
 * @returns The updated subscription or null if not found
 */
export async function updateSubscription(
  whereParams: UpdateSubscriptionWhereParams,
  setParams: UpdateSubscriptionSetParams
) {
  const subscription = await db
    .update(creatorSubscriptions)
    .set(setParams)
    .where(
      and(
        eq(creatorSubscriptions.userId, whereParams.userId),
        eq(creatorSubscriptions.planId, whereParams.planId),
        eq(creatorSubscriptions.status, whereParams.status)
      )
    )
    .returning()
    .then((res) => res[0]);

  return subscription || null;
}

/**
 * Find an existing subscription for a user with specific plan and status
 * @param userId The user ID
 * @param planId The plan ID
 * @param status The subscription status to look for
 * @returns The subscription if found, null otherwise
 */
export async function findExistingSubscription(userId: string, planId: string, status: SubscriptionStatus) {
  const subscription = await db
    .select()
    .from(creatorSubscriptions)
    .where(
      and(
        eq(creatorSubscriptions.userId, userId),
        eq(creatorSubscriptions.planId, planId),
        eq(creatorSubscriptions.status, status)
      )
    )
    .limit(1)
    .then(rows => rows[0]);

  return subscription || null;
}

/**
 * Mark all active subscriptions as inactive for a user, except for the specified subscription
 * @param userId The user ID
 * @param excludeSubscriptionId The subscription ID to exclude from being marked inactive
 * @returns Array of updated subscriptions
 */
export async function updateOtherSubscriptionsInactive(userId: string, planId: string) {
  const result = await db
    .update(creatorSubscriptions)
    .set({
      status: 'inactive' as const
    })
    .where(
      and(
        eq(creatorSubscriptions.userId, userId),
        eq(creatorSubscriptions.status, 'active'),
        // Exclude the current subscription that was just activated
        ne(creatorSubscriptions.planId, planId)
      )
    )
    .returning();

  return result;
}

/**
 * Create a subscription balance record with initial values from plan, considering current usage
 * @param params Balance parameters including subscription ID, plan details, and user ID
 * @returns The newly created balance record
 */
export interface CreatePaymentParams {
  userId: string;
  subscriptionId: string;
  amount: number;
  transaction_date?: Date;
}

/**
 * Create a payment record for a creator subscription
 * @param params Payment parameters including user ID, subscription ID, and amount
 * @returns The newly created payment record
 */
export async function createCreatorPayment(params: CreatePaymentParams) {
  const { userId, subscriptionId, amount, transaction_date = new Date() } = params;

  const payment = await db
    .insert(creatorPayments)
    .values({
      userId,
      subscriptionId,
      amount,
      transaction_date
    })
    .returning()
    .then((res) => res[0]);

  return payment;
}
/**
 * Mark a subscription as inactive by its ID
 * @param subscriptionId The ID of the subscription to mark as inactive
 * @returns The updated subscription or null if not found
 */
export async function markUserSubscriptionInactive(subscriptionId: string) {
  const updatedSubscription = await db
    .update(userSubscriptions)
    .set({ 
      status: 'inactive' as const,
      updated_at: new Date()
    })
    .where(eq(userSubscriptions.id, subscriptionId))
    .returning()
    .then(rows => rows[0]);

  return updatedSubscription || null;
}

/**
 * Update the cancellation request status for a subscription
 * @param subscriptionId The ID of the subscription
 * @returns The updated cancellation request if found and updated, null otherwise
 */
export async function updateCancellationRequest(subscriptionId: string) {
  // Check for existing cancellation request
  const [existingRequest] = await db
    .select()
    .from(userCancelationRequest)
    .where(eq(userCancelationRequest.subscriptionId, subscriptionId));

  if (!existingRequest) {
    return null;
  }

  // Update the cancellation request status
  const [updatedRequest] = await db
    .update(userCancelationRequest)
    .set({
      status: 'canceled' as const,
      updated_at: new Date()
    })
    .where(eq(userCancelationRequest.subscriptionId, subscriptionId))
    .returning();

  return updatedRequest;
}

/**
 * Find active user subscription for a given user
 * @param userId The ID of the user
 * @returns The active subscription if found, null otherwise
 */
export async function findActiveUserSubscription(userId: string) {
  const [subscription] = await db
    .select()
    .from(userSubscriptions)
    .where(
      and(
        eq(userSubscriptions.userId, userId),
        eq(userSubscriptions.status, 'active')
      )
    );

  return subscription || null;
}

/**
 * Mark all agents of a user as inactive
 * @param userId The ID of the user
 * @returns Array of updated agents
 */
export async function markAgentsAsInactive(userId: string) {
  const updatedAgents = await db
    .update(agents)
    .set({
      status: 'inactive' as const,
      updated_at: new Date()
    })
    .where(
      and(
        eq(agents.userId, userId),
        eq(agents.status, 'active')
      )
    )
    .returning();

  return updatedAgents;
}

export async function createSubscriptionBalanceWithUsage({
  subscription_id,
  plan,
  expiry_date,
  userId
}: CreateBalanceParams & { userId: string }) {
  // Get current usage stats
  const [knowledgebaseSizeUsed, totalAgentsCreated] = await Promise.all([
    getTotalKnowledgebaseSize(userId),
    getTotalAgentsCreatedByUser(userId)
  ]);
  
  const planKnowledgebaseSize = parseInt(plan.knowledgeBaseSize) || 0;
          
  const balance = await db
    .insert(subscriptionBalance)
    .values({
      subscription_id,
      total_tokens: plan.tokensAllowed,
      total_agents: plan.agentsAllowed,
      total_knowledgebase: planKnowledgebaseSize,
      tokensLeft: plan.tokensAllowed,
      agentsLeft: plan.agentsAllowed - totalAgentsCreated,
      knowledgeBaseLeft: planKnowledgebaseSize - knowledgebaseSizeUsed,
      expiry_date: expiry_date.split('T')[0]
    })
    .returning()
    .then((res) => res[0]);

  return balance;
}
