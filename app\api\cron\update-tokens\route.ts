import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { sql } from 'drizzle-orm';
import { creatorSubscriptions, agents, chat, message, subscriptionBalance } from '@/lib/db/schema';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(request: Request) {
  console.log('🔄 Starting tokens update cron job...');
  try {
    // Verify the request is from a trusted source (e.g., Vercel Cron)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      console.log('❌ Authorization failed: Invalid cron secret');
      return new NextResponse('Unauthorized', { status: 401 });
    }
    console.log('✅ Authorization successful');

    console.log('📊 Calculating token consumption...');
    console.log('💫 Executing update query...');
    const result = await db.execute(sql`
      WITH tokens_consumed AS (
        SELECT 
          cs.id as subscription_id,
          COALESCE(SUM(m.tokens), 0) as total_tokens_used
        FROM ${creatorSubscriptions} cs
        LEFT JOIN ${agents} a ON a."userId" = cs."userId"
        LEFT JOIN ${chat} c ON c."agentId" = a.id
        LEFT JOIN ${message} m ON m."chatId" = c.id
        GROUP BY cs.id
      )
      UPDATE ${subscriptionBalance} cb
      SET "tokensLeft" = "total_tokens" - tc.total_tokens_used
      FROM tokens_consumed tc
      WHERE cb."subscription_id" = tc.subscription_id
      RETURNING *
    `);
    console.log('✅ Update complete:', result);

    return NextResponse.json({ 
      success: true, 
      message: 'Tokens updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error updating tokens:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({ 
      error: 'Failed to update tokens',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
