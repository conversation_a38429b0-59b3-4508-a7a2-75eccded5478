-- Step 1: Migrate existing model values from monetization to the new model field
UPDATE agents
SET model = monetization->>'model'
WHERE monetization->>'model' IS NOT NULL;

-- Step 2: Remove the model field from the monetization JSON
UPDATE agents
SET monetization = monetization - 'model'
WHERE monetization ? 'model';

-- Step 3: Update specific agents with new model values
-- <PERSON> Jobs - GPT-4o
UPDATE agents
SET model = 'chat-model'
WHERE slug = 'stevejobs';

-- CookieBot - GPT-4o with reasoning
UPDATE agents
SET model = 'chat-model-reasoning'
WHERE slug = 'cookiebot';

-- Fitness Coach - Claude 3 Haiku
UPDATE agents
SET model = 'claude-haiku'
WHERE slug = 'fitness-coach';

-- Travel Advisor - Grok 2
UPDATE agents
SET model = 'xai-grok-2'
WHERE slug = 'travel-advisor';

-- Language Tutor - Claude 3 Opus
UPDATE agents
SET model = 'claude-opus'
WHERE slug = 'language-tutor';

-- Step 4: Verify the updates
SELECT slug, model, monetization
FROM agents
ORDER BY slug;
