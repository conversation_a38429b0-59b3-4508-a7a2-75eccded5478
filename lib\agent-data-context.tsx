'use client';

import React, { createContext, useContext, useCallback, useEffect } from 'react';
import { registerContextReset } from './reset-app-state';
import type { ReactNode } from 'react';
import useSWR from 'swr';
import { fetcher } from '@/lib/utils';

// Define the shape of our consolidated data
interface AgentBootstrapData {
  agent: any;
  profile: any;
  subscription: {
    remainingMessages: number;
    isPaid: boolean;
    expiryDate?: string;
    isCanceled?: boolean;
    cancelDate?: string;
  };
  votes: any[];
}

interface AgentDataContextType {
  data?: AgentBootstrapData;
  error?: Error;
  isLoading: boolean;
  mutate: () => Promise<AgentBootstrapData | undefined>;
  resetAgentData: () => void;
}

const AgentDataContext = createContext<AgentDataContextType | undefined>(
  undefined,
);

interface AgentDataProviderProps {
  children: ReactNode;
  agentId: string;
  chatId?: string;
  initialData?: AgentBootstrapData;
}

export const AgentDataProvider: React.FC<AgentDataProviderProps> = ({
  children,
  agentId,
  chatId,
  initialData,
}) => {
  const apiUrl = chatId
    ? `/api/agents/${agentId}/bootstrap?chatId=${chatId}`
    : `/api/agents/${agentId}/bootstrap`;

  const { data, error, mutate, isValidating } = useSWR<AgentBootstrapData>(
    apiUrl,
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 seconds
      fallbackData: initialData,
      onError: (err) => {
        console.error('[AgentDataContext] Error loading data:', err);
      },
    },
  );
  
  // Function to reset agent data
  const resetAgentData = useCallback(() => {
    // Clear SWR cache for this endpoint
    mutate(undefined, false);
    console.log('Agent data context state reset');
  }, [mutate]);
  
  // Register the reset function
  useEffect(() => {
    registerContextReset(resetAgentData);
  }, [resetAgentData]);

  return (
    <AgentDataContext.Provider
      value={{
        data,
        error,
        isLoading: !data && !error,
        mutate,
        resetAgentData,
      }}
    >
      {children}
    </AgentDataContext.Provider>
  );
};

export const useAgentData = (): AgentDataContextType => {
  const context = useContext(AgentDataContext);
  if (context === undefined) {
    throw new Error('useAgentData must be used within an AgentDataProvider');
  }
  return context;
};
