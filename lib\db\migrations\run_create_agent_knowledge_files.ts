import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import fs from 'fs';
import path from 'path';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL is not defined');
}

// Create database client after environment variables are loaded
const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

async function runMigration() {
  try {
    console.log('Starting migration: Creating agent_knowledge_files table');
    
    const migrationPath = path.join(__dirname, '0032_create_agent_knowledge_files.sql');
    const migration = fs.readFileSync(migrationPath, 'utf8');
    
    await db.execute(sql.raw(migration));
    
    console.log('✅ Successfully created agent_knowledge_files table');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    // Close the database connection
    await client.end();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  runMigration()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
