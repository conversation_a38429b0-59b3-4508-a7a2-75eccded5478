const { sql } = require('drizzle-orm');
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

// Initialize the database connection
const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

/**
 * Migration to remove agent_id column from Message_v2 table since it's redundant
 */
async function removeAgentIdColumn() {
  console.log('🔄 Removing agent_id column from Message_v2 table...');
  
  try {
    await db.execute(sql`
      ALTER TABLE "Message_v2"
      DROP COLUMN IF EXISTS "agent_id";
    `);
    
    console.log('✅ agent_id column removed successfully!');
  } catch (error) {
    console.error('❌ Failed to remove agent_id column:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  removeAgentIdColumn()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
