import { db } from '@/lib/db';
import { userSubscriptions, userCancelationRequest, agents, agentStripeInfo } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function handleCancelRequestLogForuser(
  userId: string,
  productId: string,
  cancelAt: Date | null
): Promise<void> {
  try {
    // Find the agent by product name using agentStripeInfo
    const agentInfo = await db
      .select()
      .from(agentStripeInfo)
      .where(eq(agentStripeInfo.productId, productId))
      .limit(1)
      .then(rows => rows[0]);

    if (!agentInfo) {
      throw new Error(`Agent not found for product: ${productId}`);
    }

    // Find the active subscription in our database
    const activeSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.userId, userId),
          eq(userSubscriptions.agentId, agentInfo.agentId),
          eq(userSubscriptions.status, 'active')
        )
      )
      .limit(1)
      .then(rows => rows[0]);

    if (activeSubscription) {
      await db.insert(userCancelationRequest).values({
        userId: userId,
        subscriptionId: activeSubscription.id,
        status: 'scheduled'
      });
      console.log("User subscription cancel request logged for user:", userId);
    } else {
      console.log('No active subscription found for user:', userId);
    }
  } catch (error) {
    console.error('Error handling user cancellation request:', error);
    throw error;
  }
}
