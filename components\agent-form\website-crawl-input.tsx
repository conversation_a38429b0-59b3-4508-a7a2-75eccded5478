/* eslint-disable react/no-unescaped-entities */
'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Loader2,
  Globe,
  ChevronDown,
  ChevronRight,
  X,
  Search,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { UseFormReturn } from 'react-hook-form';

// Define the form values type
interface FormValues {
  websiteUrl?: string;
  selectedLinks?: Array<{website: string, links: Array<{url: string, status: 'selected' | 'active' | 'to-delete' | 'removed'}>}>;
}

interface CrawledLink {
  url: string;
}

interface CrawledWebsite {
  domain: string;
  lastCrawled: string;
  linkCount: number;
  links: CrawledLink[];
  isExpanded: boolean;
}

interface ExcludeDialogState {
  isOpen: boolean;
  linkUrl: string;
  domain: string;
}

interface WebsiteCrawlInputProps {
  form: UseFormReturn<any>;
  onSaveDraft?: () => Promise<any>;
  isEditMode?: boolean;
}

// Validate website URL
const validateWebsiteUrl = (
  url: string,
): { isValid: boolean; error: string | null } => {
  if (!url || url.trim() === '') {
    return { isValid: true, error: null }; // Empty URL is valid (field is optional)
  }

  try {
    // Check if URL is valid
    const urlObj = new URL(url);

    // Check if protocol is http or https
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      return {
        isValid: false,
        error: 'URL must start with http:// or https://',
      };
    }

    // Check if URL has a valid domain
    if (!urlObj.hostname || urlObj.hostname.length < 3) {
      return { isValid: false, error: 'URL must have a valid domain name' };
    }

    return { isValid: true, error: null };
  } catch (error) {
    return {
      isValid: false,
      error: 'Please enter a valid URL (e.g., https://example.com)',
    };
  }
};

export default function WebsiteCrawlInput({
  form,
  onSaveDraft,
  isEditMode = false,
}: WebsiteCrawlInputProps) {
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isFetching, setIsFetching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [crawledWebsites, setCrawledWebsites] = useState<CrawledWebsite[]>([]);
  const [selectedLinks, setSelectedLinks] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('url-depth-asc');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [excludeDialog, setExcludeDialog] = useState<ExcludeDialogState>({
    isOpen: false,
    linkUrl: '',
    domain: '',
  });
  const [isProcessingUrl, setIsProcessingUrl] = useState<string | null>(null);
  const [manualUrls, setManualUrls] = useState<string>('');
  const [forceRerender, setForceRerender] = useState(0); // Force re-render trigger

  // Initialize component with existing selectedLinks from form
  useEffect(() => {
    const formSelectedLinks = form.getValues('selectedLinks') || [];
    
    if (formSelectedLinks.length > 0 && crawledWebsites.length === 0) {
      // Flatten all links and filter for active and selected status during draft load
      const allLinks: {url: string, status: string}[] = [];
      
      // Use optional chaining to prevent forEach errors
      formSelectedLinks.forEach((websiteGroup: { links?: any[]; }) => {
        websiteGroup?.links?.forEach(link => {
          if (link?.status === 'active' || link?.status === 'selected') {
            allLinks.push(link);
          }
        });
      });
      
      if (allLinks.length > 0) {
        // Extract URLs for selectedLinks state (active and selected)
        const urls = allLinks.filter(link => link.status === 'active' || link.status === 'selected').map(link => link.url);
        setSelectedLinks(urls);
        
        // Group links by domain to create crawledWebsites
        const linksByDomain: Record<string, CrawledLink[]> = {};
        
        // Helper function to get the base domain from a hostname
        const getBaseDomain = (hostname: string): string => {
          const parts = hostname.split('.');
          if (parts.length >= 2) {
            return parts.slice(-2).join('.');
          }
          return hostname;
        };
        
        allLinks.forEach(linkObj => {
          try {
            if (!linkObj.url) {
              console.warn('Skipping link with undefined URL:', linkObj);
              return;
            }
            
            const fullDomain = new URL(linkObj.url).hostname;
            const baseDomain = getBaseDomain(fullDomain);
            
            if (!linksByDomain[baseDomain]) {
              linksByDomain[baseDomain] = [];
            }
            linksByDomain[baseDomain].push({ url: linkObj.url });
          } catch (error) {
            console.error('Invalid URL:', linkObj.url);
          }
        });
        
        // Create crawled websites from grouped links
        const websites = Object.keys(linksByDomain).map(domain => ({
          domain,
          lastCrawled: new Date().toISOString(),
          linkCount: linksByDomain[domain].length,
          links: linksByDomain[domain],
          isExpanded: true,
        }));
        
        setCrawledWebsites(websites);
        setShowResults(true);
        
        // Set websiteUrl from first website group if available
        if (formSelectedLinks.length > 0 && formSelectedLinks[0].website) {
          const currentWebsiteUrl = form.getValues('websiteUrl');
          if (!currentWebsiteUrl) {
            form.setValue('websiteUrl', formSelectedLinks[0].website);
          }
        }
      }
    }
  }, [form]);

  // Force rerender effect to ensure UI updates after dialog actions
  useEffect(() => {
    if (forceRerender > 0) {
      // console.log('🔄 Force rerender triggered:', forceRerender);
    }
  }, [forceRerender]);

  // Fuzzy search function
  const fuzzySearch = (text: string, query: string): boolean => {
    if (!query.trim()) return true;
    
    const searchText = text.toLowerCase();
    const searchQuery = query.toLowerCase().trim();
    
    // Direct substring match
    if (searchText.includes(searchQuery)) return true;
    
    // Fuzzy matching - check if all characters exist in order
    let queryIndex = 0;
    for (let i = 0; i < searchText.length && queryIndex < searchQuery.length; i++) {
      if (searchText[i] === searchQuery[queryIndex]) {
        queryIndex++;
      }
    }
    
    return queryIndex === searchQuery.length;
  };

  // Filter links based on search query
  const filterLinks = (links: CrawledLink[]): CrawledLink[] => {
    if (!searchQuery.trim()) return links;
    
    return links.filter(link => fuzzySearch(link.url, searchQuery));
  };

  // Sorting function for links
  const sortLinks = (links: CrawledLink[], sortType: string): CrawledLink[] => {
    const linksCopy = [...links];
    
    switch (sortType) {
      case 'url-length-asc':
        return linksCopy.sort((a, b) => a.url.length - b.url.length);
      case 'url-length-desc':
        return linksCopy.sort((a, b) => b.url.length - a.url.length);
      case 'url-depth-asc':
        return linksCopy.sort((a, b) => {
          const depthA = a.url.split('/').length - 3; // Subtract protocol and domain parts
          const depthB = b.url.split('/').length - 3;
          return depthA - depthB;
        });
      case 'url-depth-desc':
        return linksCopy.sort((a, b) => {
          const depthA = a.url.split('/').length - 3;
          const depthB = b.url.split('/').length - 3;
          return depthB - depthA;
        });
    }
    return linksCopy;
  };

  // Get filtered and sorted crawled websites
  const getFilteredAndSortedCrawledWebsites = (): CrawledWebsite[] => {
    let processedWebsites = crawledWebsites;
    
    // Apply filtering first
    if (searchQuery.trim()) {
      processedWebsites = crawledWebsites.map(website => ({
        ...website,
        links: filterLinks(website.links)
      })).filter(website => website.links.length > 0); // Only show domains with matching links
    }
    
    // Apply sorting
    if (sortBy === 'default') {
      return processedWebsites;
    }
    
    return processedWebsites.map(website => ({
      ...website,
      links: sortLinks(website.links, sortBy),
      linkCount: website.links.length // Update count to reflect filtered results
    }));
  };

  // Get total filtered results count
  const getFilteredResultsCount = (): { total: number; domains: number } => {
    const filtered = getFilteredAndSortedCrawledWebsites();
    const total = filtered.reduce((sum, website) => sum + website.links.length, 0);
    return { total, domains: filtered.length };
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    form.setValue('websiteUrl', url);

    const { isValid, error } = validateWebsiteUrl(url);
    setErrorMessage(error);
  };

  // API calling function
  const callCrawlAPI = async (url: string) => {
    try {
      const response = await fetch('/api/fire-crawl/map', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: url,
          limit: 200,
          includeSubdomains: true,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error: any) {
      console.error('API call failed:', error);
      throw error;
    }
  };

  const handleFetchLinks = async () => {
    const url = form.getValues('websiteUrl') || '';

    setErrorMessage(null);

    const { isValid, error } = validateWebsiteUrl(url);
    if (!isValid) {
      setErrorMessage(error);
      return;
    }

    setIsFetching(true);

    try {
      // Call the real API
      const crawlData = await callCrawlAPI(url);
      
      if (crawlData.success) {
        // Handle case when no links are found
        if (!crawlData.links || crawlData.links.length === 0) {
          // Add the input URL itself as a fallback
          const inputUrl = url.trim();
          
          // Create a website entry with just the input URL
          const fallbackWebsite: CrawledWebsite = {
            domain: new URL(inputUrl).hostname,
            lastCrawled: crawlData.timestamp || new Date().toISOString(),
            linkCount: 1,
            links: [{ url: inputUrl }],
            isExpanded: true,
          };
          
          // Add to existing websites or create new list
          setCrawledWebsites([...crawledWebsites, fallbackWebsite]);
          setShowResults(true);
          
          toast.info(
            'No links were discovered on this page. The page URL has been added for manual selection.',
            { duration: 6000 }
          );
          return;
        }
        
        // Store existing crawled websites before processing new data
        const existingCrawledWebsites = [...crawledWebsites];
        
        // Group new links by domain (considering a domain and its subdomains as one)
        const newLinksByDomain: Record<string, CrawledLink[]> = {};
        
        // Helper function to get the base domain from a hostname
        const getBaseDomain = (hostname: string): string => {
          // Extract the main domain (e.g., 'buildthatidea.com' from 'app.buildthatidea.com')
          const parts = hostname.split('.');
          if (parts.length >= 2) {
            // For common domains like .com, .org, etc., we want the last two parts
            return parts.slice(-2).join('.');
          }
          return hostname; // Fallback to the full hostname
        };
        
        // Process new crawled links
        crawlData.links.forEach((link: string) => {
          try {
            const fullDomain = new URL(link).hostname;
            const baseDomain = getBaseDomain(fullDomain);
            
            if (!newLinksByDomain[baseDomain]) {
              newLinksByDomain[baseDomain] = [];
            }
            newLinksByDomain[baseDomain].push({ url: link });
          } catch (error) {
            console.error('Invalid URL:', link);
          }
        });
        
        // Merge with existing crawled websites
        const mergedWebsites = [...existingCrawledWebsites];
        
        // For each new domain from the crawl
        Object.keys(newLinksByDomain).forEach(newDomain => {
          const existingWebsiteIndex = mergedWebsites.findIndex(site => site.domain === newDomain);
          
          if (existingWebsiteIndex >= 0) {
            // Domain already exists - merge links, avoiding duplicates
            const existingUrls = mergedWebsites[existingWebsiteIndex].links.map(link => link.url);
            const newUniqueLinks = newLinksByDomain[newDomain].filter(link => !existingUrls.includes(link.url));
            
            // Update existing domain with merged links
            mergedWebsites[existingWebsiteIndex] = {
              ...mergedWebsites[existingWebsiteIndex],
              links: [...mergedWebsites[existingWebsiteIndex].links, ...newUniqueLinks],
              linkCount: mergedWebsites[existingWebsiteIndex].links.length + newUniqueLinks.length,
              lastCrawled: crawlData.timestamp, // Update timestamp for domains that got new links
            };
          } else {
            // New domain - add as new website
            mergedWebsites.push({
              domain: newDomain,
              lastCrawled: crawlData.timestamp,
              linkCount: newLinksByDomain[newDomain].length,
              links: newLinksByDomain[newDomain],
              isExpanded: true,
            });
          }
        });
        
        // Update state with merged websites
        setCrawledWebsites(mergedWebsites);
        setShowResults(true);
        
        // Calculate total new links added (excluding duplicates)
        let totalNewLinks = 0;
        Object.keys(newLinksByDomain).forEach(domain => {
          const existingWebsite = existingCrawledWebsites.find(site => site.domain === domain);
          if (existingWebsite) {
            const existingUrls = existingWebsite.links.map(link => link.url);
            const newUniqueLinks = newLinksByDomain[domain].filter(link => !existingUrls.includes(link.url));
            totalNewLinks += newUniqueLinks.length;
          } else {
            totalNewLinks += newLinksByDomain[domain].length;
          }
        });
        
        // Show appropriate success message
        if (existingCrawledWebsites.length > 0) {
          const totalLinks = mergedWebsites.reduce((sum, site) => sum + site.linkCount, 0);
          toast.success(`Successfully crawled ${totalNewLinks} new links (${totalLinks} total links)`);
        } else {
          toast.success(`Successfully crawled ${crawlData.totalLinks} links`);
        }
        
        // selectedLinks and form data are preserved automatically since we're only merging crawledWebsites
      } else {
        setErrorMessage(crawlData.error || 'Failed to crawl website');
        toast.error('Failed to crawl website');
      }
    } catch (error: any) {
      console.error('Crawl error:', error);
      setErrorMessage(error.message || 'An error occurred while crawling');
      toast.error('Failed to crawl website. Please try again.');
    } finally {
      setIsFetching(false);
    }
  };

  const handleAddManualUrls = () => {
    const urls = manualUrls.trim().split('\n').filter(url => url.trim());
    const validUrls: string[] = [];
    const invalidUrls: string[] = [];

    // Validate each URL
    urls.forEach(url => {
      const trimmedUrl = url.trim();
      const { isValid } = validateWebsiteUrl(trimmedUrl);
      if (isValid && trimmedUrl) {
        validUrls.push(trimmedUrl);
      } else {
        invalidUrls.push(trimmedUrl);
      }
    });

    if (invalidUrls.length > 0) {
      toast.error(`Invalid URLs found: ${invalidUrls.slice(0, 3).join(', ')}${invalidUrls.length > 3 ? '...' : ''}`);
      return;
    }

    if (validUrls.length === 0) {
      toast.error('No valid URLs found');
      return;
    }

    // Check 30-link limit
    if (selectedLinks.length + validUrls.length > 30) {
      const availableSlots = 30 - selectedLinks.length;
      if (availableSlots <= 0) {
        toast.error('You can only select up to 30 links in total');
        return;
      }
      toast.info(`Adding ${availableSlots} URLs. Limit of 30 reached.`);
      validUrls.splice(availableSlots);
    }

    // Group manual URLs by domain
    const linksByDomain: Record<string, CrawledLink[]> = {};
    
    const getBaseDomain = (hostname: string): string => {
      const parts = hostname.split('.');
      if (parts.length >= 2) {
        return parts.slice(-2).join('.');
      }
      return hostname;
    };

    validUrls.forEach(url => {
      try {
        const fullDomain = new URL(url).hostname;
        const baseDomain = getBaseDomain(fullDomain);
        
        if (!linksByDomain[baseDomain]) {
          linksByDomain[baseDomain] = [];
        }
        linksByDomain[baseDomain].push({ url });
      } catch (error) {
        console.error('Invalid URL:', url);
      }
    });

    // Add to existing crawled websites or create new ones
    const updatedWebsites = [...crawledWebsites];
    
    Object.keys(linksByDomain).forEach(domain => {
      const existingWebsiteIndex = updatedWebsites.findIndex(site => site.domain === domain);
      
      if (existingWebsiteIndex >= 0) {
        // Add to existing domain
        const existingUrls = updatedWebsites[existingWebsiteIndex].links.map(link => link.url);
        const newLinks = linksByDomain[domain].filter(link => !existingUrls.includes(link.url));
        
        updatedWebsites[existingWebsiteIndex] = {
          ...updatedWebsites[existingWebsiteIndex],
          links: [...updatedWebsites[existingWebsiteIndex].links, ...newLinks],
          linkCount: updatedWebsites[existingWebsiteIndex].links.length + newLinks.length
        };
      } else {
        // Create new domain
        updatedWebsites.push({
          domain,
          lastCrawled: new Date().toISOString(),
          linkCount: linksByDomain[domain].length,
          links: linksByDomain[domain],
          isExpanded: true,
        });
      }
    });

    setCrawledWebsites(updatedWebsites);
    setShowResults(true);
    
    // Add to selected links by calling toggleLinkSelection for each URL
    // This ensures identical behavior to selecting from the list
    for (const url of validUrls) {
      // Add to selectedLinks state first (toggleLinkSelection expects this check)
      if (!selectedLinks.includes(url)) {
        setSelectedLinks(prev => [...prev, url]);
        
        // Use the same form update logic as toggleLinkSelection
        const currentFormSelectedLinks = form.getValues('selectedLinks') || [];
        const websiteUrl = form.getValues('websiteUrl') || 'Manual Entry';
        
        // Get all currently selected links including this new one
        const allSelectedLinks = [...selectedLinks, url];
        
        const formattedData = [{
          website: websiteUrl,
          links: allSelectedLinks.map(linkUrl => {
            // Preserve existing status for active links, set 'selected' for new ones
            const existingLink = currentFormSelectedLinks
              .flatMap((ws: { links: any; }) => ws.links || [])
              .find((link: { url: string; }) => link.url === linkUrl);
            return {
              url: linkUrl,
              status: (existingLink?.status === 'active' ? 'active' : 'selected')
            };
          })
        }];
        form.setValue('selectedLinks', formattedData);
      }
    }

    // Clear manual input
    setManualUrls('');
    toast.success(`Added ${validUrls.length} URLs manually`);
  };

  const toggleWebsiteExpansion = (domain: string) => {
    setCrawledWebsites(
      crawledWebsites.map((site) =>
        site.domain === domain
          ? { ...site, isExpanded: !site.isExpanded }
          : site,
      ),
    );
  };

  const toggleLinkSelection = async (url: string) => {
    const currentFormSelectedLinks = form.getValues('selectedLinks') || [];
    
    // Find current status of this URL
    let currentStatus = 'selected';
    for (const websiteGroup of currentFormSelectedLinks) {
      const foundLink = websiteGroup.links?.find((link: { url: string; }) => link.url === url);
      if (foundLink) {
        currentStatus = foundLink.status;
        break;
      }
    }
    
    const isCurrentlySelected = selectedLinks.includes(url);
    const isCurrentlyChecked = isCurrentlySelected && 
                              currentStatus !== 'to-delete' && 
                              currentStatus !== 'removed';

    // Handle unchecking of active links
    if (isCurrentlyChecked && currentStatus === 'active') {
      setExcludeDialog({
        isOpen: true,
        linkUrl: url,
        domain: '', // Not needed for checkbox flow
      });
      return;
    }
    // Handle re-selection of to-delete or removed links
    if (!isCurrentlyChecked && (currentStatus === 'to-delete' || currentStatus === 'removed')) {
      const updatedFormData = currentFormSelectedLinks.map((websiteGroup: { links: any[]; }) => ({
        ...websiteGroup,
        links: websiteGroup.links.map((link: { url: string; status: string; }) =>
          link.url === url ? { ...link, status: 'selected' as const } : link
        )
      }));
      form.setValue('selectedLinks', updatedFormData);
      // Add to selectedLinks state if not already there
      if (!isCurrentlySelected) {
        setSelectedLinks([...selectedLinks, url]);
      }
      return;
    }
    // Handle normal selection/deselection
    if (!isCurrentlyChecked && selectedLinks.length >= 30) {
      toast.error('You can only select up to 30 links');
      return;
    }

    const newSelectedLinks = isCurrentlyChecked
      ? selectedLinks.filter((link) => link !== url)
      : [...selectedLinks, url];
    setSelectedLinks(newSelectedLinks);
    
    // Group by website and convert to new format
    const websiteUrl = form.getValues('websiteUrl') || '';
    if (!websiteUrl) {
      toast.error('Website URL is required');
      return;
    }
    
    // For deselection of selected (non-active) links, remove completely
    if (isCurrentlyChecked && currentStatus === 'selected') {
      // Remove completely from form data
      const updatedFormData = currentFormSelectedLinks.map((websiteGroup: { links: any[]; }) => ({
        ...websiteGroup,
        links: websiteGroup.links.filter(link => link.url !== url)
      })).filter((websiteGroup: { links: string | any[]; }) => websiteGroup.links.length > 0);
      form.setValue('selectedLinks', updatedFormData, { shouldDirty: true, shouldTouch: true });
    } else {
      // For new selections, use the original format
      const formattedData = [{
        website: websiteUrl,
        links: newSelectedLinks.map(linkUrl => {
          // Preserve existing status for active links, set 'selected' for new ones
          const existingLink = currentFormSelectedLinks
            .flatMap((ws: { links: any; }) => ws.links || [])
            .find((link: { url: string; }) => link.url === linkUrl);
          return {
            url: linkUrl,
            status: (existingLink?.status === 'active' ? 'active' : 'selected')
          };
        })
      }];
      form.setValue('selectedLinks', formattedData, { shouldDirty: true, shouldTouch: true });
    }
  };

  const selectAllLinks = (domain: string, select: boolean) => {
    // Find the website by domain
    const website = crawledWebsites.find((site) => site.domain === domain);
    if (!website) return;

    // Get all links for this website
    const websiteLinks = website.links.map((link) => link.url);

    let newSelectedLinks: string[] = [];

    if (select) {
      // If we're selecting, we need to respect the 30-link limit
      const currentlySelected = selectedLinks.filter(url => !websiteLinks.includes(url));
      const availableSlots = 30 - currentlySelected.length;
      
      if (availableSlots <= 0) {
        toast.error('You can only select up to 30 links in total');
        return;
      }
      
      // Take only as many links as we have slots available
      const linksToAdd = websiteLinks.slice(0, availableSlots);
      newSelectedLinks = [...currentlySelected, ...linksToAdd];
      
      if (linksToAdd.length < websiteLinks.length) {
        toast.info(`Selected ${linksToAdd.length} links. Limit of 30 reached.`);
      } else {
        toast.success(`Selected all links for ${domain}`);
      }
    } else {
      // Remove all website links from the selected links
      newSelectedLinks = selectedLinks.filter((url) => !websiteLinks.includes(url));
      toast.success(`Deselected all links for ${domain}`);
    }

    setSelectedLinks(newSelectedLinks);

    // Group by website and convert to new format
    const websiteUrl = form.getValues('websiteUrl') || '';
    if (!websiteUrl) {
      toast.error('Website URL is required');
      return;
    }
    
    const formattedData = [{
      website: websiteUrl,
      links: newSelectedLinks.map(linkUrl => ({
        url: linkUrl,
        status: 'selected' as const
      }))
    }];
    // Update form value
    form.setValue('selectedLinks', formattedData);
};

  const removeWebsite = (domain: string) => {
    // Remove the website from the crawled websites
    setCrawledWebsites(
      crawledWebsites.filter((site) => site.domain !== domain),
    );

    // Remove all links from this domain from the selected links
    const website = crawledWebsites.find((site) => site.domain === domain);
    if (website) {
      const websiteLinks = website.links.map((link) => link.url);
      const newSelectedLinks = selectedLinks.filter(
        (url) => !websiteLinks.includes(url),
      );
      setSelectedLinks(newSelectedLinks);
      form.setValue('selectedLinks', newSelectedLinks);
    }
  };

  const openExcludeDialog = (linkUrl: string, domain: string) => {
    const currentFormSelectedLinks = form.getValues('selectedLinks') || [];
    
    // Find current status of this URL
    let currentStatus = 'selected';
    for (const websiteGroup of currentFormSelectedLinks) {
      const foundLink = websiteGroup.links?.find((link: { url: string; }) => link.url === linkUrl);
      if (foundLink) {
        currentStatus = foundLink.status;
        break;
      }
    }
    
    // For active links, show dialog. For others, remove directly
    if (currentStatus === 'active') {
      setExcludeDialog({
        isOpen: true,
        linkUrl,
        domain,
      });
    } else {
      // Direct removal for non-active links
      excludeLinkDirect(linkUrl, domain);
    }
  };

  const closeExcludeDialog = () => {
    setExcludeDialog({
      isOpen: false,
      linkUrl: '',
      domain: '',
    });
  };

  // Handle select/deselect all filtered links
  const handleSelectAllFiltered = () => {
    const filteredWebsites = getFilteredAndSortedCrawledWebsites();
    const allFilteredLinks: string[] = [];
    filteredWebsites.forEach(site => {
      site.links.forEach(link => {
        allFilteredLinks.push(link.url);
      });
    });
    
    const allFilteredSelected = allFilteredLinks.length > 0 && allFilteredLinks.every(url => selectedLinks.includes(url));
    const isSelecting = !allFilteredSelected;
    
    if (isSelecting) {
      const nonFilteredSelectedLinks = selectedLinks.filter(url => !allFilteredLinks.includes(url));
      const availableSlots = 30 - nonFilteredSelectedLinks.length;
      
      if (availableSlots <= 0) {
        toast.error('You can only select up to 30 links in total');
        return;
      }
      
      const linksToAdd = allFilteredLinks.slice(0, availableSlots);
      const newSelectedLinks = [...nonFilteredSelectedLinks, ...linksToAdd];
      
      setSelectedLinks(newSelectedLinks);
      
      // Group by website and convert to new format
      const websiteUrl = form.getValues('websiteUrl') || '';
      if (!websiteUrl) {
        toast.error('Website URL is required');
        return;
      }
      
      const formattedData = [{
        website: websiteUrl,
        links: newSelectedLinks.map(linkUrl => ({
          url: linkUrl,
          status: 'selected' as const
        }))
      }];
      
      form.setValue('selectedLinks', formattedData);
      
      if (linksToAdd.length < allFilteredLinks.length) {
        toast.info(`Selected ${linksToAdd.length} of ${allFilteredLinks.length} filtered links. Limit of 30 reached.`);
      } else {
        toast.success(`Selected all ${allFilteredLinks.length} filtered links`);
      }
    } else {
      const newSelectedLinks = selectedLinks.filter(url => !allFilteredLinks.includes(url));
      setSelectedLinks(newSelectedLinks);
      
      // Group by website and convert to new format
      const websiteUrl = form.getValues('websiteUrl') || '';
      if (!websiteUrl) {
        toast.error('Website URL is required');
        return;
      }
      
      const formattedData = [{
        website: websiteUrl,
        links: newSelectedLinks.map(linkUrl => ({
          url: linkUrl,
          status: 'selected' as const
        }))
      }];
      form.setValue('selectedLinks', formattedData);
      toast.success(`Deselected all filtered links`);
    }
  };

  // Get select all button state
  const getSelectAllButtonState = () => {
    const filteredWebsites = getFilteredAndSortedCrawledWebsites();
    const allFilteredLinks: string[] = [];
    filteredWebsites.forEach(site => {
      site.links.forEach(link => {
        allFilteredLinks.push(link.url);
      });
    });
    
    const allFilteredSelected = allFilteredLinks.length > 0 && allFilteredLinks.every(url => selectedLinks.includes(url));
    
    return {
      isSelected: allFilteredSelected,
      isDisabled: allFilteredLinks.length === 0,
      buttonText: allFilteredSelected ? 'Deselect all' : (searchQuery ? 'Select all filtered' : 'Select all')
    };
  };

  const excludeLinkDirect = (linkUrl: string, domain: string) => {
    // Direct removal for non-active links
    const website = crawledWebsites.find((site) => site.domain === domain);
    if (!website) return;

    const updatedLinks = website.links.filter((link) => link.url !== linkUrl);
    const updatedWebsites = crawledWebsites.map((site) =>
      site.domain === domain
        ? { ...site, links: updatedLinks, linkCount: updatedLinks.length }
        : site,
    );

    setCrawledWebsites(updatedWebsites);

    if (selectedLinks.includes(linkUrl)) {
      const newSelectedLinks = selectedLinks.filter((url) => url !== linkUrl);
      setSelectedLinks(newSelectedLinks);
      
      const websiteUrl = form.getValues('websiteUrl') || '';
      const formattedData = [{
        website: websiteUrl,
        links: newSelectedLinks.map(url => ({ url, status: 'selected' as const }))
      }];
      form.setValue('selectedLinks', formattedData);
    }

    toast.success('Link removed');
  };

  const excludeLink = async () => {
    const { linkUrl, domain } = excludeDialog;
    setIsProcessingUrl(linkUrl);
    try {
      // Update status to 'to-delete' instead of making API call
      const currentFormSelectedLinks = form.getValues('selectedLinks') || [];
      const updatedFormData = currentFormSelectedLinks.map((websiteGroup: { links: any[]; }) => ({
        ...websiteGroup,
        links: websiteGroup.links.map(link => {
          if (link.url === linkUrl) {
            return { ...link, status: 'to-delete' as const };
          }
          return link;
        })
      }));

      // Use shouldDirty and shouldTouch to ensure form state updates
      form.setValue('selectedLinks', updatedFormData, { shouldDirty: true, shouldTouch: true });
      // Remove from selectedLinks state (uncheck the box)
      const newSelectedLinks = selectedLinks.filter((url) => url !== linkUrl);
      setSelectedLinks(newSelectedLinks);
      // Force component re-render to update checkbox state
      setForceRerender(prev => {
        const newValue = prev + 1;
        return newValue;
      });
      toast.success('Link marked for deletion');
    } catch (error) {
      console.error('❌ Error marking link for deletion:', error);
      toast.error('Failed to mark link for deletion');
    } finally {
      setIsProcessingUrl(null);
      closeExcludeDialog();
    }
  };

  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="websiteUrl"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Input
                {...field}
                value={field.value || ''}
                placeholder="https://example.com"
                className="w-full h-11 px-4 shadow-sm transition-all focus-visible:ring-1 focus-visible:ring-primary text-base"
                onChange={(e) => {
                  field.onChange(e);
                  handleUrlChange(e);
                }}
              />
            </FormControl>
            {errorMessage && (
              <div className="text-sm text-red-500 mt-1">{errorMessage}</div>
            )}
          </FormItem>
        )}
      />

      <div className="flex justify-end">
        <Button
          type="button"
          onClick={handleFetchLinks}
          disabled={isFetching || !!errorMessage || !form.watch('websiteUrl')?.trim()}
        >
          {isFetching ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Fetching...
            </>
          ) : (
            'Fetch links'
          )}
        </Button>
      </div>

      {showResults && (
        <div className="py-3 sm:py-4 bg-background">
          {/* Search Bar */}
          <div className="mb-3 sm:mb-4 px-3 sm:px-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search URLs, domains, or paths..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-10"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
            {searchQuery && (
              <div className="mt-2 text-sm text-muted-foreground">
                {(() => {
                  const { total, domains } = getFilteredResultsCount();
                  return `Found ${total} links across ${domains} ${domains === 1 ? 'domain' : 'domains'}`;
                })()}
              </div>
            )}
          </div>

          <div className="flex flex-col gap-3 mb-3 sm:mb-4 px-3 sm:px-4">
            <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0 sm:items-center">
              <div>
                <h3 className="text-lg font-semibold">Link sources</h3>
                {!isEditMode && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Select the links you want to include
                  </p>
                )}
              </div>
            </div>
            
            {/* Mobile: Stack vertically, Desktop: Keep inline */}
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4 sm:justify-end">
              {(() => {
                const { isSelected, isDisabled, buttonText } = getSelectAllButtonState();
                return (
                  <Button
                    type="button"
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    className="h-8 px-3 text-xs transition-all duration-200 w-full sm:w-auto"
                    onClick={handleSelectAllFiltered}
                    disabled={isDisabled}
                  >
                    {buttonText}
                  </Button>
                );
              })()}
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                <span className="text-sm text-muted-foreground">
                  Sort by:
                </span>
                <select 
                  className="border border-border rounded-md bg-background px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 w-full sm:w-auto"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <option value="url-depth-asc">URL Depth (Shallow)</option>
                  <option value="default">Domain Grouping</option>
                  <option value="url-length-asc">URL Length (Shortest)</option>
                  <option value="url-length-desc">URL Length (Longest)</option>
                  <option value="url-depth-desc">URL Depth (Deep)</option>
                </select>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {/* Show message when no links were discovered */}
            {crawledWebsites.length > 0 && crawledWebsites.every(site => site.linkCount === 1 && site.links[0]?.url === form.getValues('websiteUrl')) && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                      No links discovered automatically
                    </h3>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      The website may be blocking automated crawling or have no discoverable links. 
                      The page URL has been added below, and you can manually add more URLs in the section at the bottom.
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            {getFilteredAndSortedCrawledWebsites().map((website) => (
              <div key={website.domain} className="border border-border rounded-md mb-4 bg-background">
                <div
                  className="flex items-center justify-between p-2 sm:p-3 cursor-pointer hover:bg-muted/50"
                  onClick={() => toggleWebsiteExpansion(website.domain)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      toggleWebsiteExpansion(website.domain);
                    }
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="rounded-full bg-muted p-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="font-medium">{website.domain}</div>
                      <div className="text-xs text-muted-foreground">
                        (includes all subdomains)
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      className="p-1 hover:bg-muted rounded-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeWebsite(website.domain);
                      }}
                    >
                      <X className="h-4 w-4 text-muted-foreground" />
                    </button>
                    {website.isExpanded ? (
                      <ChevronDown className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                </div>

                {website.isExpanded && (
                  <div className="px-2 sm:px-3 pb-2 sm:pb-3">
                    <div className="bg-muted/30 p-2 rounded-md border border-border">
                      <div className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0 sm:items-center text-sm font-medium mb-2">
                        <div className="flex items-center gap-2">
                          <span>{website.linkCount} LINKS INCLUDED</span>
                          <button
                            type="button"
                            className="text-xs bg-muted hover:bg-muted/80 px-2 py-0.5 rounded transition-colors"
                            onClick={() => selectAllLinks(website.domain, selectedLinks.length < 30)}
                          >
                            {selectedLinks.length >= 30 ? 'Deselect All' : 'Select Up to 30'}
                          </button>
                        </div>
                        <span className={`text-xs ${selectedLinks.length >= 30 ? 'text-red-600 font-bold' : 'text-amber-600'}`}>
                          ({selectedLinks.length}/30 links selected)
                          {selectedLinks.length >= 30 && ' - Limit reached!'}
                        </span>
                      </div>
                      <div className="space-y-2 max-h-[300px] overflow-y-auto overflow-x-hidden pr-2 scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
                        {website.links.map((link) => {
                          // Get fresh form data every render to ensure UI stays in sync
                          const currentFormSelectedLinks = form.getValues('selectedLinks') || [];
                          let linkStatus = 'selected';
                          
                          // Find status for this link in current form data
                          for (const websiteGroup of currentFormSelectedLinks) {
                            const foundLink = websiteGroup.links?.find((l: { url: string; }) => l.url === link.url);
                            if (foundLink) {
                              linkStatus = foundLink.status;
                              break;
                            }
                          }
                          
                          // Determine if checkbox should be checked
                          // Checked if: in selectedLinks AND status is not 'to-delete' or 'removed'
                          const isInSelectedLinks = selectedLinks.includes(link.url);
                          const isNotMarkedForDeletion = linkStatus !== 'to-delete' && linkStatus !== 'removed';
                          const isChecked = isInSelectedLinks && isNotMarkedForDeletion;
                          return (
                          <div
                            key={`${link.url}-${forceRerender}`} // Include forceRerender in key to force re-render
                            className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-md transition-colors"
                          >
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                className="rounded border-border"
                                checked={isChecked}
                                onChange={() => toggleLinkSelection(link.url)}
                                disabled={isProcessingUrl === link.url}
                              />
                              {isProcessingUrl === link.url && (
                                <Loader2 className="h-4 w-4 animate-spin text-orange-500" />
                              )}
                              <div className="text-sm truncate max-w-[200px] sm:max-w-[300px] md:max-w-md text-foreground">
                                {link.url}
                              </div>
                              {linkStatus === 'active' && (
                                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full font-medium">
                                  Active
                                </span>
                              )}
                              {linkStatus === 'to-delete' && (
                                <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-medium">
                                  Marked for Deletion
                                </span>
                              )}
                            </div>
                          </div>
                        )})}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Manual URL Entry */}
          <div className="border border-border rounded-lg p-3 sm:p-4 bg-background mt-4">
            <h3 className="text-sm font-medium text-foreground mb-2">Or add URLs manually</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Enter URLs one per line. This is especially useful when:
              <br />• No links were discovered automatically
              <br />• You want to include specific pages not found by crawling
              <br />• The website blocks automated discovery
            </p>
            {crawledWebsites.length > 0 && crawledWebsites.every(site => site.linkCount === 1 && site.links[0]?.url === form.getValues('websiteUrl')) && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3 mb-3">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  💡 <strong>Tip:</strong> Since no links were automatically discovered, consider adding related pages manually here.
                </p>
              </div>
            )}
            <div className="space-y-3">
              <textarea
                placeholder="https://example.com/page1&#10;https://example.com/page2&#10;https://example.com/page3"
                value={manualUrls}
                onChange={(e) => setManualUrls(e.target.value)}
                className="w-full min-h-[80px] px-3 py-2 text-sm border border-border rounded-md bg-background resize-y focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              />
              <div className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0 sm:items-center">
                <span className="text-sm text-muted-foreground">
                  {manualUrls.trim() ? manualUrls.trim().split('\n').filter(url => url.trim()).length : 0} URLs entered
                </span>
                <Button
                  type="button"
                  size="sm"
                  onClick={handleAddManualUrls}
                  disabled={!manualUrls.trim()}
                >
                  Add URLs
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Exclude Link Dialog - Only render in edit mode */}
      {(() => {
        return isEditMode ? (
          <Dialog
            open={excludeDialog.isOpen}
            onOpenChange={(open) => {
              if (!open) closeExcludeDialog();
            }}
          >
          <DialogContent className="sm:max-w-md border-orange-200 dark:border-orange-800">
            <DialogHeader>
              <DialogTitle className="text-orange-900 dark:text-orange-100">Mark Active Link for Deletion</DialogTitle>
              <DialogDescription className="text-orange-700 dark:text-orange-300">
                This link is currently active in your vector index. Marking it for deletion will queue it for removal from your agent's knowledge base when you save your changes.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="text-sm font-medium break-all bg-orange-50 dark:bg-orange-900/20 p-3 rounded-md border border-orange-200 dark:border-orange-800 text-foreground">
                {excludeDialog.linkUrl}
              </div>
            </div>
            <DialogFooter className="flex space-x-4 sm:space-x-4 sm:justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  closeExcludeDialog();
                }}
                className="border-orange-300 text-orange-700 hover:bg-orange-50 dark:border-orange-700 dark:text-orange-300 dark:hover:bg-orange-900/20"
              >
                Cancel
              </Button>
              <Button 
                type="button" 
                onClick={() => {
                  excludeLink();
                }}
                disabled={isProcessingUrl === excludeDialog.linkUrl}
                className="bg-orange-600 hover:bg-orange-700 text-white dark:bg-orange-600 dark:hover:bg-orange-700"
              >
                {isProcessingUrl === excludeDialog.linkUrl ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Marking...
                  </>
                ) : (
                  'Mark for Deletion'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        ) : (
          <div></div>
        );
      })()}
    </div>
  );
}
