import { notFound, redirect } from 'next/navigation';
import { db } from '@/lib/db/client';
import { agents, chat as chatTable } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { generateAgentMetadata } from '@/lib/metadata';
import type { Message as UIMessage, Attachment } from 'ai';
import { getMessagesByChatId, getChatById } from '@/lib/db/queries';
import { auth } from '@/auth';
import logger from '@/lib/logger';
import type { VisibilityType } from '@/components/visibility-selector';
import { AgentChatClient } from './client';

// Force dynamic rendering to avoid connection issues during build time
export const dynamic = 'force-dynamic';
export const dynamicParams = true;

// Define ExtendedUIMessage interface to match what's expected in the Chat component
interface ExtendedUIMessage {
  warning?: {
    message: string;
    remainingMessages: number;
  };
  parts: NonNullable<UIMessage['parts']>; // Ensure parts is non-nullable
}

// Define the correct type for Next.js page props
type PageProps = {
  params: Promise<{
    slug: string;
    id: string;
  }>;
};

// Generate metadata for the page
export const generateMetadata = async ({ params }: PageProps) => {
  const { slug, id } = await params;
  return generateAgentMetadata(slug, id);
};

export default async function AgentChatPage({ params }: PageProps) {
  // Await params since it's a Promise in Next.js 15+
  const { slug, id: chatId } = await params;
  logger.debug(`Loading page for slug: ${slug}, chatId: ${chatId}`);

  const [agent] = await db
    .select()
    .from(agents)
    .where(and(eq(agents.slug, slug), eq(agents.status, 'active')));
  logger.debug(`Found agent: ${agent ? agent.id : 'Not found'}`);

  if (!agent) {
    logger.info(`Agent not found for slug: ${slug}, returning 404`);
    return notFound();
  }

  // Check if the agent is a draft or inactive - if so, don't allow access
  if (agent.status === 'draft' || agent.status === 'inactive') {
    logger.info(
      `Agent ${agent.id} is in ${agent.status} status, returning 404`,
    );
    return notFound();
  }

  const session = await auth();

  // Check if the chat exists and if the user owns it
  const chatData = await getChatById({ id: chatId });

  if (chatData) {
    // Additional chat checks can go here if needed
  }

  // Get the chat's visibility setting from the database
  let visibilityType: VisibilityType = 'private'; // Default to private

  // First, determine if the user is logged in
  const isLoggedIn = !!session?.user?.id;

  // Default to allowing edits
  let isReadonly = false;

  if (!isLoggedIn) {
    // Always readonly for logged-out users
    isReadonly = true;
  } else if (chatData && session?.user) {
    // For existing chats, check visibility and ownership
    const isOwner = session.user.id === chatData.userId;
    const isPrivate = chatData.visibility === 'private';

    // If it's a private chat and user is not the owner, redirect to agent page
    if (isPrivate && !isOwner) {
      logger.warn('Unauthorized access attempt to private chat', {
        chatId,
        userId: session.user.id,
        chatOwnerId: chatData.userId,
        visibility: chatData.visibility
      });
      
      // Use Next.js redirect instead of Response.redirect
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';
      const redirectUrl = `${baseUrl}/agent/${slug}/`;
      
      // Use Next.js redirect
      redirect(redirectUrl);
    }
  }

  // Pass the client-side auth state to the client component
  const clientAuthState = {
    isLoggedIn,
    userId: session?.user?.id,
  };

  // Fetch initial messages
  let initialMessages: Array<ExtendedUIMessage> = [];
  try {
    // Check if we have fetched chat data already
    const existingChatData = await getChatById({ id: chatId as string });

    if (existingChatData) {
      // If we already fetched the chat above, use its visibility
      visibilityType =
        (existingChatData.visibility as VisibilityType) || 'private';
      logger.debug(`Using visibility from existing chat: ${visibilityType}`);
    } else {
      // Otherwise try to look up the chat's visibility directly
      try {
        const chatQuery = await db
          .select()
          .from(chatTable)
          .where(eq(chatTable.id, chatId as string));

        if (chatQuery.length > 0) {
          const chatData = chatQuery[0];
          visibilityType = (chatData.visibility as VisibilityType) || 'private';
          logger.debug(`Found chat visibility in database: ${visibilityType}`);
        }
      } catch (err) {
        logger.error('Error querying chat visibility:', err);
      }
    }

    // Fetch messages for the chat
    const messagesFromDb = await getMessagesByChatId({ id: chatId as string });

    // Convert DB messages to UI messages
    initialMessages = messagesFromDb.map((message) => {
      // Ensure parts is always an array, never undefined
      const messageParts = (message.parts as UIMessage['parts']) || [];

      return {
        id: message.id,
        parts: messageParts,
        role: message.role as UIMessage['role'],
        content: '',
        createdAt: message.createdAt,
        experimental_attachments:
          (message.attachments as Array<Attachment>) ?? [],
      } as ExtendedUIMessage;
    });
  } catch (error) {
    logger.error('Error loading chat messages', { error, chatId });
    // If there's an error, we'll just start with an empty chat
    initialMessages = [];
  }

  // Get model from agent or cookie or default
  const modelToUse = agent.model || DEFAULT_CHAT_MODEL;

  // Fetch bootstrap data (server-side) to pass to client
  // This prevents client from needing to make this call
  logger.info(
    `[Server] Starting bootstrap data fetch for agent ${agent.id} and chat ${chatId}`,
  );
  console.time('[Server] Bootstrap data fetch time');

  let bootstrapData = null;
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const url = `${baseUrl}/api/agents/${agent.id}/bootstrap?chatId=${chatId}`;

    // Use server-side fetch without headers for simplicity
    const bootstrapResponse = await fetch(url, {
      // The fetch will automatically include cookies in same-origin requests
      cache: 'no-store', // Ensure we get fresh data
    });

    if (bootstrapResponse.ok) {
      bootstrapData = await bootstrapResponse.json();
      console.timeEnd('[Server] Bootstrap data fetch time');
      logger.info('[Server] Bootstrap data fetched successfully', {
        agentId: agent.id,
        chatId,
        hasProfile: !!bootstrapData?.profile,
        hasSubscription: !!bootstrapData?.subscription,
        votesCount: bootstrapData?.votes?.length || 0,
      });
    }
  } catch (error) {
    console.timeEnd('[Server] Bootstrap data fetch time');
    logger.error('Error fetching bootstrap data:', error);
  }

  // Create client component with model selection handling
  return (
    <AgentChatClient
      chatId={chatId}
      initialMessages={initialMessages}
      modelId={modelToUse}
      visibilityType={visibilityType}
      isReadonly={isReadonly}
      agent={agent}
      bootstrapData={bootstrapData}
      clientAuthState={clientAuthState}
    />
  );
}
