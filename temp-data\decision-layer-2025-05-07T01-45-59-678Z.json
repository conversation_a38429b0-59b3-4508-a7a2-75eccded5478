"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { useForm, FormProvider } from "react-hook-form";
import { Card, CardContent } from "@/components/ui/card";
import { AgentBasicInfoForm } from "@/components/agent-form/agent-basic-info-form";
import { AgentAppearanceForm } from "@/components/agent-form/agent-appearance-form";
import { AgentMonetizationForm } from "@/components/agent-form/agent-monetization-form";
import { Skeleton } from "@/components/ui/skeleton";
import { FloatingActionMenu } from "@/components/agent-form/floating-action-menu";
import { CheckCircle, Zap, Info } from "lucide-react";
import Link from "next/link";

// Define FileEntry interface for uploaded files
interface FileEntry {
  id: string;
  name: string;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  file?: File;
  url?: string;
  draftAgentId?: string;
  size?: number;
  progress?: number;
}

// Define the form values type
interface FormValues {
  agentName: string;
  slug: string;
  instruction: string;
  description: string;
  model: string;
  isPublic: boolean;
  isPaid: boolean;
  pricingType: "subscription" | "lifetime";
  price?: string;
  accessLevel: "free" | "subscription" | "lifetime";
  visibility: "public" | "private";
  files: any[];
}

// Simple debounce function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout | null = null;

  const debounced = (...args: any[]) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      timeout = null;
      func(...args);
    }, wait);
  };

  const cancel = () => {
    if (timeout) clearTimeout(timeout);
    timeout = null;
  };

  debounced.cancel = cancel;

  return debounced;
};

export default function CreatePage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingDraft, setIsSavingDraft] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [quickMessages, setQuickMessages] = useState<string[]>([]);
  const [slugExists, setSlugExists] = useState(false);
  const [draftAgentId, setDraftAgentId] = useState<string | null>(null);
  const [lastDraftId, setLastDraftId] = useState<string | null>(null);
  const [hasDraft, setHasDraft] = useState<boolean>(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoS3Url, setLogoS3Url] = useState<string | null>(null);
  const [planLimits, setPlanLimits] = useState<{
    canCreate: boolean;
    message: string;
    agentsLeft: number;
    totalAgents: number;
    currentAgentCount: number;
    plan?: any;
  } | null>(null);
  const [isPlanCheckLoading, setIsPlanCheckLoading] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Set document title
  useEffect(() => {
    document.title = 'Create Agent | BuildThatIdea';

    // Clear any previous draft data when creating a new agent
    localStorage.removeItem('draftAgentId');

    // Reset form to default values
    form.reset({
      agentName: "",
      slug: "",
      instruction: "",
      description: "",
      model: "gpt-3.5-turbo",
      isPublic: false,
      isPaid: false,
      pricingType: "subscription",
      price: "",
      accessLevel: "free",
      visibility: "private",
      files: [],
    });

    // Clear other state
    setQuickMessages([]);
    setLogoPreview(null);
    setLogoFile(null);
    setLogoS3Url(null);
    setDraftAgentId(null);
  }, []);

  // Form setup with manual validation
  const form = useForm<FormValues>({
    defaultValues: {
      agentName: "",
      slug: "",
      instruction: "",
      description: "",
      model: "gpt-3.5-turbo",
      isPublic: false,
      isPaid: false,
      pricingType: "subscription",
      price: "",
      accessLevel: "free",
      visibility: "private",
      files: [],
    }
  });

  // Register form validation
  useEffect(() => {
    // Register custom validation
    form.register("agentName", { 
      required: "Agent name is required",
      minLength: {
        value: 4,
        message: "Agent name must be at least 4 characters"
      }
    });
    
    form.register("slug", { 
      required: "URL path is required",
      minLength: {
        value: 4,
        message: "URL path must be at least 4 characters"
      }
    });
    
    form.register("description", { 
      required: "Description is required",
      minLength: {
        value: 10,
        message: "Description must be at least 10 characters"
      }
    });
    
    form.register("instruction", { 
      required: "Instructions are required",
      minLength: {
        value: 10,
        message: "Instructions must be at least 10 characters"
      }
    });
  }, [form]);

  // If user is not authenticated, redirect to the verification page
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/verify");
    }
  }, [router, status]);

  // Function to check if a slug is available with debounce
  const checkSlugAvailability = useCallback(
    debounce(async (slug: string) => {
      try {
        if (!session?.user?.email || !slug) {
          setSlugExists(false);
          setIsCheckingSlug(false);
          return true; // Can't check without user email or slug
        }

        setIsCheckingSlug(true);
        const response = await fetch(`/api/agents/check-slug?slug=${slug}&userEmail=${session.user.email}`);
        const data = await response.json();

        // If the slug exists but belongs to the current user's draft, it's still available for use
        const isAvailable = !data.exists || data.isOwnDraft;

        // Only set slugExists to true if it's not the user's own draft
        setSlugExists(!isAvailable);
        setIsCheckingSlug(false);

        return isAvailable;
      } catch (error) {
        console.error("Error checking slug availability:", error);
        setSlugExists(false);
        setIsCheckingSlug(false);
        return true; // Assume it's available if there's an error
      }
    }, 500), // 500ms debounce time for slug validation
    [session]
  );

  // Function to generate a slug from a name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
  };

  // Function to upload logo to S3
  const uploadLogo = async (file: File): Promise<string | undefined> => {
    try {
      const formData = new FormData();
      formData.append('logo', file);  // Changed from 'file' to 'logo'

      const response = await fetch('/api/agents/upload-logo', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload logo');
      }

      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast.error('Failed to upload logo');
      return undefined;
    }
  };



  // Function to create or update draft agent
  const createOrUpdateDraftAgent = useCallback(async () => {
    if (!session?.user?.email) return;

    // Check if required fields are filled
    const formValues = form.getValues();
    if (!formValues.agentName || !formValues.slug || formValues.agentName.length < 4 || formValues.slug.length < 1) {
      toast.error("Agent name and URL path are required");
      return;
    }

    setIsSavingDraft(true);

    try {
      // Prepare data for API
      const data = {
        ...formValues,
        id: draftAgentId || undefined, // Use undefined instead of null
        quickMessages,
        isDraft: true,
        // Include the logo URL if we have one from a previous upload
        logo: logoS3Url || undefined
      };

      // We no longer handle logo upload here since it's done separately
      // Just save the agent data with the logo URL if available
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to save draft agent';
        try {
          const errorData = await response.json();
          console.error("API Error:", errorData);
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error("Error parsing API error response:", parseError);
          console.error("Response status:", response.status, response.statusText);
        }
        throw new Error(errorMessage);
      }

      const responseData = await response.json();

      // If this is a new agent, store the ID
      if (!draftAgentId && responseData.agent?.id) {
        setDraftAgentId(responseData.agent.id);
        localStorage.setItem('draftAgentId', responseData.agent.id);
      }

      setLastSaved(new Date());
      toast.success("Draft saved successfully");
      return responseData.agent;
    } catch (error) {
      console.error('Error creating/updating draft agent:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save draft');
      return null;
    } finally {
      setIsSavingDraft(false);
    }
  }, [session?.user?.email, draftAgentId, form, logoFile, logoS3Url, quickMessages, setDraftAgentId, setLastSaved, setLogoFile, setIsSavingDraft]);

  // Custom handler for logo uploads to prevent page reloads
  const handleLogoChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent default form submission behavior
    e.preventDefault();

    const file = e.target.files?.[0];
    if (!file) return;

    // Create a preview immediately for better UX
    const reader = new FileReader();
    reader.onloadend = () => {
      setLogoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Show a toast to inform the user that logo is uploading
    toast.info("Uploading logo...");

    try {
      // Upload the logo to S3 but don't update the database yet
      const logoUrl = await uploadLogo(file);

      if (logoUrl) {
        // Store the S3 URL in state to be used when saving the draft
        setLogoS3Url(logoUrl);
        toast.success("Logo uploaded successfully");
      }
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast.error('Failed to upload logo');
    }
  }, [setLogoPreview, setLogoS3Url]);

  // Function to handle final form submission
  const handleSubmit = async (data: FormValues) => {
    if (!session?.user?.email) return;

    setIsSubmitting(true);

    try {
      // Save as draft first to ensure we have an agent ID
      const draftAgent = await createOrUpdateDraftAgent();

      if (!draftAgent) {
        throw new Error('Failed to create draft agent');
      }

      // Verify user has an active plan before proceeding
      if (!planLimits?.canCreate) {
        toast.error(
          <div className="flex flex-col gap-4">
            <p>You need an active plan to launch this agent.</p>
            <Button
              onClick={() => initiateFreeTrial()}
              className="w-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white"
            >
              Get Free Trial
            </Button>
          </div>,
          {
            duration: 10000,
          }
        );
        setIsSubmitting(false);
        return;
      }

      // Prepare data for final submission
      const finalData = {
        ...data,
        id: draftAgent.id,
        quickMessages,
        isDraft: false
      };

      // Submit the agent for creation
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(finalData),
      });

      if (!response.ok) {
        const errorData = await response.json();

        if (response.status === 403) {
          toast.error(
            <div className="flex flex-col gap-4">
              <p>{errorData.message}</p>
              <Button
                onClick={() => router.push('/billing')}
                className="w-full"
                variant="default"
              >
                Upgrade Subscription
              </Button>
            </div>,
            {
              duration: 10000,
            }
          );
          setIsSubmitting(false);
          return;
        }

        throw new Error(errorData.message || 'Failed to create agent');
      }

      const responseData = await response.json();

      // Store the created agent data for the success page
      localStorage.setItem('createdAgentData', JSON.stringify({
        id: responseData.agent.id,
        name: responseData.agent.name,
        slug: responseData.agent.slug
      }));

      // Clear the draft agent ID
      localStorage.removeItem('draftAgentId');

      // Initiate embeddings before redirecting if there are files
      if (data.files && data.files.length > 0) {
        try {
          console.log('Initiating embeddings for agent:', responseData.agent.id);

          // Call the initiate-embeddings API
          const embeddingsResponse = await fetch('/api/agents/initiate-embeddings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agentId: responseData.agent.id,
            }),
          });

          if (!embeddingsResponse.ok) {
            console.error('Failed to initiate embeddings:', await embeddingsResponse.text());
          } else {
            console.log('Embeddings initiated successfully');
          }

          // Call the intiate-decision API right after embeddings
          console.log('Initiating decision layer for agent:', responseData.agent.id);
          const decisionResponse = await fetch('/api/agents/intiate-decision', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agentId: responseData.agent.id,
            }),
          });

          if (!decisionResponse.ok) {
            console.error('Failed to initiate decision layer:', await decisionResponse.text());
          } else {
            console.log('Decision layer processing initiated successfully');
          }
        } catch (error) {
          console.error('Error in embedding/decision layer process:', error);
          // Continue with redirect even if embeddings or decision layer fail
        }
      }

      // Redirect to success page
      router.push(`/create/success?agent=${responseData.agent.slug}`);
    } catch (error) {
      console.error('Error creating agent:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create agent');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load draft agent on component mount
  const loadDraftAgent = useCallback(async () => {
    setIsLoading(true);
    // On the create page, we want to check if there's a draft but not load it automatically
    if (!session?.user?.email) {
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/agents?draftId=true');

      if (!response.ok) {
        // For new users, there won't be a draft agent yet, so this is expected
        if (response.status === 404) {
          console.log('No draft agent found for new user - this is normal');
          setIsLoading(false);
          return;
        }

        console.error(`Error loading draft agent: ${response.status} ${response.statusText}`);
        setIsLoading(false);
        return;
      }

      const data = await response.json();

      if (data.agent) {
        // Store the draft ID but don't load it automatically
        setLastDraftId(data.agent.id);
        setHasDraft(true);
        console.log('Found a draft agent:', data.agent.id);
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Error checking for draft agent:', error);
      setIsLoading(false);
    }
  }, [session?.user?.email, setIsLoading, setLastDraftId, setHasDraft]);

  // Load a specific draft agent
  const loadSpecificDraft = async (draftId: string) => {
    setIsLoading(true);
    if (!session?.user?.email) {
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/agents?id=${draftId}`);

      if (!response.ok) {
        console.error(`Error loading specific draft agent: ${response.status} ${response.statusText}`);
        setIsLoading(false);
        return;
      }

      const data = await response.json();

      if (data.agent) {
        // Set the draft agent ID in local storage
        localStorage.setItem('draftAgentId', data.agent.id);
        setDraftAgentId(data.agent.id);

        // Debug log to see what's coming from the API
        console.log('Loaded draft agent data:', {
          id: data.agent?.id,
          hasFiles: !!data.agent?.files,
          filesType: data.agent?.files ? typeof data.agent.files : 'undefined',
          filesData: data.agent?.files,
        });

        // Process files from the database format to the FileEntry format
        let processedFiles: FileEntry[] = [];
        if (data.agent.files) {
          try {
            // Parse files if they're a string, otherwise use as is
            const filesData =
              typeof data.agent.files === 'string'
                ? JSON.parse(data.agent.files)
                : data.agent.files;

            // Convert each file to the FileEntry format
            processedFiles = Array.isArray(filesData)
              ? filesData.map((file) => ({
                  id: crypto.randomUUID(),
                  name: file.file_name || file.name || 'Unknown file',
                  status: 'completed',
                  url: file.url,
                  size: file.file_size ? parseInt(file.file_size) : 0, // Use file_size if available
                  progress: 100,
                }))
              : [];
          } catch (error) {
            console.error('Error processing files from draft agent:', error);
          }
        }

        // Set form values from the draft agent data
        form.reset({
          agentName: data.agent.agentName || '',
          slug: data.agent.slug || '',
          description: data.agent.description || '',
          instruction: data.agent.instruction || '',
          model: data.agent.model || 'gpt-3.5-turbo',
          isPublic: data.agent.visibility === 'public',
          isPaid: data.agent.accessLevel !== 'free',
          pricingType: 'subscription',
          price: data.agent.price ? String(data.agent.price) : '',
          accessLevel: data.agent.accessLevel === 'lifetime' ? 'subscription' : data.agent.accessLevel || 'free',
          visibility: data.agent.visibility || 'private',
          files: processedFiles,
        });

        // Set quick messages if available
        if (data.agent.quickMessages) {
          setQuickMessages(Array.isArray(data.agent.quickMessages) ? data.agent.quickMessages : []);
        }

        // Set logo preview and S3 URL if available
        if (data.agent.logo) {
          setLogoPreview(data.agent.logo);
          setLogoS3Url(data.agent.logo);
        }

        // Hide the draft notification
        setHasDraft(false);
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading specific draft agent:', error);
      setIsLoading(false);
    }
  };

  // Check plan limits
  const checkPlanLimits = useCallback(async () => {
    if (!session?.user?.email) return;

    setIsPlanCheckLoading(true);

    try {
      const response = await fetch('/api/agents/check-plan-limits');

      if (!response.ok) {
        throw new Error('Failed to check plan limits');
      }

      const data = await response.json();
      setPlanLimits(data);
    } catch (error) {
      console.error('Error checking plan limits:', error);
    } finally {
      setIsPlanCheckLoading(false);
    }
  }, [session?.user?.email, setIsPlanCheckLoading, setPlanLimits]);

  // Auto-save draft with debounce
  const debouncedSaveDraft = useCallback(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        if (form.formState.isDirty && !isSubmitting && !isSavingDraft) {
          createOrUpdateDraftAgent();
        }
      }, 30000); // Auto-save after 30 seconds of inactivity
    };
  }, [form.formState.isDirty, isSubmitting, isSavingDraft, createOrUpdateDraftAgent]);

  // Watch form changes for auto-save
  useEffect(() => {
    // Create a debounced function that only triggers once every 5 seconds
    const debouncedWatch = debounce(() => {
      debouncedSaveDraft()();
    }, 5000);

    // Subscribe to form changes
    const subscription = form.watch(() => {
      debouncedWatch();
    });

    // Cleanup
    return () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
      debouncedWatch.cancel();
    };
  }, [form, debouncedSaveDraft]);

  // Watch for agent name changes to generate slug
  const agentName = form.watch("agentName");
  
  useEffect(() => {
    // Skip if we're already submitting or saving
    if (isSubmitting || isSavingDraft) return;
    
    // If name is empty, clear the slug
    if (!agentName || agentName.trim() === '') {
      form.setValue("slug", "", { shouldDirty: true, shouldValidate: false });
      return;
    }
    
    // Generate a new slug from the current name
    const newSlug = generateSlug(agentName);
    const currentSlug = form.getValues("slug");
    
    // Check if the slug was manually edited
    const wasAutoGenerated = !currentSlug || 
                            currentSlug === generateSlug(agentName.slice(0, -1)) ||
                            currentSlug === generateSlug(agentName);
    
    if (wasAutoGenerated) {
      // Set the new slug value with options that prevent re-renders
      form.setValue("slug", newSlug, { 
        shouldDirty: true,
        shouldTouch: false,
        shouldValidate: false
      });
      
      // Only check availability if the slug is different from the current one
      if (newSlug !== currentSlug) {
        checkSlugAvailability(newSlug);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [agentName, isSubmitting, isSavingDraft]);

  // Load draft agent and check plan limits on component mount
  useEffect(() => {
    if (session?.user?.email) {
      loadDraftAgent();
      checkPlanLimits();
    }
  }, [session?.user?.email, loadDraftAgent, checkPlanLimits]);

  // Expose the saveDraftAgent function to the window object
  useEffect(() => {
    // This ensures the window object always has the latest version of the function
    (window as any).saveDraftAgent = createOrUpdateDraftAgent;

    // Clean up when component unmounts
    return () => {
      (window as any).saveDraftAgent = undefined;
    };
  }, [createOrUpdateDraftAgent]);

  // Format last saved time
  const formatLastSaved = () => {
    if (!lastSaved) return null;

    const now = new Date();
    const diff = now.getTime() - lastSaved.getTime();

    // Less than a minute
    if (diff < 60000) {
      return 'Just now';
    }

    // Less than an hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    }

    // Format as time
    return lastSaved.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Check if form has required fields filled and user has a valid plan
  const isFormValid = () => {
    const values = form.getValues();
    const hasRequiredFields = values.agentName?.length >= 4 && values.slug?.length >= 4;
    
    // Check if user has a valid plan to create agents
    // This will be false for new users without a plan or users who have reached their limit
    const hasPlanToCreate = planLimits?.canCreate || false;
    
    return hasRequiredFields && hasPlanToCreate;
  };

  // Function to initiate the free trial checkout
  const initiateFreeTrial = async () => {
    try {
      // Get the current form values to pass the agent ID if available
      const formValues = form.getValues();
      const draftId = draftAgentId || null;
      
      const response = await fetch('/api/agents/free-trial-hobby', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId: draftId,
          agentSlug: formValues.slug || 'new-agent'
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create free trial checkout');
      }
      
      const data = await response.json();
      
      // Redirect to Stripe checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        toast.error('Unable to start free trial. Please try again.');
      }
      
    } catch (error) {
      console.error('Error initiating free trial:', error);
      toast.error('Failed to start free trial. Please try again.');
    }
  };

  // Check for trial success parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const trialStatus = params.get('trial');
    
    if (trialStatus === 'success') {
      toast.success(
        <div className="flex flex-col gap-2">
          <p className="font-medium">Free trial activated!</p>
          <p className="text-sm">Your 2-week free trial of the Hobby Plan has been activated. You can now create and launch your agent.</p>
        </div>,
        { duration: 6000 }
      );
      
      // Remove the trial parameter from URL to prevent showing the toast again on refresh
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
      
      // Refresh plan limits to update the UI
      checkPlanLimits();
    } else if (trialStatus === 'canceled') {
      toast.error('Trial signup was canceled. You can try again when you\'re ready.');
      
      // Remove the payment parameter from URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [checkPlanLimits]);

  return (
    <div className="min-h-screen bg-background">
      {isLoading || status === "loading" || isPlanCheckLoading ? (
        <div className="container mx-auto py-6 max-w-5xl">
          <div className="px-4 sm:px-0 pt-4 sm:pt-0 pb-6">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>

          <div className="px-4 sm:px-0">
            <Skeleton className="h-96" />
          </div>
        </div>
      ) : status === "authenticated" ? (
        <div className="relative">
          <FloatingActionMenu
            lastSaved={lastSaved}
            formatLastSaved={formatLastSaved}
            isSavingDraft={isSavingDraft}
            isSubmitting={isSubmitting}
            onSaveDraft={createOrUpdateDraftAgent}
            onSubmit={form.handleSubmit(handleSubmit)}
            submitButtonText="Launch Agent"
            isFormValid={isFormValid()}
            isActiveAgent={false}
          />

          <div className="min-h-[calc(100vh-80px)] bg-muted/40">
            <div className="container mx-auto py-8 px-4 max-w-5xl pb-40">
              <div className="flex flex-col space-y-6">
                {/* Plan limit notification banner */}
                {!isPlanCheckLoading && planLimits && (
                  <div className={`rounded-lg overflow-hidden ${
                    planLimits.currentAgentCount === 0 && !planLimits.plan
                      ? "bg-primary/5 border border-primary/20"
                      : !planLimits.canCreate
                        ? "bg-primary/5 border border-primary/20"
                        : "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
                  }`}>
                    <div className="flex items-center justify-between p-4">
                      <div className="flex items-start">
                        {planLimits.currentAgentCount === 0 && !planLimits.plan ? (
                          <Zap className="h-5 w-5 text-primary mr-3 mt-0.5 flex-shrink-0" />
                        ) : !planLimits.canCreate ? (
                          <Zap className="h-5 w-5 text-primary mr-3 mt-0.5 flex-shrink-0" />
                        ) : (
                          <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        )}
                        <div>
                          <h3 className="text-sm font-medium text-foreground">
                            {planLimits.currentAgentCount === 0 && !planLimits.plan
                              ? "Start your 2-week free trial"
                              : !planLimits.canCreate
                                ? "Upgrade to create more agents"
                                : `You can create ${planLimits.agentsLeft} more agent${planLimits.agentsLeft !== 1 ? 's' : ''}`}
                          </h3>
                          <p className="text-xs text-muted-foreground mt-1">
                            {planLimits.currentAgentCount === 0 && !planLimits.plan
                              ? (
                                <>
                                  Get a free trial to create your first agent and explore all premium features of Hobby Plan.{' '}
                                  <Link 
                                    href="/billing"
                                    className="text-primary underline cursor-pointer"
                                  >
                                    View plans
                                  </Link>
                                </>
                              )
                              : !planLimits.canCreate
                                ? `You've reached the limit of ${planLimits.totalAgents} agent${planLimits.totalAgents !== 1 ? 's' : ''} on your ${planLimits.plan?.name || 'current'} plan.`
                                : `You're using ${planLimits.currentAgentCount} of ${planLimits.totalAgents} agents on your ${planLimits.plan?.name || 'current'} plan.`}
                          </p>
                        </div>
                      </div>
                      {(!planLimits.canCreate || (planLimits.currentAgentCount === 0 && !planLimits.plan)) && (
                        <Button
                          size="sm"
                          onClick={() => planLimits.currentAgentCount === 0 && !planLimits.plan 
                            ? initiateFreeTrial() 
                            : router.push('/billing')}
                          className={`ml-4 whitespace-nowrap ${
                            "bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white"
                          }`}
                        >
                          {planLimits.currentAgentCount === 0 && !planLimits.plan
                            ? "Get Free Trial"
                            : "Upgrade Plan"}
                        </Button>
                      )}
                    </div>
                  </div>
                )}
                {/* Draft agent notification banner */}
                {hasDraft && lastDraftId && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4 mx-4 sm:mx-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Info className="h-5 w-5 text-blue-500 mr-2" />
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          You have a draft agent. Would you like to continue where you left off?
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          loadSpecificDraft(lastDraftId);
                          toast.success("Draft agent loaded successfully");
                        }}
                      >
                        Load Draft
                      </Button>
                    </div>
                  </div>
                )}

                {/* Page Header with improved mobile padding */}
                <div className="px-4 sm:px-0 pt-4 sm:pt-0 pb-2">
                  <h1 className="text-2xl font-bold tracking-tight">Create a New Agent</h1>
                  <p className="text-muted-foreground mt-1">
                    Create your own AI agent with custom knowledge and capabilities
                  </p>
                </div>

                <div className="flex items-center text-sm text-muted-foreground mb-6">
                  <div className="flex items-center">
                    <div className={`h-2.5 w-2.5 rounded-full ${isSavingDraft ? 'bg-primary animate-pulse' : 'bg-primary'} mr-2`}></div>
                    <span className="text-sm font-medium text-primary">Create</span>
                  </div>
                  <span className="text-muted-foreground mx-2">→</span>
                  <div className="flex items-center">
                    <div className={`h-2.5 w-2.5 rounded-full ${isSubmitting ? 'bg-amber-500 animate-pulse' : 'bg-muted-foreground/40'} mr-2`}></div>
                    <span className="text-sm text-muted-foreground">Launch</span>
                  </div>
                </div>

                <FormProvider {...form}>
                  {/* Basic Info Section */}
                  <Card className="border shadow-sm overflow-hidden mb-6">
                    <CardContent className="p-6">
                      <div className="space-y-6">
                        <div className="flex items-center">
                          <h2 className="text-xl font-semibold tracking-tight">Basic Information</h2>
                          <div className="ml-2 px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded">Required</div>
                        </div>
                        <AgentBasicInfoForm
                          isCheckingSlug={isCheckingSlug}
                          slugExists={slugExists}
                          debouncedSlugCheck={checkSlugAvailability}
                          generateSlug={generateSlug}
                          logoPreview={logoPreview}
                          setLogoPreview={setLogoPreview}
                          setLogoFile={setLogoFile}
                          setLogoS3Url={setLogoS3Url}
                          handleLogoChange={handleLogoChange}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Appearance Section */}
                  <Card className="border shadow-sm overflow-hidden mb-6">
                    <CardContent className="p-6">
                      <div className="space-y-6">
                        <div className="flex items-center">
                          <h2 className="text-xl font-semibold tracking-tight">Functionality</h2>
                          <div className="ml-2 px-2 py-0.5 bg-secondary/50 text-secondary-foreground text-xs font-medium rounded">Recommended</div>
                        </div>
                        <AgentAppearanceForm
                          quickMessages={quickMessages}
                          setQuickMessages={setQuickMessages}
                          onSaveDraft={createOrUpdateDraftAgent}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Monetization Section */}
                  <Card className="border shadow-sm overflow-hidden mb-32">
                    <CardContent className="p-6">
                      <div className="space-y-6">
                        <div className="flex items-center">
                          <h2 className="text-xl font-semibold tracking-tight">Monetization</h2>
                          <div className="ml-2 px-2 py-0.5 bg-secondary/50 text-secondary-foreground text-xs font-medium rounded">Optional</div>
                        </div>
                        
                        <div className="bg-primary/5 border border-primary/20 rounded-md p-4 mb-4">
                          <div className="flex">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                              <p className="text-sm text-gray-900 font-medium">Important: Choose pricing carefully</p>
                              <p className="text-xs text-gray-700 mt-1">
                                Once your agent is active, pricing settings cannot be changed. 
                                Make sure you're satisfied with your choice before launching your agent.
                              </p>
                            </div>
                          </div>
                        </div>
                        
                        {isPlanCheckLoading ? (
                          <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                          </div>
                        ) : (
                          <AgentMonetizationForm form={form} />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </FormProvider>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-80px)]">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Sign in to create agents</h2>
            <p className="text-muted-foreground mb-4">You need to be signed in to create AI agents</p>
            <Button onClick={() => router.push('/verify')}>Sign In</Button>
          </div>
        </div>
      )}
    </div>
  );
}