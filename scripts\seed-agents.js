#!/usr/bin/env node

// This script is used to seed multiple agents from the agents-seed.json file

const { execSync } = require('child_process');
const path = require('path');

try {
  console.log('Seeding agents from agents-seed.json...');
  
  // Run the seed-agents.ts file using the project's Next.js setup
  execSync('npx tsx lib/db/seed-agents.ts', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('Agents seeded successfully!');
} catch (error) {
  console.error('Failed to seed agents:', error);
  process.exit(1);
}
