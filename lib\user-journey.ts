import { addTagToSubscriber, createOrUpdateSubscriber, removeTag<PERSON>romSubscriber, CONVERTKIT_TAGS } from './convertkit';

/**
 * Track user signup in ConvertKit
 * Call this when a user first creates an account
 */
export async function trackUserSignup(email: string, firstName?: string) {
  // First ensure the subscriber exists in ConvertKit
  const subscriberResult = await createOrUpdateSubscriber(email, firstName);
  
  if (!subscriberResult.success) {
    console.error('Failed to create/update subscriber:', subscriberResult.message);
    return subscriberResult;
  }
  
  // Add the USER tag if it exists
  if (CONVERTKIT_TAGS.USER) {
    return await addTagToSubscriber(email, CONVERTKIT_TAGS.USER);
  }
  
  return { success: false, message: 'USER tag not configured' };
}

/**
 * Track when a user starts a trial
 */
export async function trackTrialStarted(email: string) {
  if (CONVERTKIT_TAGS.TRIAL) {
    return await addTagToSubscriber(email, CONVERTKIT_TAGS.TRIAL);
  }
  return { success: false, message: 'TRIAL tag not configured' };
}

/**
 * Track when a user upgrades to Hobby plan
 */
export async function trackHobbySubscription(email: string) {
  // Remove other subscription tags first if they exist
  if (CONVERTKIT_TAGS.TRIAL) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.TRIAL);
  }
  if (CONVERTKIT_TAGS.PRO) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.PRO);
  }
  if (CONVERTKIT_TAGS.CANCELLED) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.CANCELLED);
  }
  
  // Add the HOBBY tag if it exists
  if (CONVERTKIT_TAGS.HOBBY) {
    return await addTagToSubscriber(email, CONVERTKIT_TAGS.HOBBY);
  }
  return { success: false, message: 'HOBBY tag not configured' };
}

/**
 * Track when a user upgrades to Pro plan
 */
export async function trackProSubscription(email: string) {
  // Remove other subscription tags first if they exist
  if (CONVERTKIT_TAGS.TRIAL) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.TRIAL);
  }
  if (CONVERTKIT_TAGS.HOBBY) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.HOBBY);
  }
  if (CONVERTKIT_TAGS.CANCELLED) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.CANCELLED);
  }
  
  // Add the PRO tag if it exists
  if (CONVERTKIT_TAGS.PRO) {
    return await addTagToSubscriber(email, CONVERTKIT_TAGS.PRO);
  }
  return { success: false, message: 'PRO tag not configured' };
}

/**
 * Track when a user cancels their subscription
 */
export async function trackSubscriptionCancelled(email: string) {
  // Remove active subscription tags first if they exist
  if (CONVERTKIT_TAGS.TRIAL) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.TRIAL);
  }
  if (CONVERTKIT_TAGS.HOBBY) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.HOBBY);
  }
  if (CONVERTKIT_TAGS.PRO) {
    await removeTagFromSubscriber(email, CONVERTKIT_TAGS.PRO);
  }
  
  // Add the CANCELLED tag if it exists
  if (CONVERTKIT_TAGS.CANCELLED) {
    return await addTagToSubscriber(email, CONVERTKIT_TAGS.CANCELLED);
  }
  return { success: false, message: 'CANCELLED tag not configured' };
}

/**
 * Track when a user creates or updates a draft agent
 * @param email - The user's email address
 * @param agentName - The name of the draft agent
 */
export async function trackDraftAgentCreated(email: string, agentName: string) {
  try {
    // Debug logging
    console.log(`[ConvertKit Debug] Attempting to add HAS_DRAFT_AGENT tag to ${email}`);
    console.log(`[ConvertKit Debug] Tag ID: ${CONVERTKIT_TAGS.HAS_DRAFT_AGENT}`);
    
    // Simply add the HAS_DRAFT_AGENT tag
    if (CONVERTKIT_TAGS.HAS_DRAFT_AGENT) {
      console.log(`[ConvertKit Debug] HAS_DRAFT_AGENT tag exists: ${CONVERTKIT_TAGS.HAS_DRAFT_AGENT}`);
      const tagResult = await addTagToSubscriber(email, CONVERTKIT_TAGS.HAS_DRAFT_AGENT);
      console.log(`[ConvertKit Debug] Tag result:`, tagResult);
      
      if (!tagResult.success) {
        console.error('[ConvertKit Debug] Failed to add HAS_DRAFT_AGENT tag:', tagResult.message);
        return tagResult;
      }
      console.log(`[ConvertKit Debug] Successfully added HAS_DRAFT_AGENT tag to ${email}`);
      return { success: true, message: 'Draft agent tag added successfully' };
    } else {
      console.warn('[ConvertKit Debug] HAS_DRAFT_AGENT tag not configured');
      return { success: false, message: 'HAS_DRAFT_AGENT tag not configured' };
    }
  } catch (error) {
    console.error('Error tracking draft agent creation:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error tracking draft agent' 
    };
  }
}

/**
 * Track when an agent is activated (moved from draft to active)
 * @param email - The user's email address
 * @param remainingDraftCount - The number of draft agents the user still has
 */
export async function trackAgentActivated(email: string, remainingDraftCount: number) {
  try {
    // If there are no more draft agents, remove the HAS_DRAFT_AGENT tag
    if (remainingDraftCount <= 0 && CONVERTKIT_TAGS.HAS_DRAFT_AGENT) {
      const removeTagResult = await removeTagFromSubscriber(email, CONVERTKIT_TAGS.HAS_DRAFT_AGENT);
      if (!removeTagResult.success) {
        console.error('Failed to remove HAS_DRAFT_AGENT tag:', removeTagResult.message);
        return removeTagResult;
      }
      return { success: true, message: 'Draft agent tag removed successfully' };
    }
    
    // If there are still draft agents, do nothing
    return { success: true, message: 'User still has draft agents, tag retained' };
  } catch (error) {
    console.error('Error tracking agent activation:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error tracking agent activation' 
    };
  }
}
