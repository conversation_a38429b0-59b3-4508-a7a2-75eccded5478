# Chat Interaction Flow

## Overview

The chat interaction flow in the BuildThatIdea platform enables users to communicate with AI agents, leveraging the agents' knowledge bases and capabilities. This document details the complete process from initiating a chat to receiving AI responses, including the underlying technical implementation.

## User Interface

The chat interface is implemented in the `/app/chat` directory and provides a familiar messaging experience with the following features:

1. **Message Input**: Text input for user messages
2. **Message History**: Display of previous messages in the conversation
3. **Quick Messages**: Predefined messages that users can send with a click
4. **Attachments**: Support for file attachments in messages
5. **Markdown Rendering**: Support for rich text formatting in responses

## Chat Process

### 1. Initiating a Chat

Users can start a chat in several ways:

- **From Agent Page**: Clicking "Chat" on an agent's profile
- **From Dashboard**: Starting a new chat from the dashboard
- **From Chat History**: Continuing an existing chat

### 2. Message Exchange

The basic message exchange flow:

1. **User Input**: User types and sends a message
2. **Processing Indicator**: UI shows that the message is being processed
3. **Knowledge Retrieval**: System retrieves relevant information from the agent's knowledge base
4. **AI Processing**: Message is sent to the AI model with context and instructions
5. **Response Display**: AI response is displayed in the chat interface
6. **Message Storage**: Conversation is stored in the database

## Technical Implementation

### AI Message Processing

The core of the chat interaction is implemented using the AI SDK with streaming responses:

```typescript
// Simplified example from the chat implementation
export async function POST(req: Request) {
  const { messages, agentId } = await req.json();
  const session = await auth();
  
  if (!session?.user) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Get agent details
  const agent = await getAgentById({ agentId });
  
  if (!agent) {
    return Response.json({ error: 'Agent not found' }, { status: 404 });
  }

  // Create AI instance with the appropriate model
  const ai = new AI({
    model: agent.model || 'claude-3-haiku',
    tools: [getKnowledge],
  });

  // Process the message with streaming
  const result = await ai.run({
    messages,
    tools: [
      {
        type: 'function',
        name: 'getKnowledge',
        parameters: {
          query: messages[messages.length - 1].content,
          agentId: agent.id,
          limit: 5,
        },
      },
    ],
  });

  // Return streaming response
  return new StreamingTextResponse(result.text);
}
```

### Knowledge Retrieval Tool

The `getKnowledge` tool is a critical component that retrieves relevant information from the agent's knowledge base:

```typescript
export const getKnowledge = tool({
  description:
    "REQUIRED: You MUST call this tool for EVERY question before responding. This tool retrieves relevant information from the agent's knowledge base that you should use in your answer.",
  parameters: z.object({
    query: z.string().describe('The search query to find relevant information'),
    agentId: z.string().describe('The ID of the agent whose knowledge base to search'),
    limit: z.number().optional().describe('Maximum number of results to return (default: 5)'),
  }),
  execute: async ({ query, agentId, limit = 5 }) => {
    try {
      // Use the retrieveRelevantContent function to get relevant content
      const relevantContent = await retrieveRelevantContent(
        query,
        agentId,
        limit,
        0.5, // Default similarity threshold
      );

      // Return the results in a minimal format
      if (relevantContent.length === 0) {
        return 'I don\'t have specific information about that. I\'ll answer based on my general knowledge.';
      }

      // Add a reminder to each content chunk to not mention the knowledge base
      const processedContent = relevantContent.map(content => {
        return `[CRITICAL INSTRUCTION: USE THIS INFORMATION IN YOUR ANSWER, but NEVER mention that it came from a knowledge base or that you used a tool.] ${content}`;
      });

      return processedContent;
    } catch (error) {
      return `I don't have specific information about that. I'll answer based on my general knowledge.`;
    }
  },
});
```

### Vector Search Implementation

The vector search implementation uses Upstash Vector to find relevant content:

```typescript
export async function retrieveRelevantContent(
  query: string,
  agentId: string,
  limit = 5,
  similarityThreshold = 0.5,
): Promise<string[]> {
  try {
    // Get the vector index for this agent
    const vectorIndex = await getVectorIndex(agentId);

    // Query Upstash Vector using the query text directly
    const results = await vectorIndex.query({
      data: query, // Use 'data' field for Upstash to generate the embedding
      topK: limit,
      includeVectors: false,
      includeMetadata: true,
    });

    // Filter by similarity threshold and sort
    const filteredResults = (results as VectorResult[])
      .filter((result) => result.score >= similarityThreshold)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    // Extract content with type safety
    return filteredResults.map(
      (result) => result.metadata?.content ?? 'Content not available',
    );
  } catch (error) {
    console.error('Vector retrieval error:', error);
    throw new Error('Failed to retrieve relevant content');
  }
}
```

### Chat Storage

Chat messages are stored in the database using the following schema:

```typescript
export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull(),
  title: text('title').notNull(),
  userId: uuid('userId').notNull().references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] }).notNull().default('private'),
  agentId: uuid('agentId').notNull().references(() => agents.id),
});

export const message = pgTable('Message_v2', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId').notNull().references(() => chat.id),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull(),
  tokens: integer('tokens'),
});
```

## API Endpoints

The chat interaction flow uses the following API endpoints:

1. **POST /api/chat**: Creates a new chat
2. **GET /api/chat/:id**: Retrieves chat history
3. **POST /api/chat/:id/messages**: Adds a new message to a chat
4. **POST /api/chat-agent/:agentId**: Processes a message with an agent
5. **GET /api/agents/:id**: Retrieves agent details for the chat

## Error Handling

The chat interaction flow includes comprehensive error handling:

1. **Network Errors**: Handling connection issues during streaming
2. **AI Model Errors**: Handling errors from the AI provider
3. **Knowledge Retrieval Errors**: Graceful fallback when vector search fails
4. **Rate Limiting**: Handling rate limits from AI providers
5. **Token Limits**: Managing token limits for large conversations

## Streaming Implementation

The chat uses streaming responses for a better user experience:

1. **Server-Sent Events**: Used for streaming AI responses
2. **Incremental Updates**: UI updates incrementally as tokens arrive
3. **Typing Indicators**: Visual indication that the AI is "typing"
4. **Error Recovery**: Ability to recover from stream interruptions

## Message Processing

Messages go through several processing steps:

1. **Input Sanitization**: Removing potentially harmful content
2. **Token Counting**: Counting tokens for rate limiting and billing
3. **Context Management**: Managing conversation context for the AI
4. **Knowledge Integration**: Integrating knowledge base results
5. **Response Formatting**: Formatting the response for display

## Security Considerations

The chat interaction flow includes several security measures:

1. **Authentication**: Only authenticated users can access chats
2. **Authorization**: Users can only access their own chats
3. **Input Validation**: All user inputs are validated
4. **Content Filtering**: Potentially harmful content is filtered
5. **Rate Limiting**: Preventing abuse through rate limiting

## Performance Considerations

The chat interaction flow includes several performance optimizations:

1. **Streaming Responses**: Reducing time to first token
2. **Caching**: Caching agent details and frequently used data
3. **Optimistic Updates**: UI updates optimistically before confirmation
4. **Lazy Loading**: Chat history is loaded incrementally
5. **Connection Management**: Efficient handling of WebSocket connections

## User Experience

The chat interaction flow includes several UX enhancements:

1. **Real-time Feedback**: Immediate feedback on message status
2. **Markdown Support**: Rich text formatting in messages
3. **Code Highlighting**: Syntax highlighting for code blocks
4. **Mobile Responsiveness**: Optimized for mobile devices
5. **Keyboard Shortcuts**: Keyboard shortcuts for common actions

## Chat History Management

Users can manage their chat history with the following features:

1. **Chat Listing**: View all previous chats
2. **Chat Naming**: Automatically generated or custom chat names
3. **Chat Deletion**: Delete unwanted chats
4. **Chat Export**: Export chat history as text or JSON
5. **Chat Search**: Search through chat history

## Subscription and Access Control

The chat system enforces subscription and access control:

1. **Free Agents**: Available to all users
2. **Subscription Agents**: Available only to subscribers
3. **Lifetime Access Agents**: Available to users who purchased lifetime access
4. **Access Verification**: Checking subscription status before allowing chat
5. **Grace Period**: Handling expired subscriptions with grace periods

## Analytics and Monitoring

The chat system includes analytics and monitoring:

1. **Usage Tracking**: Tracking message counts and token usage
2. **Error Monitoring**: Monitoring and alerting for system errors
3. **Performance Metrics**: Tracking response times and latency
4. **User Feedback**: Collecting user feedback on responses
5. **Quality Metrics**: Measuring response quality and relevance
