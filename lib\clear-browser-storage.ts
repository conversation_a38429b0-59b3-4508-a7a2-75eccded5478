/**
 * Utility function to clear all browser storage during sign-out
 * Clears sessionStorage, localStorage, and IndexedDB
 * @returns Promise that resolves when all storage has been cleared
 */
export async function clearBrowserStorage(): Promise<void> {
  console.log('Starting browser storage clearing process');
  
  // Clear sessionStorage with verification
  try {
    // First log what's in sessionStorage for debugging
    console.log('SessionStorage before clearing:', Object.keys(sessionStorage));
    
    // Clear all sessionStorage
    sessionStorage.clear();
    
    // Verify it's actually cleared
    if (Object.keys(sessionStorage).length > 0) {
      console.warn('SessionStorage not fully cleared, forcing item-by-item removal');
      // Force item-by-item removal as fallback
      Object.keys(sessionStorage).forEach(key => {
        console.log(`Removing sessionStorage item: ${key}`);
        sessionStorage.removeItem(key);
      });
    }
    
    console.log('Session storage cleared, remaining items:', Object.keys(sessionStorage));
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
  }

  // Clear localStorage
  try {
    localStorage.clear();
    console.log('Local storage cleared');
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }

  // Clear IndexedDB - properly awaited
  try {
    await clearIndexedDBDatabases();
    console.log('IndexedDB cleared');
  } catch (error) {
    console.error('Error clearing IndexedDB:', error);
  }
  
  console.log('Browser storage clearing process completed');
}

/**
 * Helper function to clear all IndexedDB databases
 */
async function clearIndexedDBDatabases(): Promise<void> {
  if (typeof window === 'undefined' || !window.indexedDB || !window.indexedDB.databases) {
    console.log('IndexedDB not available or databases API not supported');
    return;
  }
  
  try {
    const databases = await window.indexedDB.databases();
    console.log(`Found ${databases.length} IndexedDB databases to clear`);
    
    // Create an array of promises for each database deletion
    const deletionPromises = databases.map(db => {
      if (!db.name) return Promise.resolve();
      
      return new Promise<void>((resolve, reject) => {
        const deleteRequest = window.indexedDB.deleteDatabase(db.name!);
        
        deleteRequest.onsuccess = () => {
          console.log(`Successfully deleted IndexedDB database: ${db.name}`);
          resolve();
        };
        
        deleteRequest.onerror = () => {
          console.error(`Error deleting IndexedDB database: ${db.name}`);
          reject(new Error(`Failed to delete database ${db.name}`));
        };
      });
    });
    
    // Wait for all deletions to complete
    await Promise.all(deletionPromises);
  } catch (error) {
    console.error('Error in clearIndexedDBDatabases:', error);
    throw error;
  }
}
