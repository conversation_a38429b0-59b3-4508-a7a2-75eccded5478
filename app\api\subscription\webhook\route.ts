import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { handleCreatedSubscriptionForCreator } from '@/lib/subscriptions/creator-subscriptions-events/handle-subscription-created';
import { handleUpdatedSubscriptionForCreator } from '@/lib/subscriptions/creator-subscriptions-events/handle-subscription-updated';
import { handleDeletedSubscriptionForCreator } from '@/lib/subscriptions/creator-subscriptions-events/handle-subscription-deleted';
import { handlePaymentSucceedForCreator } from '@/lib/subscriptions/creator-subscriptions-events/handle-payment-succeed';
import { handleCancelRequestLogForCreator } from '@/lib/subscriptions/creator-subscriptions-events/handle-cancel-request';
import { handleUserSubscription } from '@/lib/subscriptions/handle-user-subscription';
import { saveStripeCustomerInfo, saveStripeUserInfo } from '@/lib/subscriptions/manage-stripe-customer';
import { handleCancelRequestLogForuser } from '@/lib/subscriptions/user-subscriptions-events/handle-cancel-request';
import { handleDeletedSubscriptionForUser } from '@/lib/subscriptions/user-subscriptions-events/handle-subscription-deleted';
import { db } from '@/lib/db';
import { agents, stripeEvents } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { sub, add } from 'date-fns';

interface StripeInvoice extends Stripe.Invoice {
  amount_paid: number;
  subscription: string | null;
}

// Use a function to create the Stripe instance to avoid initialization during build
const getStripe = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  if (!stripeSecretKey) {
    throw new Error('Missing STRIPE_SECRET_KEY environment variable');
  }
  // Cast the API version to any to avoid type conflicts
  // This is safe as we're using a valid Stripe API version
  return new Stripe(stripeSecretKey, {
    apiVersion: '2025-03-31.basil',
  });
};

const getWebhookSecret = () => {
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  if (!webhookSecret) {
    throw new Error('Missing STRIPE_WEBHOOK_SECRET environment variable');
  }
  return webhookSecret;
};

export const config = {
  api: {
    bodyParser: false,
  },
};

export async function POST(request: Request) {
  let event: Stripe.Event | undefined;
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return new Response(
        JSON.stringify({ error: 'No signature found' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const webhookSecret = getWebhookSecret();
    const stripe = getStripe();
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);

    // List of events we want to handle
    const ALLOWED_EVENTS: string[] = [
      'checkout.session.completed',
      'invoice.paid',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'customer.subscription.created',
      'subscription_schedule.created',
      'subscription_schedule.updated',
      'invoice.payment_succeeded'
    ];


    // Check if the event type is one we want to handle
    if (!ALLOWED_EVENTS.includes(event.type as typeof ALLOWED_EVENTS[number])) {
      console.log('Unhandled event type:', event.type);
      return new Response(JSON.stringify({ received: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    // First check if this event was already processed successfully
    const existingEvent = await db
      .select()
      .from(stripeEvents)
      .where(eq(stripeEvents.id, event.id))
      .limit(1);

    if (existingEvent.length > 0 && existingEvent[0].processed) {
      console.log('Event already processed successfully:', event.id);
      return new Response(JSON.stringify({ received: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // If not processed or not exists, insert/update the event record
    await db
      .insert(stripeEvents)
      .values({
        id: event.id,
        type: event.type,
      })
      .onConflictDoNothing();

    console.log('----------------- Webhook event type: ------', event.type);
    
    if (event.type === 'customer.subscription.created') {
      const subscription = event.data.object;
      const metadata = subscription.metadata; // Metadata from the subscription
      if (metadata && metadata.type === 'creator') {
        let balanceExpiryDate;
        // assuming it would be called for first time user only
        const priceId = subscription.items.data[0].price.id;
        const price = await stripe.prices.retrieve(priceId);
        const product = await stripe.products.retrieve(price.product as string);
        const isTrial = metadata?.isFreeTrial === 'true';
 
        const periodEndUnix = subscription.trial_end || subscription.current_period_end;
        const expiryDate = new Date(periodEndUnix * 1000).toISOString();
        
        // Set balance expiry date based on subscription type
        if (isTrial || price.recurring?.interval === 'month') {
            balanceExpiryDate = expiryDate;
        } else {
            // For yearly subscriptions, set balance expiry to one month from now
            const oneMonthFromNow = new Date();
            oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
            balanceExpiryDate = oneMonthFromNow.toISOString();
        }

        handleCreatedSubscriptionForCreator(subscription.metadata.userId, product.name,
          expiryDate,
          balanceExpiryDate,
      );
            }
    }
    else if (event.type === 'invoice.payment_succeeded') {
      const invoice = event.data.object as Stripe.Invoice;

      const invoiceSubscriptionId = (invoice.subscription as string | null);

      if (!invoiceSubscriptionId) {
        console.error('No subscription ID on invoice!');
        return;
      }

      const subscription = await stripe.subscriptions.retrieve(invoiceSubscriptionId);
      const metadata = subscription.metadata;
      
      if (metadata && metadata.type === 'user') {
        const invoice = event.data.object as Stripe.Invoice;
  
        // Get subscription ID from the invoice lines data
        const invoiceSubscriptionId = invoice.lines.data[0]?.subscription as string;
        if (!invoiceSubscriptionId) {
          console.error('No subscription found in invoice');
          return new Response(
            JSON.stringify({ error: 'No subscription found' }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          );
        }
 
        const invoiceLine = invoice.lines.data[0]; // assumes only 1 line item
      
        const expiryUnix = invoiceLine.period.end;
        const expiryDate = new Date(expiryUnix * 1000).toISOString();

        
        await handleUserSubscription({
          userId: metadata.userId,
          agentId: metadata.agentId,
          subscriptionType  : metadata.subscriptionType,
          expiryDate: expiryDate
        }, invoice.amount_paid);
              }
    } 
    else if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      const metadata = session.metadata;
      
      if (metadata && metadata.type === 'creator') {
          // Get customer ID from the session
        const customerId = session.customer as string;
        if (!customerId) {
          console.error('No customer ID found in checkout session');
          return new Response(
            JSON.stringify({ error: 'No customer ID found' }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          );
        }

        // Get user ID from metadata
        const userId = metadata?.userId;
        if (!userId) {
          console.error('No user ID found in checkout session metadata');
          return new Response(
            JSON.stringify({ error: 'No user ID found' }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          );
        }

        await saveStripeCustomerInfo(userId, customerId);

        console.log('Checkout session completed:', {
          customerId,
          userId,
        });
       
      }
      if (metadata && metadata.type === 'user') {
        // Get customer ID from the session
      const customerId = session.customer as string;
      if (!customerId) {
        console.error('No customer ID found in checkout session');
        return new Response(
          JSON.stringify({ error: 'No customer ID found' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }

      // Get user ID from metadata
      const userId = metadata?.userId;
      if (!userId) {
        console.error('No user ID found in checkout session metadata');
        return new Response(
          JSON.stringify({ error: 'No user ID found' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }

      await saveStripeUserInfo(userId, customerId);

      console.log('Checkout session completed:', {
        customerId,
        userId,
      });
     
    }
    }
    
    else if (event.type === 'customer.subscription.updated') { // event triggers on upgrade, downgrade and cancellation request
      const subscription = event.data.object as Stripe.Subscription;
      const freshSub = await stripe.subscriptions.retrieve(subscription.id);
      try {
        const metadata = freshSub.metadata;
        const previous = event.data.previous_attributes;

        if (metadata?.type === 'user') {
        if (freshSub.cancel_at_period_end && previous?.cancel_at_period_end === false) { // Cancellation request
          // Get product details for the subscription
          const priceId = freshSub.items.data[0].price.id;
          const price = await stripe.prices.retrieve(priceId);
          const product = await stripe.products.retrieve(price.product as string);
          if (freshSub.cancel_at_period_end && freshSub.canceled_at) {
            let cancelAt = freshSub.cancel_at ? new Date(freshSub.cancel_at * 1000) : null;
            await handleCancelRequestLogForuser(metadata.userId, product.id, cancelAt);
          }
        }
      } else if (metadata?.type === 'creator') {
        // Always try to get the schedule for subscription updates
        let schedule;
        if (freshSub.schedule) {
          schedule = await stripe.subscriptionSchedules.retrieve(freshSub.schedule as string);
        }

        if (freshSub.cancel_at_period_end && previous?.cancel_at_period_end === false) { // Cancellation request
          // Get product details for the subscription
          const priceId = freshSub.items.data[0].price.id;
          const price = await stripe.prices.retrieve(priceId);
          const product = await stripe.products.retrieve(price.product as string);
          if (freshSub.cancel_at_period_end && freshSub.canceled_at) {
            let cancelAt = freshSub.cancel_at ? new Date(freshSub.cancel_at * 1000) : null;
            await handleCancelRequestLogForCreator(metadata.userId, product.name, cancelAt);
          }
        }

        if (previous?.items) {
          await handleUpdatedSubscriptionForCreator(
            metadata.userId,
            freshSub,
            previous,
            schedule
          );
        }
        
        const customerId = freshSub.customer as string;
        const userId = metadata.userId;
        
        await saveStripeCustomerInfo(userId, customerId);
      }
    } catch (error) {
      console.error('Error handling subscription update:', error);
      throw error;
    }
    }
    else if (event.type === 'invoice.paid') {
      const invoice = event.data.object as StripeInvoice;
      const subscription = invoice.subscription;
      if (subscription) {
        const subscriptionDetails = await stripe.subscriptions.retrieve(subscription) as Stripe.Subscription;
        const metadata = subscriptionDetails.metadata;
        if (metadata?.type === 'creator') {
          // Get product details
          const priceId = subscriptionDetails.items.data[0].price.id;
          const price = await stripe.prices.retrieve(priceId);
          const product = await stripe.products.retrieve(price.product as string);
          const subscriptionType = price.recurring?.interval === 'year' ? 'yearly' : 'monthly';
          const now = new Date();
          let expiryDate: Date;
          if (subscriptionType === 'yearly') {
            expiryDate = add(now, { years: 1 });
          } else {
            expiryDate = add(now, { months: 1 });
          }
          // Format as YYYY-MM-DD
          const formattedDate = expiryDate.toISOString().split('T')[0];
          console.log('Formatted expiry date:', formattedDate);
          await handlePaymentSucceedForCreator(
            {
              userId: metadata.userId,
              productName: product.name,
              subscriptionType,
            },
            invoice.amount_paid,
            false,
            formattedDate
          );
        }
      }
    }
    else if (event.type === 'subscription_schedule.created' || event.type === 'subscription_schedule.updated') {
      const schedule = event.data.object as Stripe.SubscriptionSchedule;
      if (!schedule.subscription) {
        return NextResponse.json({ received: true });
      }
      if (typeof schedule.subscription !== 'string') {
        return NextResponse.json({ received: true });
      }
      const subscription = await stripe.subscriptions.retrieve(schedule.subscription);
      const previousSubscription = event.data.previous_attributes as Partial<Stripe.Subscription>;
      const metadata = subscription.metadata;
      
      if (metadata?.type === 'creator') {
        await handleUpdatedSubscriptionForCreator(
          metadata.userId,
          subscription,
          previousSubscription,
          schedule  // Pass the schedule here
        );
      }
    }
    else if (event.type === 'customer.subscription.deleted') {
      const subscription = event.data.object as Stripe.Subscription;
      const metadata = subscription.metadata;
      if (metadata && metadata.type === 'user') {
        const userId = metadata?.userId;
        await handleDeletedSubscriptionForUser(userId);
      }
      if (metadata && metadata.type === 'creator') {
        const userId = metadata?.userId;
        await handleDeletedSubscriptionForCreator(userId);
      }
    }

    // Only mark as processed if we actually processed something
    return NextResponse.json({ received: true });
  } catch (error: any) {
    console.error('Error handling webhook:', error);
    const statusCode = error.message?.includes('signature') ? 400 : 500;
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: statusCode,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  finally {
    try {
      if (event?.id) {
        await db.update(stripeEvents)
          .set({ processed: true, processed_at: new Date() })
          .where(eq(stripeEvents.id, event.id));
      }
    } catch (error) {
      console.error('Error in finally block:', error);
    }
  }
}