const { sql } = require('drizzle-orm');
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL environment variable is not set');
}

const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

/**
 * Migration to rename agent_id to agentId in Chat table
 */
async function renameChatAgentIdColumn() {
  console.log('🔄 Renaming agent_id to agentId in Chat table...');
  
  try {
    // Rename the column
    await db.execute(sql`
      ALTER TABLE "Chat"
      RENAME COLUMN "agent_id" TO "agentId";
    `);
    
    console.log('✅ Column renamed successfully!');
  } catch (error) {
    console.error('❌ Failed to rename column:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  renameChatAgentIdColumn()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
