"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { resetAppState } from '@/lib/reset-app-state';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LockKeyhole, Sparkles, AlertCircle, ExternalLink, LogOut, ArrowRight } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { ShimmerButton } from "@/components/ui/shimmer-button";

export default function VerifyPage() {
  useEffect(() => {
    document.title = 'Verify Account | BuildThatIdea';
  }, []);

  const router = useRouter();
  const { data: session } = useSession();
  const [code, setCode] = useState<string[]>(Array(8).fill(""));
  const [error, setError] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isWaitlisted, setIsWaitlisted] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(8).fill(null));

  // Reset state when entering a new code after being waitlisted
  useEffect(() => {
    if (isWaitlisted) {
      setIsWaitlisted(false);
    }
  }, [code,isWaitlisted]);

  const handleChange = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle pasting of entire code
      if (value.length === 8) {
        const pastedCode = value.toUpperCase().split('');
        if (pastedCode.every(char => char.match(/^[A-Z0-9]$/))) {
          setCode(pastedCode);
          setError(null);
          // Focus on the last input
          inputRefs.current[7]?.focus();
          return;
        }
      }
      value = value.slice(0, 1);
    }
    
    // Allow uppercase letters and numbers
    value = value.toUpperCase();
    if (value.match(/^[A-Z0-9]$/)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);
      setError(null);
      
      // Move to next input
      if (index < 7 && value) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();
    
    // Handle 8-character code
    if (pastedData.length === 8) {
      const pastedCode = pastedData.toUpperCase().split('');
      if (pastedCode.every(char => char.match(/^[A-Z0-9]$/))) {
        setCode(pastedCode);
        setError(null);
        // Focus on the last input
        inputRefs.current[7]?.focus();
      } else {
        setError("Invalid characters in code. Use only letters and numbers.");
      }
    } 
    // Handle shorter codes - fill as many inputs as possible
    else if (pastedData.length > 0 && pastedData.length < 8) {
      const validChars = pastedData.toUpperCase().split('')
        .filter(char => char.match(/^[A-Z0-9]$/))
        .slice(0, 8);
      
      if (validChars.length > 0) {
        const newCode = [...code];
        validChars.forEach((char, i) => {
          newCode[i] = char;
        });
        setCode(newCode);
        // Focus on the next empty input or the last filled one
        const nextEmptyIndex = Math.min(validChars.length, 7);
        inputRefs.current[nextEmptyIndex]?.focus();
      }
    }
    // Handle codes that might have spaces or other formatting
    else if (pastedData.length > 8) {
      // Try to extract 8 valid characters
      const validChars = pastedData.toUpperCase().split('')
        .filter(char => char.match(/^[A-Z0-9]$/))
        .slice(0, 8);
      
      if (validChars.length === 8) {
        setCode(validChars);
        setError(null);
        // Focus on the last input
        inputRefs.current[7]?.focus();
      } else if (validChars.length > 0) {
        const newCode = [...code];
        validChars.forEach((char, i) => {
          newCode[i] = char;
        });
        setCode(newCode);
        // Focus on the next empty input or the last filled one
        const nextEmptyIndex = Math.min(validChars.length, 7);
        inputRefs.current[nextEmptyIndex]?.focus();
      } else {
        setError("Invalid characters in code. Use only letters and numbers.");
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace") {
      if (!code[index]) {
        // Move to previous input on backspace if current is empty
        if (index > 0) {
          inputRefs.current[index - 1]?.focus();
        }
      } else {
        // Clear current input on backspace
        const newCode = [...code];
        newCode[index] = "";
        setCode(newCode);
      }
    }
  };

  const handleVerify = async () => {
    const fullCode = code.join("");
    setIsVerifying(true);
    setError(null);
    
    try {
      const response = await fetch("/api/auth/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ code: fullCode }),
      });
      
      const data = await response.json();
      console.log("Verification response:", data); // Debug log
      
      if (data.success) {
        toast.success("Access granted! Welcome aboard!");
        // Add a small delay to ensure the cookie is set before redirecting
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 500);
      } else if (data.waitlisted) {
        console.log("User waitlisted, showing waitlist UI"); // Debug log
        setIsWaitlisted(true);
        setError(data.message || "This code has reached its maximum usage. You've been added to the waiting list.");
        toast.info("You've been added to our waiting list. We'll notify you when a spot becomes available.");
      } else {
        setError(data.message || "Invalid secret code. The AI guardians are not impressed.");
      }
    } catch (error) {
      console.error("Verification error:", error);
      setError("An error occurred during verification. Please try again.");
    } finally {
      setIsVerifying(false);
    }
  };

  const handleLogout = async () => {
    try {
      // Reset all application state (contexts, browser storage, cookies)
      await resetAppState();
      console.log('App state reset before logout');
      
      // Sign out with redirect to login
      await signOut({ callbackUrl: "/login" });
    } catch (error) {
      console.error('Error during logout:', error);
      
      // Fallback: try to sign out directly if resetAppState fails
      try {
        console.warn('Proceeding with sign-out after resetAppState failure');
        await signOut({ callbackUrl: "/login" });
      } catch (fallbackError) {
        console.error('Error in sign-out fallback:', fallbackError);
        // Last resort: force redirect to login
        window.location.href = '/login';
      }
    }
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md">
        <div className="mx-auto mb-8 flex flex-col items-center text-center">
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-[#f97316] to-[#ec4899] text-white">
            <LockKeyhole className="h-6 w-6" />
          </div>
          <h1 className="mb-1 text-2xl font-bold tracking-tight">Invite-Only Access</h1>
          <p className="text-sm text-muted-foreground">
            Do you have the secret code to access the platform?
          </p>
        </div>

        <div className="overflow-hidden rounded-xl border bg-card/50 backdrop-blur-sm shadow-sm">
          <div className="p-6">
            {isWaitlisted ? (
              <div className="flex flex-col gap-4 text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-amber-100 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400">
                  <Sparkles className="h-6 w-6" />
                </div>
                <h2 className="text-xl font-semibold">You're on the Waiting List</h2>
                <p className="text-sm text-muted-foreground">
                  We've received your request and will notify you when a spot becomes available.
                </p>
                <div className="mt-2">
                  <Link 
                    href="https://twitter.com/buildthatidea" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-sm font-medium text-primary hover:underline"
                  >
                    Follow us for updates <ExternalLink className="h-3.5 w-3.5" />
                  </Link>
                </div>
                <div className="mt-4 flex flex-col gap-3">
                  <ShimmerButton
                    shimmerColor="rgba(255, 255, 255, 0.2)"
                    shimmerSize="0.1em"
                    shimmerDuration="2s"
                    className="w-full h-10 rounded-lg bg-gradient-to-r from-[#f97316] to-[#ec4899] text-white font-medium hover:shadow-md transition-all"
                    asChild
                  >
                    <Link 
                      href="https://buildthatidea.typeform.com/to/jmek3QH1" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center justify-center gap-2"
                    >
                      Join Waiting List <ExternalLink className="h-3.5 w-3.5" />
                    </Link>
                  </ShimmerButton>
                </div>
              </div>
            ) : (
              <div className="flex flex-col gap-6 px-4 sm:px-8">
                <div className="flex flex-col gap-4">
                  <div className="flex justify-center gap-2">
                    {code.map((digit, index) => (
                      <Input
                        key={index}
                        ref={(el) => {
                          inputRefs.current[index] = el;
                          return undefined;
                        }}
                        type="text"
                        inputMode="text"
                        pattern="[A-Z0-9]*"
                        maxLength={1}
                        className="w-9 h-12 sm:w-11 text-center text-lg font-mono uppercase bg-background border-border focus:border-primary focus:ring-1 focus:ring-primary/30 transition-all"
                        value={digit}
                        onChange={(e) => handleChange(index, e.target.value)}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                        onPaste={handlePaste}
                        autoFocus={index === 0}
                        id={index === 0 ? "code-input" : undefined}
                      />
                    ))}
                  </div>
                  
                  <p className="text-xs text-center text-muted-foreground">
                    Enter your 8-character secret code to unlock access
                  </p>
                </div>
                
                <ShimmerButton
                  shimmerColor="rgba(255, 255, 255, 0.2)"
                  shimmerSize="0.1em"
                  shimmerDuration="2s"
                  className="w-full h-10 rounded-lg bg-gradient-to-r from-[#f97316] to-[#ec4899] text-white font-medium hover:shadow-md transition-all"
                  onClick={handleVerify}
                  disabled={code.some(digit => digit === "") || isVerifying}
                >
                  {isVerifying ? (
                    <span className="flex items-center gap-2">
                      <div className="h-4 w-4 border-t-2 border-b-2 border-current rounded-full animate-spin" />
                      Verifying...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      Let me in <ArrowRight className="h-4 w-4" />
                    </span>
                  )}
                </ShimmerButton>
                
                {error && (
                  <div className="flex flex-col gap-4 items-center">
                    <div className="flex items-center gap-2 text-red-500 text-sm">
                      <AlertCircle className="h-4 w-4" />
                      <p>{error}</p>
                    </div>
                    
                    <div className="w-full h-px bg-gray-200 dark:bg-gray-800 my-1"></div>
                    
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground mb-2">
                        Don't have a valid invite code?
                      </p>
                      <Link 
                        href="https://buildthatidea.typeform.com/to/jmek3QH1" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs flex items-center justify-center gap-1 text-primary hover:underline"
                      >
                        Join our waiting list <ExternalLink className="h-3 w-3" />
                      </Link>
                    </div>
                  </div>
                )}
                
                {!error && (
                  <div className="flex flex-col items-center gap-2">
                    <p className="text-xs text-center text-muted-foreground">
                      Don't have an invite code yet?
                    </p>
                    <Link 
                      href="https://buildthatidea.typeform.com/to/jmek3QH1" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-xs flex items-center gap-1 text-primary hover:underline"
                    >
                      Join our waiting list <ExternalLink className="h-3 w-3" />
                    </Link>
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="border-t bg-muted/50 p-4">
            <Button 
              onClick={handleLogout}
              className="w-full"
              variant="ghost"
              size="sm"
            >
              <LogOut className="h-4 w-4 mr-2" /> Sign Out
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
