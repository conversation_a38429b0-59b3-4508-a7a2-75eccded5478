import { db } from './client';
import { agents, message, chat } from './schema';
import { eq, count, sum, sql, and, inArray } from 'drizzle-orm';

/**
 * Get the total number of agents created by a user
 * @param userId The ID of the user
 * @returns Promise<number> Total number of agents created by the user
 */
export async function getTotalAgentsCreatedByUser(userId: string): Promise<number> {
  console.log('Querying agents for userId:', userId);

  // // First get all agents for this user to debug
  // const allAgents = await db
  //   .select({
  //     id: agents.id,
  //     status: agents.status,
  //     userId: agents.userId,
  //     name: agents.agentName
  //   })
  //   .from(agents)
  //   .where(eq(agents.userId, userId));

  // console.log('All agents found:', JSON.stringify(allAgents, null, 2));

  // // Get active agents to debug
  // const activeAgents = await db
  //   .select({
  //     id: agents.id,
  //     status: agents.status,
  //     userId: agents.userId,
  //     name: agents.agentName
  //   })
  //   .from(agents)
  //   .where(and(
  //     eq(agents.userId, userId),
  //     eq(agents.status, 'active')
  //   ));

  // console.log('Active agents found:', JSON.stringify(activeAgents, null, 2));

  // Then do the count query
  const [result] = await db
    .select({ count: count(agents.id) })
    .from(agents)
    .where(and(
      eq(agents.userId, userId),
      eq(agents.status, 'active')
    ));

    console.log('Total agents result:', { result });
    console.log("User id:", userId)
  return Number(result.count) || 0;
}

/**
 * Get the total knowledgebase size (in bytes) for all agents created by a user
 * @param userId The ID of the user
 * @returns Promise<number> Total size in bytes
 */
export async function getTotalKnowledgebaseSize(userId: string): Promise<number> {
  // First get all agents with their files for this user
  const agentsWithFiles = await db
    .select({
      files: agents.files
    })
    .from(agents)
    .where(and(
      eq(agents.userId, userId),
      eq(agents.status, 'active')
    ));

    console.log('Total agents result:', { agentsWithFiles });
  // Calculate total size by iterating through agents and their files
  let totalSize = 0;
  for (const agent of agentsWithFiles) {
    if (!agent.files) continue;
    
    const files = Array.isArray(agent.files) ? agent.files : [];
    for (const file of files) {
      if (file && typeof file === 'object' && 'file_size' in file) {
        totalSize += Number(file.file_size) || 0;
      }
    }
  }

  console.log('Total knowledgebase size result:', { totalSize });
  return totalSize;
}

/**
 * Calculate total tokens used by a user across all their active agents' chats
 * @param userId The ID of the user
 * @returns Promise<number> Total tokens used
 */
export async function calculateTotalTokensUsed(userId: string): Promise<number> {
  try {
    // First get all active agents for this user
    const userAgents = await db
      .select({
        id: agents.id
      })
      .from(agents)
      .where(and(
        eq(agents.userId, userId),
        eq(agents.status, 'active')
      ));

    if (!userAgents.length) {
      return 0;
    }

    const agentIds = userAgents.map(agent => agent.id);

    // Get all chats for these agents
    const userChats = await db
      .select({
        id: chat.id
      })
      .from(chat)
      .where(inArray(chat.agentId, agentIds));

    if (!userChats.length) {
      return 0;
    }

    const chatIds = userChats.map(chat => chat.id);

    // Calculate total tokens from messages in these chats
    const [result] = await db
      .select({
        totalTokens: sql<number>`COALESCE(SUM(${message.tokens}), 0)::int`
      })
      .from(message)
      .where(inArray(message.chatId, chatIds));

    return result.totalTokens || 0;
  } catch (error) {
    console.error('Error calculating total tokens used:', error);
    throw error;
  }
}
