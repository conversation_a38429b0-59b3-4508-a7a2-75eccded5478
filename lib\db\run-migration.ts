import { db } from './client';
import { sql } from 'drizzle-orm';

async function runMigration() {
  try {
    console.log('Starting migration: Adding model field to agents table');
    
    // Add model field to agents table
    await db.execute(sql`ALTER TABLE agents ADD COLUMN IF NOT EXISTS model TEXT`);
    console.log('Added model column to agents table');

    // Update existing agents to move model from monetization to the new field
    await db.execute(sql`
      UPDATE agents
      SET model = monetization->>'model'
      WHERE monetization->>'model' IS NOT NULL
    `);
    console.log('Moved model values from monetization to the new model field');

    // Clear the model field from the monetization JSON
    await db.execute(sql`
      UPDATE agents
      SET monetization = jsonb_set(
        monetization,
        '{model}',
        'null'::jsonb,
        true
      ) - 'model'
      WHERE monetization->>'model' IS NOT NULL
    `);
    console.log('Removed model field from monetization JSON');

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    process.exit(0);
  }
}

runMigration();
