CREATE TABLE IF NOT EXISTS "UserSubscriptions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"agentId" uuid NOT NULL,
	"userId" uuid NOT NULL,
	"allowed_free_messages" integer NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserSubscriptions" ADD CONSTRAINT "UserSubscriptions_agentId_agents_id_fk" FOREIGN KEY ("agentId") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserSubscriptions" ADD CONSTRAINT "UserSubscriptions_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
