// /app/api/agents/process-delete-embedding/route.ts
// This route handles the actual deletion of vector embeddings stored in Upstash Vector
// Now updated to work with namespaced vectors where each agent has its own namespace
// The namespace equals the agentId, matching how embeddings are stored in process-embeddings
import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { agentKnowledgeFiles } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { Index } from '@upstash/vector';

export const maxDuration = 800;

// Configuration constants
const CONFIG = {
  REQUEST_TIMEOUT: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
} as const;

class APIError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number = 500,
    public readonly code?: string,
    public readonly cause?: unknown,
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Helper function to verify internal API requests
function verifyInternalRequest(request: Request): boolean {
  const authSecret = process.env.AUTH_SECRET;
  if (!authSecret) {
    console.warn('AUTH_SECRET not configured');
    return true; // Allow if no secret is configured
  }

  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7);
  return token === authSecret;
}

// Delete vectors from index with retry logic
async function deleteVectorsWithRetry(
  vectorIndex: Index,
  filter: string,
  namespace: string,
  maxRetries: number = CONFIG.MAX_RETRIES
): Promise<{ deleted: number }> {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await vectorIndex.delete({ filter }, { namespace });
    } catch (error) {
      console.error(`Error deleting vectors (attempt ${attempt + 1}):`, error);
      
      if (attempt === maxRetries - 1) {
        throw error;
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY * Math.pow(2, attempt)));
    }
  }
  
  throw new Error('Failed to delete vectors after all retries');
}

// Process a single file deletion
async function processFileDeletion(
  fileId: string,
  agentId: string,
  vectorInfo: { endpoint: string; token: string }
) {
  let formattedEndpoint = vectorInfo.endpoint;
  if (formattedEndpoint && !formattedEndpoint.startsWith('http')) {
    formattedEndpoint = `https://${formattedEndpoint}`;
  }

  try {
    // Initialize Upstash Vector client
    const vectorIndex = new Index({
      url: formattedEndpoint,
      token: vectorInfo.token,
    });

    console.log(`Deleting embeddings for file: ${fileId} in namespace: ${agentId}`);

    // Use metadata filtering to delete vectors associated with this file in the agent's namespace
    const deleteResult = await deleteVectorsWithRetry(
      vectorIndex,
      `fileId = '${fileId}'`,
      agentId
    );

    console.log(`Deleted ${deleteResult.deleted} vectors for file ${fileId} in namespace ${agentId}`);

    // Update file status in database to 'deleted' after successful deletion
    await db
      .update(agentKnowledgeFiles)
      .set({
        status: 'deleted',
      })
      .where(eq(agentKnowledgeFiles.id, fileId));

    return {
      fileId,
      status: 'completed',
      deletedVectors: deleteResult.deleted,
      namespace: agentId
    };
  } catch (error) {
    console.error('Error processing file deletion:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      fileId,
      agentId,
    });
    throw error;
  }
}

interface ProcessDeleteRequest {
  agentId: string;
  fileIds: string[];
  vectorInfo: {
    endpoint: string;
    token: string;
  };
}

export async function POST(request: Request) {
  try {
    // Verify this is an internal API call
    if (!verifyInternalRequest(request)) {
      console.warn('Unverified internal request, continuing anyway');
    }

    // Parse request body
    const body: ProcessDeleteRequest = await request.json();
    const { agentId, fileIds, vectorInfo } = body;

    // Validate required parameters
    if (!agentId) {
      throw new APIError('Missing required parameter: agentId', 400, 'MISSING_AGENT_ID');
    }

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      throw new APIError('Missing or invalid required parameter: fileIds', 400, 'INVALID_FILE_IDS');
    }

    if (!vectorInfo || !vectorInfo.endpoint || !vectorInfo.token) {
      throw new APIError('Missing or invalid required parameter: vectorInfo', 400, 'INVALID_VECTOR_INFO');
    }

    console.log(`Starting parallel deletion for ${fileIds.length} files`);

    // Process all file deletions in parallel, ensuring we use the agent's namespace
    const deletionPromises = fileIds.map(async (fileId) => {
      try {
        // Process file deletion - passing agentId to ensure namespace is used correctly
        const result = await processFileDeletion(fileId, agentId, vectorInfo);
        console.log(`✅ Successfully deleted embeddings for file: ${fileId} in namespace: ${agentId}`);
        return result;
      } catch (error) {
        console.error(`❌ Error deleting embeddings for file ${fileId} in namespace: ${agentId}:`, error);

        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { 
          fileId, 
          status: 'failed', 
          error: errorMessage,
          namespace: agentId 
        };
      }
    });

    // Wait for all deletions to complete
    const results = await Promise.all(deletionPromises);

    // Summarize results
    const summary = {
      total: results.length,
      completed: results.filter(r => r.status === 'completed').length,
      failed: results.filter(r => r.status === 'failed').length,
    };

    console.log('Deletion completed:', summary);

    return NextResponse.json({
      message: 'Embeddings deletion completed',
      agentId,
      timestamp: new Date().toISOString(),
      summary,
      results,
      namespace: agentId, // Include namespace in the response for clarity
      success: summary.failed === 0,
    });
  } catch (error) {
    console.error('❌ Error in deletion processing route:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    if (error instanceof APIError) {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          success: false,
          timestamp: new Date().toISOString()
        },
        { status: error.statusCode },
      );
    }

    return NextResponse.json(
      {
        error: 'Processing failed: ' + errorMessage,
        code: 'INTERNAL_ERROR',
        success: false,
        timestamp: new Date().toISOString()
      },
      { status: 500 },
    );
  }
}
