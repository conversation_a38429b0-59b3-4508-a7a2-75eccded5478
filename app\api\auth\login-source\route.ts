import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { source } = await req.json();
    
    if (!source) {
      return NextResponse.json({ error: 'Missing source parameter' }, { status: 400 });
    }

    // Set a cookie to track where the user started their login journey
    const response = NextResponse.json({ success: true });
    response.cookies.set('login_source', source, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60, // 1 hour expiry - enough time to complete the login flow
      path: '/',
    });
    
    return response;
  } catch (error) {
    console.error('Error setting login source:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
