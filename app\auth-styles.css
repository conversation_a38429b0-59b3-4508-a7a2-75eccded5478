/* Auth pages styling based on AI Agents Launchpad design system */

.auth-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom, #ffffff, #f9f5ff);
}

.auth-card {
  width: 100%;
  max-width: 28rem;
  overflow: hidden;
  border-radius: 1rem;
  border: 1px solid #f3f3f3;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  background-color: white;
  padding: 2rem;
}

.auth-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  text-align: center;
  margin-bottom: 2rem;
}

.auth-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(to right, #f97316, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.auth-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.auth-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.auth-input {
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.auth-input:focus {
  border-color: #f97316;
  outline: none;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

.auth-button {
  position: relative;
  display: flex;
  height: 2.5rem;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  background: linear-gradient(to right, #f97316, #ec4899);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  transition: all 150ms ease;
}

.auth-button:hover {
  background: linear-gradient(to right, #ea580c, #db2777);
}

.auth-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.5);
}

.auth-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.auth-link {
  font-weight: 500;
  color: #f97316;
}

.auth-link:hover {
  color: #ea580c;
  text-decoration: underline;
}

.auth-footer {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Shimmer effect for buttons */
.auth-button-shimmer {
  position: relative;
  overflow: hidden;
}

.auth-button-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}
