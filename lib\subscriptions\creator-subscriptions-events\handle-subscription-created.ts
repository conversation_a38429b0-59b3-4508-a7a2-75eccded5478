import { findPlanByName, createSubscription, createSubscriptionBalance } from '@/lib/db/subscription-utils';
import { trackTrialStarted } from '@/lib/user-journey';
import { db } from '@/lib/db';
import { user } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function handleCreatedSubscriptionForCreator(
  userId: string,
  productName: string,
  expiryDate: string,
  balanceExpiryDate: string,
) {

  const plan = await findPlanByName(productName);
  if (!plan) {
    throw new Error('Plan not found');
  }

  // Get user email for ConvertKit tracking
  const userRecord = await db
    .select({ email: user.email })
    .from(user)
    .where(eq(user.id, userId))
    .limit(1)
    .then(rows => rows[0]);

  const newSubscription = await createSubscription({
    userId,
    planId: plan.id,
    subscription_type: 'trial',
    expiry_date: expiryDate,
  });

  await createSubscriptionBalance({
    userId,
    subscription_id: newSubscription.id,
    plan,
    expiry_date: balanceExpiryDate
  });

  // Track the trial in ConvertKit if we have the user's email
  if (userRecord?.email) {
    try {
      await trackTrialStarted(userRecord.email);
      console.log(`Added trial tag in ConvertKit for user: ${userRecord.email}`);
    } catch (error) {
      console.error('Error adding ConvertKit trial tag:', error);
      // Don't throw here - we don't want to fail the subscription creation if ConvertKit fails
    }
  }

  return newSubscription;
}

