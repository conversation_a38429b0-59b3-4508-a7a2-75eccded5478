const { sql } = require('drizzle-orm');
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL environment variable is not set');
}

const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

/**
 * Migration to ensure Chat.agentId column exists and has correct name
 */
async function fixChatAgentIdColumn() {
  console.log('🔄 Checking Chat.agentId column...');
  
  try {
    // First check if agent_id exists
    try {
      await db.execute(sql`
        ALTER TABLE "Chat"
        RENAME COLUMN "agent_id" TO "agentId";
      `);
      console.log('✅ Renamed agent_id to agentId');
    } catch (error) {
      // If agent_id doesn't exist, check if agentId exists
      try {
        await db.execute(sql`
          SELECT "agentId" FROM "Chat" LIMIT 1;
        `);
        console.log('✅ Column agentId already exists');
      } catch (error) {
        // If neither exists, create agentId
        await db.execute(sql`
          ALTER TABLE "Chat"
          ADD COLUMN "agentId" UUID NOT NULL REFERENCES "agents"(id);
        `);
        console.log('✅ Created agentId column');
      }
    }
  } catch (error) {
    console.error('❌ Failed to fix column:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  fixChatAgentIdColumn()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
