-- Create agent_profiles table
CREATE TABLE IF NOT EXISTS "agent_profiles" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "agent_id" UUID NOT NULL REFERENCES "agents"("id") ON DELETE CASCADE,
  "rating" REAL DEFAULT 4.5,
  "total_chats" INTEGER DEFAULT 0,
  "datasets" INTEGER DEFAULT 0,
  "user_ratings" INTEGER DEFAULT 0,
  "features" JSONB,
  "created_at" TIMESTAMP DEFAULT now(),
  "updated_at" TIMESTAMP DEFAULT now()
);

-- Create index for faster lookups by agent_id
CREATE INDEX IF NOT EXISTS "agent_profiles_agent_id_idx" ON "agent_profiles" ("agent_id");
