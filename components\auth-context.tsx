'use client';

import React, {
  createContext,
  useContext,
  useState,
  type ReactNode,
  useCallback,
  useEffect,
  useRef,
} from 'react';
import { registerContextReset } from '@/lib/reset-app-state';
import { AuthModal } from './auth-modal';
import { useSession } from 'next-auth/react';

interface AuthContextType {
  showAuthModal: (onSuccess?: () => void) => void;
  hideAuthModal: () => void;
  isAuthModalOpen: boolean;
  refreshSession: () => Promise<boolean>;
  authState: {
    isAuthenticated: boolean;
    lastAuthTime: number;
  };
  triggerAuthStateChange: () => void;
  resetAuthState: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const [successCallback, setSuccessCallback] = useState<(() => void) | null>(
    null,
  );

  // Use this for session operations, not for tracking state
  const { update: updateSession } = useSession();

  // Get session data but we won't use it to directly set auth state
  const { data: session } = useSession();

  // Keep track of our last update to avoid loops
  const lastUpdateRef = useRef<number>(0);

  // Auth state with initialization
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    lastAuthTime: 0,
  });
  
  // Function to reset auth state
  const resetAuthState = useCallback(() => {
    setIsOpen(false);
    setSuccessCallback(null);
    setAuthState({
      isAuthenticated: false,
      lastAuthTime: 0,
    });
    lastUpdateRef.current = 0;
    console.log('Auth context state reset');
  }, []);
  
  // Register the reset function
  useEffect(() => {
    registerContextReset(resetAuthState);
  }, [resetAuthState]);

  // One-time initialization when session loads initially
  useEffect(() => {
    // Only run if session is defined (not on first render) and we haven't updated yet
    if (session !== undefined && authState.lastAuthTime === 0) {
      setAuthState({
        isAuthenticated: !!session?.user,
        lastAuthTime: Date.now(),
      });
    }
  }, [session, authState.lastAuthTime]); // This only runs for initialization

  // Function to manually trigger auth state change WITHOUT causing a re-render loop
  const triggerAuthStateChange = useCallback(() => {
    // Generate a new timestamp
    const now = Date.now();

    // Only update if this is a new event (at least 100ms since last update)
    if (now - lastUpdateRef.current > 100) {
      lastUpdateRef.current = now;
      setAuthState({
        isAuthenticated: !!session?.user,
        lastAuthTime: now,
      });
    }
  }, [session?.user]);

  const showAuthModal = useCallback((onSuccess?: () => void) => {
    if (onSuccess && typeof onSuccess === 'function') {
      setSuccessCallback(() => onSuccess);
    } else {
      setSuccessCallback(null);
    }
    setIsOpen(true);
  }, []);

  const hideAuthModal = useCallback(() => {
    setIsOpen(false);
  }, []);

  // This function manually refreshes the session data
  const refreshSession = useCallback(async () => {
    if (typeof window !== 'undefined') {
      try {
        // Using next-auth's update method to refresh the session
        await updateSession();
        // Don't trigger auth state change here to avoid infinite loops
        return true;
      } catch (error) {
        console.error('Error refreshing session:', error);
        return false;
      }
    }
    return false;
  }, [updateSession]);

  const handleSuccess = useCallback(async () => {
    try {
      // Update the session data
      await updateSession();

      // Trigger auth state change to notify components
      triggerAuthStateChange();

      // Execute callback if provided
      if (successCallback && typeof successCallback === 'function') {
        successCallback();
        setSuccessCallback(null);
      }

      // Close the modal
      hideAuthModal();

      // No need for window.location.reload() anymore
      // Components will re-render based on the authState change
    } catch (error) {
      console.error('Error in auth success handler:', error);
      hideAuthModal();
    }
  }, [successCallback, hideAuthModal, updateSession, triggerAuthStateChange]);

  return (
    <AuthContext.Provider
      value={{
        showAuthModal,
        hideAuthModal,
        isAuthModalOpen: isOpen,
        refreshSession,
        authState,
        triggerAuthStateChange,
        resetAuthState,
      }}
    >
      {children}
      <AuthModal
        isOpen={isOpen}
        onClose={hideAuthModal}
        onSuccess={handleSuccess}
      />
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
