DO $$ 
BEGIN
    -- First, add the new userId column
    ALTER TABLE "agents" ADD COLUMN "userId" UUID REFERENCES "User"(id);

    -- Copy data from email to userId by matching with User table
    UPDATE "agents" a
    SET "userId" = u.id
    FROM "User" u
    WHERE a.email = u.email;

    -- Make userId NOT NULL after data migration
    ALTER TABLE "agents" ALTER COLUMN "userId" SET NOT NULL;

    -- Drop the email column
    ALTER TABLE "agents" DROP COLUMN "email";
END $$;
