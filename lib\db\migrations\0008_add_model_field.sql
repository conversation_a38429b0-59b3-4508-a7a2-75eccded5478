-- Add model field to agents table
ALTER TABLE agents ADD COLUMN IF NOT EXISTS model TEXT;

-- Update existing agents to move model from monetization to the new field
UPDATE agents
SET model = monetization->>'model'
WHERE monetization->>'model' IS NOT NULL;

-- Clear the model field from the monetization JSON
UPDATE agents
SET monetization = jsonb_set(
  monetization,
  '{model}',
  'null'::jsonb,
  true
) - 'model'
WHERE monetization->>'model' IS NOT NULL;
