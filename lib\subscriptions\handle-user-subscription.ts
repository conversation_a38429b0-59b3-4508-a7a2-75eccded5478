import { db } from '@/lib/db';
import { userPayments, userSubscriptions } from '@/lib/db/schema';
import { and, eq } from 'drizzle-orm';

interface UserSubscriptionMetadata {
  userId: string;
  agentId: string;
  expiryDate?: string;
  subscriptionType?: string;
}

export async function handleUserSubscription(
  metadata: UserSubscriptionMetadata,
  amountTotal: number | null
) {
  console.log('Starting handleUserSubscription:', {
    metadata,
    amountTotal,
  });
  const { userId, agentId, expiryDate, subscriptionType } = metadata;

  console.log('Finding user subscription:', { userId, agentId, amountTotal, expiryDate, subscriptionType });
  
  // Try to find existing subscription
  const [existingSubscription] = await db
    .select()
    .from(userSubscriptions)
    .where(and(eq(userSubscriptions.userId, userId), eq(userSubscriptions.agentId, agentId)));

  // Create or update subscription
  let updatedSubscription;
  if (existingSubscription) {
    console.log('Updating existing subscription:', existingSubscription);
    // Update existing subscription to have unlimited messages
    [updatedSubscription] = await db
      .update(userSubscriptions)
      .set({
        allowedFreeMessages: 999999, // Using a very large number to represent "unlimited"
        isPaid: true,
        updatedAt: new Date(),
        expiryDate: expiryDate ? new Date(expiryDate) : new Date(),
        subscriptionType: metadata.subscriptionType,
      })
      .where(and(eq(userSubscriptions.userId, userId), eq(userSubscriptions.agentId, agentId)))
      .returning();
  } else {
    console.log('Creating new subscription:', { userId, agentId, expiryDate });
    // Create new subscription
    [updatedSubscription] = await db
      .insert(userSubscriptions)
      .values({
        userId,
        agentId,
        allowedFreeMessages: 999999,
        isPaid: true,
        subscriptionType: metadata.subscriptionType,
        expiryDate: expiryDate ? new Date(expiryDate) : new Date()
      })
      .returning();
  }

  if (amountTotal && amountTotal > 0) {
    // Record the payment in userPayments table
    console.log('Recording payment in userPayments:', {
      userId,
      subscriptionId: updatedSubscription.id,
      amount: amountTotal / 100
    });

    await db.insert(userPayments).values({
      userId,
      subscriptionId: updatedSubscription.id,
      amount: amountTotal / 100,
    });
  } else {
    console.log('Skipping payment record - no amount or zero amount:', { amountTotal });
  }

  console.log('User subscription updated:', {
    remainingMessages: updatedSubscription.allowedFreeMessages,
    isPaid: updatedSubscription.isPaid,
    expiryDate: updatedSubscription.expiryDate
  });

  return updatedSubscription;
}
