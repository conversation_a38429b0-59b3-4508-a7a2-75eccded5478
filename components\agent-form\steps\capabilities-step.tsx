'use client';

import React, { useEffect, useState, useCallback } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { FormValues, FileEntry } from '../types';
import { FileUp, Globe, Upload, AlertCircle, Loader2, Check, X } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import WebsiteCrawlInput from '../website-crawl-input';

interface CapabilitiesStepProps {
  form: UseFormReturn<FormValues, any, FormValues>;
  updateValidity?: (isValid: boolean) => void;
  // Add these new props
  draftAgentId?: string | null;
  onSaveDraft?: () => Promise<any>;
  setIsUploadingFiles?: (isUploading: boolean) => void;
  isEditMode?: boolean; // Add isEditMode prop
  planLimits?: any; // Add planLimits prop if needed
}

export function CapabilitiesStep({
  form,
  updateValidity,
  draftAgentId,
  onSaveDraft,
  setIsUploadingFiles,
  isEditMode = false, // Default to false for backward compatibility
  planLimits,
}: CapabilitiesStepProps) {
  
  // Helper function to get agent ID from various sources
  const getAgentId = useCallback(async (): Promise<string | null> => {
    // Try prop first
    if (draftAgentId) {
      return draftAgentId;
    }
    // Try localStorage
    const localStorageId = localStorage.getItem('draftAgentId');
    if (localStorageId) {return localStorageId;}
    // Try to get from window object (for create page)
    const windowAgentId = (window as any).draftAgentId;
    if (windowAgentId) {
      return windowAgentId;
    }
    
    // Try calling onSaveDraft if available
    if (onSaveDraft) {
      try {
        const draftAgent = await onSaveDraft();
        if (draftAgent?.id) {
          // Store in localStorage for future use
          localStorage.setItem('draftAgentId', draftAgent.id);
          return draftAgent.id;
        }
      } catch (error) {
        console.error('[AGENT-ID] Failed to save draft:', error);
      }
    }
    
    console.error('[AGENT-ID] No agent ID found from any source');
    return null;
  }, [draftAgentId, onSaveDraft]);
  const [files, setFiles] = useState<File[]>([]);
  const [hasFiles, setHasFiles] = useState<boolean>(false);
  const [fileUploadError, setFileUploadError] = useState<string | null>(null);
  
  // Add new state for real file upload functionality
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isDeletingFile, setIsDeletingFile] = useState<string | null>(null);
  const [fileToDelete, setFileToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [fileSizeError, setFileSizeError] = useState<string | null>(null);

  // Constants for file size limits
  const MAX_FILE_SIZE_HOBBY = 10 * 1024 * 1024; // 10MB for hobby plan
  const MAX_FILE_SIZE_PRO = 100 * 1024 * 1024; // 100MB for pro plan

  // Helper function to format file size
  const formatFileSize = (bytes: number | string): string => {
    const size = typeof bytes === 'string' ? Number.parseInt(bytes) : bytes;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  // Calculate total size of all files
  const calculateTotalSize = (files: FileEntry[]): string => {
    if (!files || files.length === 0) return '0 B';
    
    const totalBytes = files.reduce((total: number, file: FileEntry) => {
      const fileSize = file.size ? (typeof file.size === 'string' ? Number.parseInt(file.size, 10) : file.size) : 0;
      return total + fileSize;
    }, 0);
    
    return formatFileSize(totalBytes);
  };

  // Real file upload implementation with progress tracking
  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      event.preventDefault();

      const files = event.target.files;
      if (!files || files.length === 0) return;

      // Clear any previous file size errors
      setFileSizeError(null);

      // Calculate total size of existing and new files
      const existingFiles = form.getValues('files') || [];
      const existingFilesSize = existingFiles.reduce((total: number, file: FileEntry) => total + (file.size || 0), 0);
      const newFilesSize = Array.from(files).reduce((total: number, file: File) => total + file.size, 0);
      const totalSize = existingFilesSize + newFilesSize;
      
      // Check plan and enforce appropriate file size limit
      // In edit mode, use current plan limits regardless of original agent plan
      const isHobbyPlan = planLimits?.plan?.name?.toLowerCase() === 'hobby' || !planLimits?.plan;
      const isProPlan = planLimits?.plan?.name?.toLowerCase() === 'pro';
      const isEnterprisePlan = planLimits?.plan?.name?.toLowerCase() === 'enterprise';
      
      // For edit mode, always use current plan limits
      if (!isEditMode) {
        // Create mode: enforce plan limits normally
        if (isHobbyPlan && totalSize > MAX_FILE_SIZE_HOBBY) {
          setFileSizeError(`Hobby plan only allows up to 10MB of files. Please upgrade to Pro for larger file uploads.`);
          event.target.value = '';
          return;
        }
        
        if (isProPlan && totalSize > MAX_FILE_SIZE_PRO) {
          setFileSizeError(`Pro plan only allows up to 100MB of files. Please <a href="https://buildthatidea.typeform.com/to/dX0HyMyI" target="_blank" rel="noopener noreferrer" style="text-decoration: underline;">contact us</a> to upgrade to Enterprise plan for larger file uploads.`);
          event.target.value = '';
          return;
        }
      } else {
        // Edit mode: use current plan limits, allowing upgrades
        if (isHobbyPlan && totalSize > MAX_FILE_SIZE_HOBBY) {
          setFileSizeError(`Your current Hobby plan only allows up to 10MB of files. Please upgrade to Pro for larger file uploads.`);
          event.target.value = '';
          return;
        }
        
        if (isProPlan && totalSize > MAX_FILE_SIZE_PRO) {
          setFileSizeError(`Your current Pro plan allows up to 100MB of files. Please <a href="https://buildthatidea.typeform.com/to/dX0HyMyI" target="_blank" rel="noopener noreferrer" style="text-decoration: underline;">contact us</a> to upgrade to Enterprise plan for larger file uploads.`);
          event.target.value = '';
          return;
        }
        
        // Enterprise or no plan restrictions in edit mode
        if (isEnterprisePlan) {
          // Enterprise plan should have no file size restrictions, or very high limits
          // Add enterprise limits here if needed
        }
      }

      setIsUploading(true);
      // Update parent component state if available
      if (setIsUploadingFiles) {
        setIsUploadingFiles(true);
      }

      try {
        // Get agent ID using our helper function
        const agentId = await getAgentId();
        if (!agentId) {
          toast.error('Please save your agent details first before uploading files.');
          event.target.value = '';
          setIsUploading(false);
          if (setIsUploadingFiles) {
            setIsUploadingFiles(false);
          }
          return;
        }
        const newProgressState: Record<string, number> = {};

        const tempFileEntries: FileEntry[] = Array.from(files).map((file) => {
          const id = crypto.randomUUID();
          newProgressState[id] = 0;

          return {
            id,
            name: file.name,
            status: 'uploading' as const,
            size: file.size,
            progress: 0,
            file,
          };
        });

        setUploadProgress((prev) => ({ ...prev, ...newProgressState }));

        const existingFiles = form.getValues('files') || [];
        form.setValue('files', [...existingFiles, ...tempFileEntries]);

        const uploadPromises = tempFileEntries.map(async (fileEntry) => {
          try {
            // Special handling for markdown files - ensure correct MIME type
            let fileType = fileEntry.file?.type;
            if (fileEntry.file?.name.endsWith('.md') && (!fileType || fileType === '')) {
              fileType = 'text/markdown';
            }
            
            const requestBody = {
              fileName: fileEntry.file?.name,
              fileType: fileType,
              fileSize: fileEntry.file?.size,
              agentId: agentId,
            };
            
            const presignedUrlResponse = await fetch('/api/agents/generate-presigned-url', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(requestBody),
            });

            if (!presignedUrlResponse.ok) {
              const errorText = await presignedUrlResponse.text();
              throw new Error(`Failed to get presigned URL: ${presignedUrlResponse.status} - ${errorText}`);
            }

            const presignedData = await presignedUrlResponse.json();
            const { presignedUrl, fileUrl } = presignedData;

            return new Promise<{
              fileEntry: FileEntry;
              response: { url: string; status: 'completed' | 'error'; name: string; error?: string; vectorIndexGenerated?: boolean; };
            }>((resolve, reject) => {
              const xhr = new XMLHttpRequest();

              xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                  const percentComplete = Math.round((event.loaded / event.total) * 100);
                  
                  setUploadProgress((prev) => ({
                    ...prev,
                    [fileEntry.id]: percentComplete,
                  }));
                }
              };

              xhr.onload = () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                  
                  setUploadProgress((prev) => ({
                    ...prev,
                    [fileEntry.id]: 100,
                  }));
                  
                  // Successfully uploaded to S3, resolve with file info
                  resolve({
                    fileEntry,
                    response: {
                      url: fileUrl,
                      status: 'completed',
                      name: fileEntry.name,
                      vectorIndexGenerated: false,
                    },
                  });
                } else {
                  resolve({
                    fileEntry,
                    response: {
                      url: '',
                      status: 'error',
                      name: fileEntry.name,
                      error: `Failed to upload to S3: ${xhr.status} ${xhr.statusText}`,
                    },
                  });
                }
              };

              xhr.onerror = () => {
                resolve({
                  fileEntry,
                  response: {
                    url: '',
                    status: 'error',
                    name: fileEntry.name,
                    error: 'Network error during upload',
                  },
                });
              };

              xhr.onabort = () => {
                resolve({
                  fileEntry,
                  response: {
                    url: '',
                    status: 'error',
                    name: fileEntry.name,
                    error: 'Upload was aborted',
                  },
                });
              };

              xhr.open('PUT', presignedUrl);
              xhr.setRequestHeader('Content-Type', fileEntry.file?.type || 'application/octet-stream');
              xhr.send(fileEntry.file);
            });
          } catch (error) {
            return {
              fileEntry,
              response: {
                status: 'error' as const,
                name: fileEntry.name,
                error: error instanceof Error ? error.message : 'Unknown error',
                url: '', 
                vectorIndexGenerated: false, 
              },
            };
          }
        });

        const results = await Promise.all(uploadPromises);

        const completedFiles = form.getValues('files');

        const needsUpdate = results.some(result => {
          const file = completedFiles.find(f => f.id === result.fileEntry.id);
          return file && file.status === 'uploading';
        });

        if (needsUpdate) {
          const finalFiles = completedFiles.map((file) => {
            const result = results.find((r) => r.fileEntry.id === file.id);
            if (result) {
              const uploadedFile = result.response;

              return {
                ...file,
                status: uploadedFile.status as 'completed' | 'error',
                url: uploadedFile.url,
                error: uploadedFile.error,
                progress: 100,
              };
            }
            return file;
          });

          form.setValue('files', finalFiles);

          const newProgressState = { ...uploadProgress };
          results.forEach(result => {
            delete newProgressState[result.fileEntry.id];
          });
          setUploadProgress(newProgressState);
          
          // Register all completed files with the upload-knowledgebase endpoint
          try {
            const successfulUploads = results
              .filter(r => r.response.status === 'completed' && r.response.url)
              .map(r => ({
                name: r.response.name,
                url: r.response.url,
                type: r.fileEntry.file?.type,
                size: r.fileEntry.file?.size?.toString(),
              }));
              
            if (successfulUploads.length > 0) {
              const knowledgebaseResponse = await fetch('/api/agents/upload-knowledgebase', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  agentId: agentId,
                  files: successfulUploads,
                }),
              });
              
              if (!knowledgebaseResponse.ok) {
                throw new Error(`Failed to register files with API: ${knowledgebaseResponse.status}`);
              }
              
              const knowledgebaseData = await knowledgebaseResponse.json();
              
              // Check if vector index was generated
              if (knowledgebaseData.vectorIndexAvailable) {
                toast.success('Knowledge base index created successfully');
              } else {
                toast.success('Files uploaded successfully');
              }
            }
          } catch (error) {
            console.error('Error registering files with knowledge base:', error);
            toast.error('Files uploaded but could not be registered with knowledge base');
          }
        }
      } catch (error) {
        console.error('[FILE-UPLOAD] Error uploading files:', error);
        toast.error('Failed to upload files');

        const failedFiles = form.getValues('files');
        const updatedFiles = failedFiles.map((file) => {
          if (file.status === 'uploading') {
            return {
              ...file,
              status: 'error' as const,
              error: 'Upload failed',
            };
          }
          return file;
        });
        form.setValue('files', updatedFiles);
      } finally {
        setIsUploading(false);
        if (setIsUploadingFiles) {
          setIsUploadingFiles(false);
        }
        event.target.value = '';
      }
    },
    [form, getAgentId, uploadProgress, setIsUploadingFiles]
  );

  // Remove a file
  const removeFile = useCallback(
    async (fileId: string) => {
      const currentFiles = form.getValues('files') || [];
      const fileToRemove = currentFiles.find(file => file.id === fileId);

      setIsDeletingFile(fileId);

      try {
        // Remove from UI immediately for better UX
        const updatedFiles = currentFiles.filter((file) => file.id !== fileId);
        form.setValue('files', updatedFiles);

        // Clean up upload progress state
        setUploadProgress((prev) => {
          const newState = { ...prev };
          delete newState[fileId];
          return newState;
        });

        // Call API to update database status (required for both create and edit flows)
        if (fileToRemove?.url && fileToRemove.status === 'completed') {
          try {
            const agentId = await getAgentId();
            
            if (agentId) {
              // Determine if we're on the create page
              const isCreatePage = window.location.pathname.includes('/create');
              
              const response = await fetch('/api/agents/delete-knowledgebase-file', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  agentId: agentId,
                  fileUrl: fileToRemove.url,
                  isCreatePage: isCreatePage,
                }),
              });

              if (response.ok) {
                toast.success('File removed successfully');
              } else {
                console.error('Server cleanup failed for file:', fileToRemove.name);
                toast.success('File removed from UI (server cleanup may have failed)');
              }
            } else {
              // No agent ID available - this shouldn't happen if file was uploaded successfully
              console.warn('No agent ID available for file cleanup:', fileToRemove.name);
              toast.success('File removed from UI (unable to update server records)');
            }
          } catch (error) {
            console.error('Error during server cleanup:', error);
            toast.success('File removed from UI (server cleanup failed)');
          }
        } else {
          toast.success('File removed successfully');
        }

      } catch (error) {
        console.error('Error removing file:', error);
        toast.error('Failed to remove file');
        
        // Revert UI changes on unexpected errors
        form.setValue('files', currentFiles);
      } finally {
        setIsDeletingFile(null);
        setFileToDelete(null);
        setShowDeleteConfirm(false);
      }
    },
    [form, getAgentId]
  );

  // Handle file drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      // Create a synthetic event to trigger file upload
      const syntheticEvent = {
        preventDefault: () => {},
        target: { files: e.dataTransfer.files, value: '' }
      } as React.ChangeEvent<HTMLInputElement>;
      
      handleFileUpload(syntheticEvent);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Update form files when progress changes
  useEffect(() => {
    if (Object.keys(uploadProgress).length > 0) {
      const currentFiles = form.getValues('files') || [];

      const hasProgressChanged = currentFiles.some(file =>
        file.progress !== (uploadProgress[file.id] || file.progress)
      );

      if (hasProgressChanged) {
        const updatedFiles = currentFiles.map((file) => ({
          ...file,
          progress: uploadProgress[file.id] || file.progress,
        }));

        form.setValue('files', updatedFiles, { shouldDirty: true });
      }
    }
  }, [uploadProgress, form]);

  // Update form values and validity when files change
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'files') {
        const currentFiles = value.files || [];
        setHasFiles(currentFiles.length > 0);
      }
    });

    // Run initial validation only once
    const currentFiles = form.getValues('files') || [];
    setHasFiles(currentFiles.length > 0);

    return () => subscription.unsubscribe();
  }, [form]);

  // Separate effect for validity updates to prevent infinite loops
  useEffect(() => {
    if (updateValidity) {
      updateValidity(true); // CapabilitiesStep is always valid
    }
  }, [updateValidity]);

  return (
    <div className="space-y-8">
      <div className="space-y-6">
        {/* <h3 className="text-lg font-medium">Agent Capabilities</h3>
        <p className="text-sm text-muted-foreground">Configure additional capabilities for your agent</p>
         */}
        <div className="grid gap-8 mt-4">
          {/* File Upload Capability */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <FileUp className="size-5 text-muted-foreground" />
              <h4 className="text-sm font-medium">File Upload</h4>
            </div>
            <p className="text-sm text-muted-foreground">
              Upload files for your agent to process and analyze
            </p>

            <div className="space-y-4">
              {/* File upload dropzone */}
              <div
                className={cn(
                  'border-2 border-dashed rounded-lg p-6 transition-all cursor-pointer',
                  fileUploadError
                    ? 'border-destructive/50 bg-destructive/5'
                    : 'border-muted-foreground/20 hover:border-primary/50 hover:bg-primary/5',
                )}
                onClick={() =>
                  document.getElementById('file-upload-input')?.click()
                }
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    document.getElementById('file-upload-input')?.click();
                  }
                }}
              >
                <div className="flex flex-col items-center justify-center space-y-2 text-center">
                  <div className="rounded-full bg-primary/10 p-2">
                    {isUploading ? (
                      <Loader2 className="size-5 text-primary animate-spin" />
                    ) : (
                      <Upload className="size-5 text-primary" />
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium">
                      {isUploading ? 'Uploading files...' : 'Click to upload sample files'}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      or drag and drop
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    PDF, TXT, DOCX, CSV, JSON (max {planLimits?.plan?.name?.toLowerCase() === 'pro' ? '100MB' : planLimits?.plan?.name?.toLowerCase() === 'enterprise' ? 'unlimited' : '10MB'} total)
                  </div>
                </div>
                <input
                  id="file-upload-input"
                  type="file"
                  accept=".pdf,.txt,.docx,.csv,.json,.md"
                  className="hidden"
                  multiple
                  onChange={handleFileUpload}
                  disabled={isUploading}
                />
              </div>

              {/* File Size Error Message */}
              {fileSizeError && (
                <div className="flex items-center text-xs text-destructive">
                  <AlertCircle className="size-3.5 mr-1.5" />
                  <div dangerouslySetInnerHTML={{ __html: fileSizeError }} />
                </div>
              )}

              {/* Uploaded Files List */}
              {form.watch('files')?.length > 0 && (
                <div className="space-y-1 mt-2">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-xs font-medium">Uploaded Files</h3>
                    <span className="text-xs text-muted-foreground">
                      {form.watch('files').length} file
                      {form.watch('files').length !== 1 ? 's' : ''} 
                      ({calculateTotalSize(form.watch('files'))})
                    </span>
                  </div>
                  {form.watch('files').map((file: FileEntry) => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between p-2 bg-background rounded-md border border-border"
                    >
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <div className="bg-primary/10 p-1.5 rounded-md">
                          <svg
                            className="size-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                            />
                          </svg>
                        </div>

                        <div className="flex flex-col min-w-0 flex-1">
                          <div className="flex justify-between items-center">
                            <span className="text-xs font-medium truncate">
                              {file.name}
                            </span>
                            <span className="text-xs text-muted-foreground ml-2 whitespace-nowrap">
                              {file.size && formatFileSize(file.size)}
                            </span>
                          </div>

                          {file.status === 'uploading' && (
                            <div className="w-full mt-0.5">
                              <div className="h-1 bg-muted rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-primary transition-all duration-300"
                                  style={{
                                    width: `${uploadProgress[file.id] || file.progress || 0}%`,
                                  }}
                                />
                              </div>
                              <div className="flex items-center justify-between mt-1">
                                <span className="text-xs text-muted-foreground">
                                  Uploading...{' '}
                                  {(uploadProgress[file.id] || file.progress || 0).toFixed(0)}%
                                </span>
                              </div>
                            </div>
                          )}

                          {file.status === 'error' && (
                            <span className="text-xs text-destructive flex items-center gap-1">
                              <X className="size-3" />
                              {file.error || 'Error'}
                            </span>
                          )}

                          {file.status === 'completed' && (
                            <span className="text-xs text-green-500 flex items-center gap-1">
                              <Check className="size-3" />
                              Complete
                            </span>
                          )}
                        </div>
                      </div>

                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setFileToDelete(file.id);
                          setShowDeleteConfirm(true);
                        }}
                        disabled={file.status === 'uploading' || isDeletingFile === file.id}
                        className="h-6 w-6 rounded-full"
                      >
                        {isDeletingFile === file.id ? (
                          <Loader2 className="size-3 animate-spin" />
                        ) : (
                          <X className="size-3" />
                        )}
                        <span className="sr-only">Remove file</span>
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                Your agent will use these files to learn and answer questions
              </div>
            </div>
          </div>

          {/* Website Crawling Capability */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Globe className="size-5 text-muted-foreground" />
              <h4 className="text-sm font-medium">Website Crawling</h4>
            </div>
            <p className="text-sm text-muted-foreground">
            Enter a website domain, click 'Fetch Links,' and select the links for your agent to scrape and analyze.
            </p>
            <WebsiteCrawlInput
              form={form}
              onSaveDraft={onSaveDraft || (async () => {
                toast.success('Draft saved successfully');
                return Promise.resolve();
              })}
              isEditMode={isEditMode} // Pass through isEditMode prop
            />
          </div>
        </div>
      </div>

      {/* Confirmation Dialog for File Deletion */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => {
          if (fileToDelete) {
            removeFile(fileToDelete);
          }
        }}
        title="Remove File"
        description={
          "Are you sure you want to remove this file? This action cannot be undone."
        }
        confirmText={isDeletingFile ? "Removing..." : "Remove"}
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  );
}
