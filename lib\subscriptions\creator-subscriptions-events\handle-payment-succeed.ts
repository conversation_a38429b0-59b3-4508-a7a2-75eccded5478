import { db } from '@/lib/db';
import { creatorPayments, creatorSubscriptions, user } from '@/lib/db/schema';
import { eq, and, ne } from 'drizzle-orm';
import { findPlanByName, findExistingSubscription, createSubscription, updateSubscription, createCreatorPayment, createSubscriptionBalance } from '@/lib/db/subscription-utils';
import { add } from 'date-fns';
import { trackHobbySubscription, trackProSubscription } from '@/lib/user-journey';

interface CreatorSubscriptionMetadata {
  userId: string;
  productName: string;
  subscriptionType: 'trial' | 'monthly' | 'yearly';
}

export async function handlePaymentSucceedForCreator(
  metadata: CreatorSubscriptionMetadata,
  amountTotal: number | null,
  isTrial: boolean,
  expiryDate: string
) {
  console.log('expiryDate')
  console.log(expiryDate)
  const { userId, productName } = metadata;
  const finalAmountPaid = amountTotal ? amountTotal / 100 : 0;
  // Convert Unix timestamp to YYYY-MM-DD format
  let balanceExpiryDate;
  console.log("handlePaymentSucceedForCreator", metadata, amountTotal, isTrial)
  try {
    // Get plan details to set initial balance
    const plan = await findPlanByName(productName);

    if (!plan) {
      throw new Error('Plan not found');
    }

    console.log('expiryDate', expiryDate)
    let subscription = await findExistingSubscription(userId, plan.id, 'created');
    if (!subscription) {
      console.log("No subscription found for user, creating a new one....", userId);
      // Calculate expiry date based on subscription type
      subscription = await createSubscription({
        userId,
        planId: plan.id,
        isPaid: true,
        amount_paid: finalAmountPaid,
        status: 'active',
        subscription_type: metadata.subscriptionType,
        expiry_date: expiryDate // Convert string to Date object
      });
      console.log("Subscription created for user:", subscription);
       // Mark any other active subscriptions as inactive
    
    }
    else{
      subscription = await updateSubscription(
        { userId, planId: plan.id, status: subscription.status },
        { 
          status: 'active',
          isPaid: true,
          amount_paid: finalAmountPaid,
        }
      );
    }
  

    
    if (metadata.subscriptionType === 'monthly') {
      balanceExpiryDate = new Date(expiryDate).toISOString().split('T')[0];
    } else {
      const now = new Date();
      const oneMonthFromNow = add(now, { months: 1 });
      balanceExpiryDate = oneMonthFromNow.toISOString().split('T')[0];
    }

    await createSubscriptionBalance({
      userId,
      subscription_id: subscription.id,
      plan,
      expiry_date: balanceExpiryDate
    });


    if (!isTrial && finalAmountPaid > 0) {
      // Create payment record for non-trial subscriptions
      const today = new Date();
      
      // Check if payment record already exists for this subscription and date
      const existingPayment = await db
        .select()
        .from(creatorPayments)
        .where(
          and(
            eq(creatorPayments.subscriptionId, subscription.id),
            eq(creatorPayments.amount, finalAmountPaid),
            eq(creatorPayments.transaction_date, today)
          )
        )
        .limit(1)
        .then(rows => rows[0]);

      if (!existingPayment) {
        await createCreatorPayment({
          userId,
          subscriptionId: subscription.id,
          amount: finalAmountPaid,
          transaction_date: today
        });
        console.log('Created payment record for user:', userId);
      }
      else{
        console.log("Payment record already exists for user:", userId);
      }
      
      // Track the subscription in ConvertKit
      try {
        // Get user email for ConvertKit tracking
        const userRecord = await db
          .select({ email: user.email })
          .from(user)
          .where(eq(user.id, userId))
          .limit(1)
          .then(rows => rows[0]);
          
        if (userRecord?.email) {
          // Determine if this is a hobby or pro plan based on the product name
          // This assumes product names contain 'hobby' or 'pro' in their name
          const planLower = productName.toLowerCase();
          
          if (planLower.includes('pro')) {
            await trackProSubscription(userRecord.email);
            console.log(`Added Pro tag in ConvertKit for user: ${userRecord.email}`);
          } else if (planLower.includes('hobby')) {
            await trackHobbySubscription(userRecord.email);
            console.log(`Added Hobby tag in ConvertKit for user: ${userRecord.email}`);
          } else {
            console.log(`Unknown plan type for ConvertKit tagging: ${productName}`);
          }
        }
      } catch (error) {
        console.error('Error updating ConvertKit tags:', error);
        // Don't throw here - we don't want to fail the subscription creation if ConvertKit fails
      }
    }
    
    return subscription;
  } catch (error) {
    console.error('Error handling creator subscription:', error);
    throw error;
  }
}
