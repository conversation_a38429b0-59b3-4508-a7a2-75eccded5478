# Architecture Overview

## System Architecture

The BuildThatIdea platform follows a modern web application architecture with a Next.js frontend and backend API routes, using PostgreSQL for data storage and various cloud services for specialized functionality.

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      Client Browser                         │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                        Next.js App                          │
│  ┌─────────────────┐    ┌─────────────────┐                 │
│  │   React UI      │    │   API Routes    │                 │
│  │  Components     │    │  (Serverless)   │                 │
│  └─────────────────┘    └────────┬────────┘                 │
│                                  │                          │
│  ┌─────────────────┐    ┌────────▼────────┐                 │
│  │  Auth System    │    │  Drizzle ORM    │                 │
│  │  (NextAuth.js)  │◄───┤                 │                 │
│  └─────────────────┘    └────────┬────────┘                 │
└──────────────────────────────────┼─────────────────────────┘
                                   │
┌──────────────────────────────────▼─────────────────────────┐
│                      PostgreSQL Database                    │
└──────────────────────────────────┬─────────────────────────┘
                                   │
┌─────────────────┐  ┌─────────────▼───────────┐  ┌───────────────────┐
│   AWS S3        │  │     Upstash Vector      │  │   Stripe          │
│  (File Storage) │  │  (Vector Embeddings)    │  │  (Payments)       │
└─────────────────┘  └─────────────────────────┘  └───────────────────┘
        │                       │                          │
        │                       │                          │
┌───────▼───────────────────────▼──────────────────────────▼───────────┐
│                        External AI Models                            │
│              (OpenAI, Anthropic, and other providers)                │
└─────────────────────────────────────────────────────────────────────┘
```

## Component Architecture

### Frontend Layer

The frontend is built with Next.js and React, utilizing the App Router for routing and page organization. Key frontend components include:

1. **Page Components** - Main page layouts in the `/app` directory
2. **UI Components** - Reusable UI elements in the `/components` directory
3. **Context Providers** - React context for state management in the `/context` directory
4. **Custom Hooks** - Reusable logic in the `/hooks` directory

### Backend Layer

The backend is implemented using Next.js API routes, which function as serverless endpoints:

1. **API Routes** - Organized in the `/app/api` directory by feature
2. **Database Access** - Using Drizzle ORM for type-safe database operations
3. **Authentication** - NextAuth.js for user authentication and session management
4. **External Service Integration** - AWS S3, Stripe, Upstash Vector, etc.

### Database Layer

PostgreSQL is used as the primary database, with Drizzle ORM providing a type-safe interface:

1. **Schema Definition** - In `/lib/db/schema.ts`
2. **Migrations** - Managed through Drizzle Kit in the `/migrations` directory
3. **Queries** - Common database operations in `/lib/db/queries.ts`

## Key Subsystems

### Authentication System

The authentication system is built on NextAuth.js with custom credential providers:

1. **Login/Register** - Email and password authentication
2. **Session Management** - JWT-based sessions
3. **Authorization** - Role-based access control for users and creators

### Agent Management System

The agent management system handles the creation, editing, and deployment of AI agents:

1. **Agent Creation** - Multi-step form for creating new agents
2. **Knowledge Base** - File upload and processing for agent knowledge
3. **Vector Embeddings** - Generation and storage of embeddings for knowledge retrieval

### Chat System

The chat system enables interaction with AI agents:

1. **Chat Interface** - Real-time messaging UI
2. **Message Processing** - Handling user messages and agent responses
3. **Knowledge Retrieval** - Integration with the vector database for relevant information

### Payment System

The payment system manages subscriptions, one-time payments, and creator payouts:

1. **Stripe Integration** - For payment processing
2. **Subscription Management** - Handling recurring subscriptions
3. **Payout Processing** - Managing creator earnings and payouts

## Data Flow

### Agent Creation Flow

1. User inputs agent details and uploads knowledge base files
2. Files are processed and stored in AWS S3
3. Vector embeddings are generated and stored in Upstash Vector
4. Agent record is created in the PostgreSQL database

### Chat Interaction Flow

1. User sends a message to an agent
2. System retrieves relevant knowledge from the vector database
3. Message is sent to the AI model with context and instructions
4. AI response is processed and returned to the user
5. Conversation is stored in the database

### Payment Flow

1. User initiates a subscription or one-time payment
2. Stripe checkout session is created
3. User completes payment on Stripe
4. Webhook confirms payment and updates subscription status
5. Creator earnings are calculated and tracked for payout

## Deployment Architecture

The application is deployed on Vercel with the following components:

1. **Next.js Application** - The main application code
2. **API Routes** - Serverless functions for backend logic
3. **Vercel Postgres** - Managed PostgreSQL database
4. **External Services** - AWS S3, Upstash Vector, Stripe, etc.

## Security Architecture

1. **Authentication** - NextAuth.js with JWT tokens
2. **Password Security** - bcrypt hashing for passwords
3. **API Security** - Input validation with Zod
4. **Data Encryption** - Sensitive data encryption for tokens and keys
5. **CORS Policies** - Controlled cross-origin resource sharing
