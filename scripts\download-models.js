// scripts/download-models.js
const { pipeline, env } = require('@xenova/transformers');
const fs = require('fs');
const path = require('path');

// Set the cache directory to a location within the project
// This ensures models are saved in the project directory and included in the deployment
const publicDir = path.join(process.cwd(), 'public');
const modelsDir = path.join(publicDir, 'models');

// Configure the transformers library to use our custom cache location
env.cacheDir = modelsDir;

// Create models directory if it doesn't exist
if (!fs.existsSync(modelsDir)) {
  fs.mkdirSync(modelsDir, { recursive: true });
}

async function downloadModels() {
  console.log('Downloading transformer models to:', modelsDir);
  
  try {
    // Download the feature extraction model
    console.log('Downloading all-MiniLM-L6-v2 model...');
    const extractEmbeddings = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
    console.log('all-MiniLM-L6-v2 model downloaded successfully');
    
    // Add any other models you need to download here
    
    // Create a marker file to indicate successful download
    fs.writeFileSync(path.join(modelsDir, 'download-complete.txt'), 
      `Models downloaded successfully at ${new Date().toISOString()}\n` +
      `Models location: ${modelsDir}\n`);
      
    console.log('All models downloaded successfully');
  } catch (error) {
    console.error('Error downloading models:', error);
    process.exit(1);
  }
}

downloadModels();
