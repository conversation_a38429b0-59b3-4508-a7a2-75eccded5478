# Vector Search and Embeddings

## Overview

The BTI platform uses vector search and embeddings to enable AI agents to find and retrieve relevant information from their knowledge bases. This document details the implementation of vector search and embeddings, including the technology stack, data flow, and integration with the rest of the platform.

## Technology Stack

- **Upstash Vector**: Vector database for storing and querying embeddings
- **AI Models**: For generating embeddings from text
- **Next.js API Routes**: Backend endpoints for vector operations
- **TypeScript**: Type-safe implementation of vector operations

## Vector Search Components

### 1. Vector Database

The platform uses Upstash Vector as its vector database:

1. **Database Structure**: Each agent has its own vector index
2. **Vector Format**: High-dimensional vectors representing text chunks
3. **Metadata**: Each vector includes metadata about its source
4. **Query Interface**: Similarity search based on cosine distance

### 2. Embedding Generation

Text is converted to vector embeddings using AI models:

1. **Text Preprocessing**: Cleaning and normalizing text
2. **Chunking**: Splitting text into manageable chunks
3. **Embedding Models**: Using AI models to generate embeddings
4. **Dimensionality**: Typically 1536-dimensional vectors

### 3. Vector Index Management

Each agent has its own vector index:

1. **Index Creation**: Creating a new index when an agent is created
2. **Index Updates**: Adding, updating, or removing vectors as knowledge changes
3. **Index Security**: Securing access to the index with tokens
4. **Index Monitoring**: Monitoring index size and performance

## Implementation Details

### 1. Vector Index Creation

The vector index creation is implemented in the API route for generating indices:

```typescript
// Simplified implementation of the generate-index API route
export async function POST(request: Request) {
  try {
    const { name } = await request.json();
    
    if (!name) {
      return Response.json({ error: 'Name is required' }, { status: 400 });
    }
    
    // Create a new index in Upstash Vector
    const response = await fetch('https://api.upstash.com/v2/vector/index', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${process.env.UPSTASH_VECTOR_REST_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        dimensions: 1536,
        metric: 'cosine',
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to create vector index:', errorText);
      return Response.json({ error: 'Failed to create vector index' }, { status: 500 });
    }
    
    const indexData = await response.json();
    
    return Response.json({
      endpoint: indexData.endpoint,
      token: indexData.token,
    });
  } catch (error) {
    console.error('Error in generate-index:', error);
    return Response.json({ error: 'Failed to generate index' }, { status: 500 });
  }
}
```

### 2. Vector Index Access

The vector index access is implemented in `/lib/embeddings/retriever.ts`:

```typescript
// Cache for vector indices to avoid creating new instances for the same agent
const vectorIndexCache = new Map<string, Index<ChunkMetadata>>();

/**
 * Get or create a vector index for a specific agent
 */
async function getVectorIndex(agentId: string): Promise<Index<ChunkMetadata>> {
  // Check if we already have this index in the cache
  if (vectorIndexCache.has(agentId)) {
    const cachedIndex = vectorIndexCache.get(agentId);
    if (cachedIndex) {
      return cachedIndex;
    }
  }

  // Get agent information from the database
  const agent = await getAgentById({ agentId });

  if (!agent || !agent.endpoint || !agent.token) {
    throw new Error(`Agent ${agentId} has no vector index configured`);
  }

  // Decrypt the token
  const decryptedToken = decrypt(agent.token);

  // Ensure the endpoint has the proper URL format with protocol
  let formattedEndpoint = agent.endpoint;
  if (formattedEndpoint && !formattedEndpoint.startsWith('http')) {
    formattedEndpoint = `https://${formattedEndpoint}`;
  }

  console.log(
    `Creating vector index for agent ${agentId} with endpoint ${formattedEndpoint}`,
  );

  // Create a new vector index instance
  const vectorIndex = new Index<ChunkMetadata>({
    url: formattedEndpoint,
    token: decryptedToken,
  });

  // Cache the index for future use
  vectorIndexCache.set(agentId, vectorIndex);

  return vectorIndex;
}
```

### 3. Vector Search

The vector search is implemented in the `retrieveRelevantContent` function:

```typescript
/**
 * Retrieve relevant content from the agent's vector index
 */
export async function retrieveRelevantContent(
  query: string,
  agentId: string,
  limit = 5,
  similarityThreshold = 0.5,
): Promise<string[]> {
  try {
    console.log('=== VECTOR RETRIEVAL STARTED ===');
    console.log(
      `Query: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`,
    );
    console.log(`Agent ID: ${agentId}`);
    console.log(
      `Limit: ${limit}, Similarity threshold: ${similarityThreshold}`,
    );

    // Get the vector index for this agent
    const vectorIndex = await getVectorIndex(agentId);
    console.log('Vector index retrieved successfully');

    // Query Upstash Vector using the query text directly
    // Let Upstash handle the embedding generation
    console.log('Querying vector index...');
    const results = await vectorIndex.query({
      data: query, // Use 'data' field for Upstash to generate the embedding
      topK: limit,
      includeVectors: false,
      includeMetadata: true,
    });
    console.log(`Raw results received: ${results.length}`);

    // Filter by similarity threshold and sort
    const filteredResults = (results as VectorResult[])
      .filter((result) => result.score >= similarityThreshold)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    console.log(
      `Found ${filteredResults.length} relevant chunks after filtering`,
    );

    // Log similarity scores
    filteredResults.forEach((result, index) => {
      console.log(`Result ${index + 1} - Score: ${result.score.toFixed(4)}`);
    });

    console.log('=== VECTOR RETRIEVAL COMPLETED ===');

    // Extract content with type safety
    return filteredResults.map(
      (result) => result.metadata?.content ?? 'Content not available',
    );
  } catch (error) {
    console.error('=== VECTOR RETRIEVAL ERROR ===');
    console.error(
      `Query: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`,
    );
    console.error(`Agent ID: ${agentId}`);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    console.error('==============================');
    throw new Error('Failed to retrieve relevant content');
  }
}
```

### 4. Vector Insertion

When processing files, the system inserts vectors into the index:

```typescript
// Simplified vector insertion
async function vectorizeAndStoreChunks(
  chunks: string[],
  agentId: string,
  fileId: string
) {
  try {
    // Get the vector index
    const vectorIndex = await getVectorIndex(agentId);
    
    // Prepare vectors for insertion
    const vectors = chunks.map((chunk, index) => ({
      id: `${fileId}-${index}`,
      metadata: {
        agentId,
        fileId,
        chunkIndex: index.toString(),
        content: chunk,
      },
      data: chunk, // Upstash will generate the embedding
    }));
    
    // Insert vectors in batches
    const batchSize = 100;
    for (let i = 0; i < vectors.length; i += batchSize) {
      const batch = vectors.slice(i, i + batchSize);
      await vectorIndex.upsert(batch);
    }
    
    return true;
  } catch (error) {
    console.error('Error vectorizing chunks:', error);
    throw error;
  }
}
```

### 5. Vector Deletion

When files are deleted, the system removes vectors from the index:

```typescript
// Simplified vector deletion
async function deleteVectorsForFile(agentId: string, fileId: string) {
  try {
    // Get the vector index
    const vectorIndex = await getVectorIndex(agentId);
    
    // Query for vectors with the file ID
    const results = await vectorIndex.query({
      metadata: { fileId },
      topK: 1000, // Assuming no more than 1000 chunks per file
    });
    
    // Extract vector IDs
    const vectorIds = results.map(result => result.id);
    
    // Delete vectors
    if (vectorIds.length > 0) {
      await vectorIndex.delete(vectorIds);
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting vectors:', error);
    throw error;
  }
}
```

## Data Flow

### 1. Knowledge Base Creation

When a knowledge base is created:

1. Files are uploaded to AWS S3
2. Text is extracted from the files
3. Text is split into chunks
4. Chunks are converted to vector embeddings
5. Embeddings are stored in Upstash Vector

### 2. Knowledge Base Updates

When a knowledge base is updated:

1. New files are processed as above
2. Modified files have their old vectors removed and new ones added
3. Deleted files have their vectors removed from the index

### 3. Knowledge Retrieval

When a user asks a question:

1. The question is converted to a vector embedding
2. The embedding is used to search the vector index
3. Similar vectors are retrieved based on cosine similarity
4. The text chunks associated with the vectors are returned
5. The AI model uses the retrieved chunks to generate a response

## Upstash Vector Integration

### 1. Vector Index Configuration

The Upstash Vector index is configured with:

1. **Dimensions**: 1536 (matching the embedding model)
2. **Metric**: Cosine similarity
3. **Name**: Based on the agent ID

### 2. API Integration

The platform integrates with Upstash Vector through:

1. **REST API**: For index creation and management
2. **Client SDK**: For vector operations
3. **Authentication**: Using API tokens

### 3. Token Management

Vector index tokens are securely managed:

1. **Token Storage**: Encrypted in the database
2. **Token Decryption**: Decrypted only when needed
3. **Token Rotation**: Support for rotating tokens if needed

## Performance Considerations

The vector search system includes several performance optimizations:

### 1. Caching

1. **Index Caching**: Caching vector indices in memory
2. **Result Caching**: Potential caching of common query results

### 2. Batching

1. **Batch Insertion**: Inserting vectors in batches
2. **Batch Deletion**: Deleting vectors in batches

### 3. Query Optimization

1. **Similarity Threshold**: Filtering results by similarity threshold
2. **Result Limiting**: Limiting the number of results returned
3. **Metadata Filtering**: Using metadata to filter results

## Security Considerations

The vector search system includes several security measures:

### 1. Token Security

1. **Encryption**: Encrypting vector index tokens
2. **Access Control**: Restricting access to vector indices
3. **Token Rotation**: Support for rotating tokens if needed

### 2. Data Security

1. **Data Isolation**: Each agent has its own vector index
2. **Metadata Filtering**: Ensuring users can only access their own data
3. **Input Validation**: Validating all inputs to vector operations

## Error Handling

The vector search system includes comprehensive error handling:

### 1. Index Errors

1. **Creation Errors**: Handling errors during index creation
2. **Access Errors**: Handling errors when accessing indices
3. **Deletion Errors**: Handling errors when deleting indices

### 2. Operation Errors

1. **Query Errors**: Handling errors during vector queries
2. **Insertion Errors**: Handling errors during vector insertion
3. **Deletion Errors**: Handling errors during vector deletion

### 3. Graceful Degradation

1. **Fallback Responses**: Providing fallback responses when vector search fails
2. **Error Logging**: Detailed logging of vector operation errors
3. **Retry Logic**: Retrying failed operations when appropriate

## Monitoring and Analytics

The vector search system includes monitoring and analytics:

### 1. Operation Logging

1. **Query Logging**: Logging vector queries
2. **Result Logging**: Logging query results
3. **Error Logging**: Logging operation errors

### 2. Performance Metrics

1. **Query Latency**: Measuring query response time
2. **Index Size**: Monitoring index size
3. **Operation Counts**: Tracking operation counts

## Limitations and Constraints

The vector search system has several limitations:

### 1. Upstash Vector Limits

1. **Index Size**: Maximum number of vectors per index
2. **Query Rate**: Maximum queries per second
3. **Vector Dimensions**: Fixed at 1536 dimensions

### 2. Semantic Limitations

1. **Semantic Understanding**: Limited by the embedding model
2. **Context Window**: Limited context for each chunk
3. **Cross-Chunk Relationships**: Limited understanding of relationships between chunks

## Future Enhancements

Planned enhancements to the vector search system:

### 1. Advanced Retrieval

1. **Hybrid Search**: Combining vector search with keyword search
2. **Multi-query Retrieval**: Using multiple queries for better coverage
3. **Query Rewriting**: Rewriting queries for better results

### 2. Performance Improvements

1. **Caching Improvements**: More sophisticated caching strategies
2. **Batch Processing**: Improved batch processing
3. **Parallel Processing**: Parallel vector operations

### 3. Feature Enhancements

1. **Relevance Feedback**: Using feedback to improve search results
2. **Personalized Retrieval**: Personalizing retrieval based on user preferences
3. **Multi-modal Embeddings**: Support for image and audio embeddings
