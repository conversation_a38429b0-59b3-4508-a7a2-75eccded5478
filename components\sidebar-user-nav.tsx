'use client';
import { Ch<PERSON><PERSON>Up, <PERSON><PERSON><PERSON>, LogOut, Sun, Moon, Sparkles } from 'lucide-react';
import Image from 'next/image';
import type { User } from 'next-auth';
import { signOut, useSession } from 'next-auth/react';
import { resetAppState } from '@/lib/reset-app-state';
import { useTheme } from 'next-themes';
import { useAuth } from './auth-context';
import { usePathname, useRouter } from 'next/navigation';
import { useSubscription } from '@/context/subscription-context';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  useSidebar,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

export function SidebarUserNav({ user }: { user: User }) {
  const { setTheme, theme } = useTheme();
  const { update: updateSession } = useSession();
  const { triggerAuthStateChange } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const userName = user?.name || user?.email?.split('@')[0] || 'User';
  const { state } = useSidebar();
  const isCollapsed = state === 'collapsed';

  // Get subscription data directly from context
  const { isProForAgent, getRemainingMessages } = useSubscription();

  // Extract agent ID from pathname if available
  // Format: /agent/[agentId]/chat/[chatId]
  let currentAgentId = 'default';

  if (pathname.startsWith('/agent/')) {
    const pathParts = pathname.split('/');
    if (pathParts.length >= 3) {
      currentAgentId = pathParts[2];
    }
  }

  // Get subscription status for the current agent
  const isPro = isProForAgent(currentAgentId);
  const remainingMessages = getRemainingMessages(currentAgentId);

  return (
    <SidebarMenu className={`${isCollapsed ? 'px-0' : 'px-3'} pb-4`}>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              className={`data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground ${isCollapsed ? 'size-10 p-0 mx-auto' : 'h-12 px-3'} hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors`}
            >
              {isCollapsed ? (
                <Image
                  src={`https://avatar.vercel.sh/${user.email}`}
                  alt={user.email ?? 'User Avatar'}
                  width={28}
                  height={28}
                  className="rounded-full border border-sidebar-border mx-auto"
                />
              ) : (
                <>
                  <div className="flex items-center gap-3">
                    <Image
                      src={`https://avatar.vercel.sh/${user.email}`}
                      alt={user.email ?? 'User Avatar'}
                      width={32}
                      height={32}
                      className="rounded-full border border-sidebar-border"
                    />
                    <div className="flex flex-col items-start text-left">
                      <div className="flex items-center gap-1">
                        <span className="font-medium text-sm truncate">
                          {userName}
                        </span>
                        {isPro && (
                          <span className="bg-gradient-to-r from-orange-500 to-pink-500 text-white text-[10px] px-1.5 py-0.5 rounded-full flex items-center gap-0.5">
                            <Sparkles className="size-2.5" />
                            <span>PRO</span>
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground truncate">
                        {user?.email}
                      </span>
                    </div>
                  </div>
                  <ChevronUp className="ml-auto size-4 text-muted-foreground" />
                </>
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side="top"
            className="w-[--radix-popper-anchor-width]"
            align="start"
          >
            <DropdownMenuItem
              className="cursor-pointer flex items-center gap-2"
              onSelect={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {theme === 'light' ? (
                <>
                  <Moon className="size-4" />
                  <span>Dark mode</span>
                </>
              ) : (
                <>
                  <Sun className="size-4" />
                  <span>Light mode</span>
                </>
              )}
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer flex items-center gap-2">
              <Settings className="size-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <button
                type="button"
                className="w-full cursor-pointer flex items-center gap-2"
                onClick={async () => {
                  try {
                    // Reset all application state (contexts, browser storage, cookies)
                    await resetAppState();
                    console.log('App state reset before logout');

                    // Check if we're on an agent chat page
                    const isAgentChatPage = pathname.startsWith('/agent/');

                    if (isAgentChatPage) {
                      // For agent chat pages, stay on the same page but update auth state
                      await signOut({ redirect: false });

                      // Force a session update and reset auth state
                      await updateSession();
                      triggerAuthStateChange();

                      // Force a client-side router refresh without full page reload
                      router.refresh();
                    } else {
                      // For all other pages, redirect to login page
                      await signOut({
                        redirect: true,
                        callbackUrl: '/login',
                      });
                    }
                  } catch (error) {
                    console.error('Error during sign-out:', error);
                    
                    // Fallback: try to sign out directly if resetAppState fails
                    try {
                      // We don't call resetAppState again since it might be what failed
                      // Just proceed with sign-out
                      console.warn('Proceeding with sign-out after resetAppState failure');
                    } catch (fallbackError) {
                      console.error('Error in sign-out fallback:', fallbackError);
                    }
                    
                    // Fallback logout behavior
                    await signOut({
                      redirect: true,
                      callbackUrl: '/login',
                    });
                  }
                }}
              >
                <LogOut className="size-4" />
                <span>Sign out</span>
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
