import React, { useState } from 'react';
import Image from 'next/image';
import { Upload, Pencil, Trash2, Alert<PERSON>ircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

// S3 upload function
const uploadLogo = async (file: File): Promise<string | undefined> => {
  try {
    const formData = new FormData();
    formData.append('logo', file);
    const response = await fetch('/api/agents/upload-logo', {
      method: 'POST',
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to upload logo');
    }
    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error('Error uploading logo:', error);
    toast.error('Failed to upload logo');
    return undefined;
  }
};

interface LogoUploaderProps {
  logoPreview: string | null;
  setLogoPreview: (preview: string | null) => void;
  setLogoFile: (file: File | null) => void;
  setLogoUrl?: (url: string | null) => void; // Optional function to set the logo URL
  handleLogoChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onUploadingStateChange?: (isUploading: boolean) => void; // New prop to notify about uploading state
}

export function LogoUploader({
  logoPreview,
  setLogoPreview,
  setLogoFile,
  setLogoUrl,
  handleLogoChange: externalHandleLogoChange,
  onUploadingStateChange,
}: LogoUploaderProps) {
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);

  // Validate the image file
  const validateImage = (file: File): boolean => {
    // Check file type
    const validTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/svg+xml',
    ];
    if (!validTypes.includes(file.type)) {
      setError(
        'Invalid file type. Please upload a PNG, JPG, GIF, or SVG file.',
      );
      return false;
    }

    // Check file size (2MB max)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
      setError('File is too large. Maximum size is 2MB.');
      return false;
    }

    // Clear any previous errors
    setError(null);
    return true;
  };

  // Handle logo upload
  const handleLogoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent default form submission behavior
    e.preventDefault();

    const file = e.target.files?.[0];
    if (!file) return;

    // Validate the file
    if (!validateImage(file)) {
      // Reset the input
      e.target.value = '';
      toast.error(error || 'Invalid file');
      return;
    }

    // If an external handler is provided, use it
    if (externalHandleLogoChange) {
      externalHandleLogoChange(e);
      return;
    }

    // Otherwise use the default implementation with S3 upload
    setLogoFile(file);
    
    // Create preview immediately
    const reader = new FileReader();
    reader.onloadend = () => {
      setLogoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Upload to S3
    setIsUploading(true);
    // Notify parent about uploading state change
    if (onUploadingStateChange) {
      onUploadingStateChange(true);
    }
    toast.info('Uploading logo...');
    const logoUrl = await uploadLogo(file);
    
    if (logoUrl && setLogoUrl) {
      setLogoUrl(logoUrl);
      setImageLoading(true);
      setLogoPreview(logoUrl); // This will trigger image loading
      toast.success('Logo uploaded successfully');
    }
    setIsUploading(false);
    // Notify parent about uploading state change
    if (onUploadingStateChange) {
      onUploadingStateChange(false);
    }
  };

  return (
    <div className="mb-4 sm:mb-6">
      <h3 className="text-sm font-medium mb-2">Logo</h3>
      <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
        {/* Simplified logo upload area - mobile optimized */}
        <div
          role="button"
          tabIndex={0}
          className={`flex items-center justify-center border-2 ${
            error
              ? 'border-red-300 dark:border-red-700'
              : isUploading
                ? 'border-orange-400 dark:border-orange-500'
                : 'border-dashed border-gray-300 dark:border-gray-700'
          } rounded-lg size-20 sm:size-24 relative overflow-hidden transition-all duration-200 hover:border-orange-600 dark:hover:border-orange-600 cursor-pointer`}
          onClick={() =>
            !isUploading && document.getElementById('logo-upload')?.click()
          }
          onKeyDown={(e) =>
            e.key === 'Enter' &&
            !isUploading &&
            document.getElementById('logo-upload')?.click()
          }
          aria-label="Upload logo"
        >
          {isUploading ? (
            <div className="text-center p-2">
              <div className="mx-auto size-6 border-2 border-orange-600 border-t-transparent rounded-full animate-spin"></div>
              <div className="mt-1 text-xs text-orange-600">Uploading...</div>
            </div>
          ) : logoPreview ? (
            <>
              {imageLoading ? (
                <div className="absolute inset-0 bg-orange-600/10 rounded-lg animate-pulse">
                  <div className="size-full bg-gradient-to-r from-orange-600/20 to-orange-600/5 rounded-lg"></div>
                </div>
              ) : null}
              <Image
                src={logoPreview}
                alt="Agent Logo"
                fill
                sizes="(max-width: 640px) 80px, 96px"
                className="object-cover rounded-lg"
                onLoad={() => setImageLoading(false)}
                onError={() => {
                  setImageLoading(false);
                  setError('Failed to load image');
                }}
              />
              <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                <div className="flex gap-1">
                  <Button
                    type="button"
                    variant="secondary"
                    size="icon"
                    className="size-8 bg-white/90 hover:bg-white"
                  >
                    <Pencil className="size-4 text-gray-700" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center p-2 cursor-pointer">
              <Upload className="mx-auto size-6 text-muted-foreground" />
              <div className="mt-1 text-xs text-muted-foreground">
                Upload logo
              </div>
            </div>
          )}
            <input
            id="logo-upload"
            type="file"
            accept="image/jpeg,image/png,image/gif,image/svg+xml"
            className="hidden"
            onChange={handleLogoChange}
            disabled={isUploading}
          />
        </div>

        <div className="flex-1 space-y-2 mt-2 sm:mt-0">
          {logoPreview && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="text-xs h-8"
              onClick={() => {
                setLogoPreview(null);
                setLogoFile(null);
                setImageLoading(false);
                // Also clear the logo URL if the function is provided
                if (setLogoUrl) {
                  setLogoUrl(null);
                }
                setError(null);
                toast.success('Logo removed');
              }}
            >
              <Trash2 className="size-3 mr-1" />
              Remove logo
            </Button>
          )}

          <div className="text-xs text-muted-foreground space-y-1">
            <p>Upload a square image (PNG, JPG, GIF, or SVG)</p>
            <p>Max size: 2MB</p>
          </div>

          {error && (
            <div className="flex items-center text-red-500 text-xs">
              <AlertCircle className="size-3 mr-1" />
              {error}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
