// This file configures the initialization of Sentry for edge runtimes
// The config you add here will be used whenever your application gets used in an edge runtime.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // In production, reduce the trace sample rate for better performance
  // 0.1 means 10% of transactions will be captured
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  // Disable debug mode in production
  debug: process.env.NODE_ENV !== 'production',
  
  // Set environment explicitly
  environment: process.env.NODE_ENV || 'development',
  
  // Add release information for better error tracking
  release: process.env.VERCEL_GIT_COMMIT_SHA || process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || 'local-development',
  
  // Edge-specific configuration
  // Minimize overhead for edge functions
  maxBreadcrumbs: 20,
  
  // Ensure we don't impact edge function performance too much
  autoSessionTracking: false,
});
