// /app/api/agents/generate-index/route.ts
import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    // Parse the request body to get the name
    const body = await request.json();
    const { name } = body;

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Get the environment variables
    const username = process.env.UPSTASH_USER;
    const apiKey = process.env.UPSTASH_API;

    if (!username || !apiKey) {
      console.error(
        'Missing environment variables: UPSTASH_USER or UPSTASH_API',
      );
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 },
      );
    }

    // Fixed values for other parameters
    const postData = {
      name,
      region: 'us-east-1',
      similarity_function: 'COSINE',
      dimension_count: 1536,
      type: 'payg', // !NOTE : Change this to payg once BTI account is linked
      // No embedding_model specified - this creates a custom embedding index
    };

    // Make the API request to Upstash
    const response = await axios.post(
      'https://api.upstash.com/v2/vector/index',
      postData,
      {
        auth: {
          username,
          password: apiKey,
        },
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    //console.log('Upstash response:', response.data);

    // Return the successful response
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Error creating Upstash index:', error);

    // Improved error handling with detailed logging
    if (axios.isAxiosError(error) && error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);

      return NextResponse.json(
        {
          error: 'Failed to create index',
          details: error.response.data,
        },
        { status: error.response.status },
      );
    }

    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 },
    );
  }
}
