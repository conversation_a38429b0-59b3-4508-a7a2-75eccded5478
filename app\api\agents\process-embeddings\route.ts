// /app/api/agents/process-embeddings/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { agentKnowledgeFiles } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { OpenAI } from 'openai';
import { Index } from '@upstash/vector';

export const maxDuration = 800;

// Configuration constants
const CONFIG = {
  CHUNK_SIZE: 1000,
  CHUNK_OVERLAP: 200,
  BATCH_SIZE: 50, // Increased from 10 to 50 to process more chunks in parallel
  FILE_DOWNLOAD_TIMEOUT: 30000,
  OPENAI_TIMEOUT: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
} as const;

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

class APIError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number = 500,
    public readonly cause?: unknown,
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Helper function to verify internal API requests
function verifyInternalRequest(request: Request): boolean {
  // If AUTH_SECRET is not set, skip verification
  if (!process.env.AUTH_SECRET) {
    return true;
  }

  const authHeader = request.headers.get('Authorization');
  const expectedValue = `Bearer ${process.env.AUTH_SECRET}`;

  if (!authHeader) {
    console.warn('Authorization header missing in internal API request');
    return false;
  }

  if (authHeader !== expectedValue) {
    console.warn('Authorization header value does not match expected value');
    return false;
  }

  return true;
}

// Create embeddings using OpenAI
async function createEmbedding(text: string, model: string = 'text-embedding-3-small'): Promise<number[]> {
  for (let attempt = 0; attempt < CONFIG.MAX_RETRIES; attempt++) {
    try {
      const response = await openai.embeddings.create({
        model: model,
        input: text,
      });
      
      return response.data[0].embedding;
    } catch (error) {
      console.error(`Error creating embedding (attempt ${attempt + 1}):`, error);
      
      if (attempt === CONFIG.MAX_RETRIES - 1) {
        throw new Error('Failed to create embedding after multiple attempts');
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY * Math.pow(2, attempt)));
    }
  }
  
  throw new Error('Failed to create embedding');
}

// Helper function to determine optimal chunk size based on text length
function determineOptimalChunkSize(textLength: number): { chunkSize: number, overlap: number } {
  // Base configuration
  const BASE_CHUNK_SIZE = CONFIG.CHUNK_SIZE;
  const BASE_OVERLAP = CONFIG.CHUNK_OVERLAP;
  
  // Target max number of chunks to stay within time limits
  const TARGET_MAX_CHUNKS = 2000;
  
  // Calculate estimated chunks with base size
  const estimatedChunks = Math.ceil(textLength / (BASE_CHUNK_SIZE - BASE_OVERLAP));
  
  if (estimatedChunks <= TARGET_MAX_CHUNKS) {
    // If base size works fine, use it
    return { chunkSize: BASE_CHUNK_SIZE, overlap: BASE_OVERLAP };
  }
  
  // Calculate optimal chunk size to hit target
  // Set absolute maximum to avoid OpenAI token limits
  // 4000 chars as specified (roughly 1000-1300 tokens)
  const ABSOLUTE_MAX_CHUNK_SIZE = 4000;
  const ABSOLUTE_MIN_CHUNK_SIZE = 1000;
  
  // Calculate a larger chunk size to hit target
  let newChunkSize = Math.min(
    ABSOLUTE_MAX_CHUNK_SIZE,
    Math.ceil(textLength / TARGET_MAX_CHUNKS) + BASE_OVERLAP
  );
  
  // Ensure minimum chunk size for context quality
  newChunkSize = Math.max(ABSOLUTE_MIN_CHUNK_SIZE, newChunkSize);
  
  // Adjust overlap proportionally (~10-15% of chunk size)
  let newOverlap = Math.max(100, Math.round(newChunkSize * 0.12)); 
  
  console.log(`Dynamically adjusting chunk size - Text length: ${textLength} chars, estimated chunks: ${estimatedChunks}`);
  console.log(`Using chunk size: ${newChunkSize}, overlap: ${newOverlap}, estimated new chunks: ~${Math.ceil(textLength / (newChunkSize - newOverlap))}`);
  
  return { chunkSize: newChunkSize, overlap: newOverlap };
}

// Helper function to split text into chunks with overlap
function splitTextIntoChunks(text: string, chunkSize = CONFIG.CHUNK_SIZE, overlap = CONFIG.CHUNK_OVERLAP): string[] {
  const chunks: string[] = [];
  let i = 0;
  
  // Clean up the text first
  text = text
    .replace(/\s+/g, ' ')
    .replace(/\n\s*\n/g, '\n\n')
    .trim();
  
  while (i < text.length) {
    const start = i === 0 ? 0 : Math.max(0, i - overlap);
    const end = Math.min(start + chunkSize, text.length);
    
    let actualEnd = end;
    if (end < text.length) {
      // Try to end at a sentence boundary
      const sentenceEnd = text.lastIndexOf('.', end);
      const questionEnd = text.lastIndexOf('?', end);
      const exclamationEnd = text.lastIndexOf('!', end);
      
      const lastPunctuation = Math.max(sentenceEnd, questionEnd, exclamationEnd);
      
      if (lastPunctuation > start + chunkSize / 2) {
        actualEnd = lastPunctuation + 1;
      }
    }
    
    const chunk = text.slice(start, actualEnd).trim();
    if (chunk.length > 50) { // Only include chunks with meaningful content
      chunks.push(chunk);
    }
    
    i = actualEnd;
  }
  
  return chunks;
}

// Process a single file with embeddings
async function processFileWithEmbeddings(
  fileId: string,
  fileUrl: string,
  agentId: string,
  vectorInfo: { endpoint: string; token: string },
  embeddingModel: string = 'text-embedding-3-small'
) {
  let formattedEndpoint = vectorInfo.endpoint;
  if (formattedEndpoint && !formattedEndpoint.startsWith('http')) {
    formattedEndpoint = `https://${formattedEndpoint}`;
  }

  try {
    const { extractTextFromFile } = await import('@/lib/embeddings/extractors');
    const { updateAgentKnowledgeFile } = await import('@/lib/db/queries');
    
    // Initialize Upstash Vector client
    const vectorIndex = new Index({
      url: formattedEndpoint,
      token: vectorInfo.token,
    });

    // Download file with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), CONFIG.FILE_DOWNLOAD_TIMEOUT);
    const response = await fetch(fileUrl, { signal: controller.signal });
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.status} ${response.statusText}`);
    }

    const fileBlob = await response.blob();
    
    // Extract text from file
    const text = await extractTextFromFile(fileBlob);
    if (!text || text.length === 0) {
      throw new Error('Text extraction produced empty result');
    }

    // Calculate optimal chunk size based on text length
    const { chunkSize, overlap } = determineOptimalChunkSize(text.length);
    // console.log(`Processing file ${fileId} with chunk size ${chunkSize} and overlap ${overlap}`);

    // Split text into chunks with dynamic sizing
    const chunks = splitTextIntoChunks(text, chunkSize, overlap);
    
    await updateAgentKnowledgeFile({
      fileId,
      data: { totalChunks: chunks.length.toString() },
    });

    // Ensure namespace exists by performing a dummy operation first
    try {
      // Create a dummy vector to initialize the namespace if it doesn't exist
      // Using small non-zero values to avoid cosine similarity issues with all-zero vectors
      const dummyVector = {
        id: `${agentId}_namespace_init`,
        vector: new Array(1536).fill(0).map(() => 0.1), // Small non-zero values
        metadata: {
          text: "Namespace initialization vector",
          agentId,
          fileId: "init",
          chunkIndex: "init"
        }
      };
      
      // Try to initialize the namespace with the dummy vector
      await vectorIndex.upsert([dummyVector], { namespace: agentId });
      // Namespace initialized or already exists
    } catch (error: unknown) {
      console.error(`Error ensuring namespace ${agentId} exists:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to ensure namespace ${agentId} exists: ${errorMessage}`);
    }

    // Process chunks in batches
    for (let batchStart = 0; batchStart < chunks.length; batchStart += CONFIG.BATCH_SIZE) {
      const batchEnd = Math.min(batchStart + CONFIG.BATCH_SIZE, chunks.length);
      const batchChunks = chunks.slice(batchStart, batchEnd);

      // Create embeddings for batch in parallel
      const batchPromises = batchChunks.map(async (chunk, i) => {
        const chunkIndex = batchStart + i;
        const embedding = await createEmbedding(chunk, embeddingModel);
        
        return {
          id: `${agentId}:${fileId}:${chunkIndex}`,
          vector: embedding,
          metadata: {
            agentId,
            fileId,
            chunkIndex: chunkIndex.toString(),
            text: chunk, // Store the actual text in metadata for retrieval
          },
        };
      });

      const dataPayload = await Promise.all(batchPromises);
      
      // Upsert the data to the already initialized namespace
      try {
        await vectorIndex.upsert(dataPayload, { namespace: agentId });
      } catch (error: unknown) {
        console.error(`Error upserting data to namespace ${agentId}:`, error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Failed to upsert data to namespace ${agentId}: ${errorMessage}`);
      }

      // Update progress
      await updateAgentKnowledgeFile({
        fileId,
        data: { processedChunks: batchEnd.toString() },
      });
      
      // Cleanup for memory management
      dataPayload.length = 0;
    }

  } catch (error) {
    console.error('Error processing file:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      fileId,
      agentId,
    });
    throw error;
  }
}

interface ProcessEmbeddingsRequest {
  agentId: string;
  fileIds: string[];
  vectorInfo: {
    endpoint: string;
    token: string;
  };
  embeddingConfig?: {
    model: string;
    dimension: number;
  };
}

export async function POST(request: Request) {
  try {
    // Verify this is an internal API call but continue even if verification fails
    const isVerified = verifyInternalRequest(request);
    if (!isVerified) {
      console.warn('Unverified internal request, continuing anyway');
    }

    // Parse request body
    const body: ProcessEmbeddingsRequest = await request.json();
    const { 
      agentId, 
      fileIds, 
      vectorInfo,
      embeddingConfig = { model: 'text-embedding-3-small', dimension: 1536 }
    } = body;

    // Validate required parameters
    if (!agentId) {
      console.error('Missing required parameter: agentId');
      throw new APIError('Missing required parameter: agentId', 400);
    }

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      console.error('Missing or invalid required parameter: fileIds');
      throw new APIError('Missing or invalid required parameter: fileIds', 400);
    }

    if (!vectorInfo || !vectorInfo.endpoint || !vectorInfo.token) {
      console.error('Missing or invalid required parameter: vectorInfo');
      throw new APIError('Missing or invalid required parameter: vectorInfo', 400);
    }

    // Starting parallel processing for files

    // Process all files in parallel
    const processingPromises = fileIds.map(async (fileId) => {
      try {
        // Fetch the file details
        const fileResults = await db
          .select()
          .from(agentKnowledgeFiles)
          .where(eq(agentKnowledgeFiles.id, fileId))
          .limit(1);

        if (fileResults.length === 0) {
          console.error(`❌ File not found in database: ${fileId}`);
          return { fileId, status: 'not_found', error: 'File not found' };
        }

        const file = fileResults[0];

        // Update status to processing
        await db
          .update(agentKnowledgeFiles)
          .set({
            status: 'processing',
            processedAt: new Date(),
          })
          .where(eq(agentKnowledgeFiles.id, file.id));

        // Process the file with embeddings
        await processFileWithEmbeddings(
          file.id, 
          file.url, 
          agentId, 
          vectorInfo,
          embeddingConfig.model
        );

        // Update status to completed
        await db
          .update(agentKnowledgeFiles)
          .set({
            status: 'completed',
            processedAt: new Date(),
          })
          .where(eq(agentKnowledgeFiles.id, file.id));

        // Successfully processed file
        return { fileId, status: 'completed' };

      } catch (error) {
        console.error(`❌ Error processing file ${fileId}:`, error);

        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

        // Update status to failed
        try {
          await db
            .update(agentKnowledgeFiles)
            .set({
              status: 'failed',
              processingError: errorMessage,
              processedAt: new Date(),
            })
            .where(eq(agentKnowledgeFiles.id, fileId));
        } catch (dbError) {
          console.error(`Failed to update status for file ${fileId}:`, dbError);
        }

        return { fileId, status: 'failed', error: errorMessage };
      }
    });

    // Wait for all files to be processed
    const results = await Promise.all(processingPromises);

    // Summarize results
    const summary = {
      total: results.length,
      completed: results.filter(r => r.status === 'completed').length,
      failed: results.filter(r => r.status === 'failed').length,
      notFound: results.filter(r => r.status === 'not_found').length,
    };

    // Processing completed

    return NextResponse.json({
      message: 'Processing completed',
      agentId,
      timestamp: new Date().toISOString(),
      summary,
      results,
      success: summary.failed === 0 && summary.notFound === 0,
    });
  } catch (error) {
    console.error('❌ Error in background processing route:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;

    // Error details logged
    if (errorStack) {
      // Stack trace logged
    }

    if (error instanceof APIError) {
      return NextResponse.json(
        {
          error: error.message,
          success: false,
          timestamp: new Date().toISOString()
        },
        { status: error.statusCode },
      );
    }

    return NextResponse.json(
      {
        error: 'Processing failed: ' + errorMessage,
        success: false,
        timestamp: new Date().toISOString()
      },
      { status: 500 },
    );
  }
}
