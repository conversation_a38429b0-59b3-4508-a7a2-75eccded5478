import { db } from '@/lib/db/client';
import { agents } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { notFound, redirect } from 'next/navigation';
import { generateUUID } from '@/lib/utils';
import { generateAgentMetadata } from '@/lib/metadata';

// Force dynamic rendering to avoid connection issues during build time
export const dynamic = 'force-dynamic';
export const dynamicParams = true;

export async function generateStaticParams() {
  const allAgents = await db.select({ slug: agents.slug }).from(agents);
  return allAgents.map((agent) => ({ slug: agent.slug }));
}

// Generate metadata for the page and add it to the static params
export const generateMetadata = async ({
  params,
}: { params: { slug: string } }) => {
  return generateAgentMetadata(params.slug);
};

export default async function AgentPage({
  params,
}: { params: { slug: string } }) {
  // Find the agent by slug
  const slug = params.slug;
  const [agent] = await db.select().from(agents).where(
    and(
      eq(agents.slug, slug),
      eq(agents.status, 'active')
    )
  );

  if (!agent) return notFound();

  // Check if the agent is a draft or inactive - if so, don't allow access
  if (agent.status === 'draft' || agent.status === 'inactive') {
    return notFound();
  }

  // Generate a new chat ID - we'll redirect to the proper URL with the ID
  const chatId = generateUUID();

  // Redirect to the URL with the chat ID in the path
  return redirect(`/agent/${slug}/${chatId}`);
}
