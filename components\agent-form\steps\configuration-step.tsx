'use client';

import React, { useEffect, useState } from 'react';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import type { UseFormReturn } from 'react-hook-form';
import type { FormValues } from '../types';
import { EnhancedModelSelector } from '../enhanced-model-selector';
import { EnhancedTextarea } from '@/components/enhanced-textarea';
import { QuickMessagesEditor } from '../quick-messages-editor';

interface ConfigurationStepProps {
  form: UseFormReturn<FormValues, any, FormValues>;
  updateValidity?: (valid: boolean) => void;
  onImprovingStateChange?: (isImproving: boolean) => void;
}

export function ConfigurationStep({
  form,
  updateValidity,
  onImprovingStateChange,
}: ConfigurationStepProps) {
  const [isValid, setIsValid] = useState(false);

  // Watch for changes in the form values
  const instruction = form.watch('instruction');
  const model = form.watch('model');
  const quickMessages = form.watch('quickMessages') || [];

  // Validate the form on mount and when values change
  useEffect(() => {
    const hasRequiredFields = instruction?.length >= 10 && !!model;

    setIsValid(hasRequiredFields);

    // Notify parent component about validity
    if (updateValidity) {
      updateValidity(hasRequiredFields);
    }
  }, [instruction, model, updateValidity]);

  return (
    <div className="space-y-8">
      <div className="space-y-6">
        <div className="space-y-6 mt-4">
          <FormField
            control={form.control}
            name="model"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel
                  htmlFor="agent-model"
                  className="text-sm font-medium"
                >
                  Base Model <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <div className="w-full">
                    <EnhancedModelSelector
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="instruction"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel
                  htmlFor="agent-instruction"
                  className="text-sm font-medium"
                >
                  Instructions <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <EnhancedTextarea
                    id="agent-instruction"
                    placeholder="Describe how your agent should behave, what knowledge it has, and how it should respond to users."
                    className="min-h-[120px] resize-none"
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                    agentName={form.watch('agentName')}
                    selectedChatModel={form.watch('model')}
                    onSave={(value) => field.onChange(value)}
                    onImprovingStateChange={onImprovingStateChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <QuickMessagesEditor
              quickMessages={quickMessages}
              setQuickMessages={(messages) => form.setValue('quickMessages', messages)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
