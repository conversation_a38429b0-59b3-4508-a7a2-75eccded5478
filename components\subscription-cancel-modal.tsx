'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/context/subscription-context';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { AlertCircle } from 'lucide-react';

interface SubscriptionCancelModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  agentName: string;
  onSuccess?: () => void; // Optional callback for after successful cancellation
}

export function SubscriptionCancelModal({
  isOpen,
  onClose,
  agentId,
  agentName,
  onSuccess
}: SubscriptionCancelModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { checkSubscription } = useSubscription();

  const handleCancel = async () => {
    if (!agentId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Direct API call to cancel subscription without using Stripe portal
      const response = await fetch('/api/subscription/cancel-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ agentId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to cancel subscription');
      }
      
      // Get the cancellation date from the response
      const cancelDate = data.subscription?.cancel_at;
      const periodEnd = data.subscription?.current_period_end;
      
      // Format the date for display in the toast
      const formattedDate = cancelDate ? new Date(cancelDate * 1000).toLocaleDateString() : 'the end of your billing period';
      
      // Show success message with the cancellation date
      toast.success(`Subscription will be canceled on ${formattedDate}`);
      
      // Store the cancellation information in sessionStorage to ensure it persists
      const storageKey = `subscription_${agentId}`;
      const existingData = JSON.parse(sessionStorage.getItem(storageKey) || '{}');
      
      // Update with cancellation info
      const updatedData = {
        ...existingData,
        isPro: existingData.isPro || false,
        isLoading: false,
        remainingMessages: existingData.remainingMessages || 0,
        isCanceled: true,
        cancelDate: cancelDate ? new Date(cancelDate * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // Fallback to 30 days from now
      };
      
      // Save to session storage
      sessionStorage.setItem(storageKey, JSON.stringify(updatedData));
      
      // Instead of calling checkSubscription which can cause an infinite loop,
      // directly update the subscription context state
      // This prevents the maximum update depth exceeded error
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
      
      // Close the modal
      onClose();
    } catch (error: any) {
      console.error('Error in cancellation flow:', error);
      setError(error.message || 'An error occurred while canceling your subscription. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Cancel Subscription</DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel your subscription to {agentName}?
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            Your subscription will remain active until the end of your current billing period. You will not be charged again after that.
          </p>
          
          {error && (
            <div className="flex items-center gap-2 p-3 text-sm bg-destructive/10 text-destructive rounded-md mb-4">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Keep Subscription
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleCancel} 
            disabled={isLoading}
          >
            {isLoading ? 'Canceling...' : 'Confirm Cancellation'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default SubscriptionCancelModal;
