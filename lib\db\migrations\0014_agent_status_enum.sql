-- Create the enum type for agent status
CREATE TYPE agent_status AS ENUM ('active', 'inactive', 'draft');

-- First, drop any default or constraints on the status column
ALTER TABLE "agents" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "agents" ALTER COLUMN "status" DROP NOT NULL;

-- Handle NULL values and convert existing data
UPDATE "agents" SET "status" = 'draft' WHERE "status" IS NULL OR "status" NOT IN ('active', 'inactive', 'draft');

-- Alter the column type to use the enum
ALTER TABLE "agents" ALTER COLUMN "status" TYPE agent_status USING status::agent_status;

-- Make the column NOT NULL and set default
ALTER TABLE "agents" ALTER COLUMN "status" SET NOT NULL;
ALTER TABLE "agents" ALTER COLUMN "status" SET DEFAULT 'draft';
