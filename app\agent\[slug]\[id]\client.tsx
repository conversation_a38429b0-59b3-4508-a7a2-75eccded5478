'use client';

import { useState, useEffect, useTransition } from 'react';
import { Chat } from '@/components/chat';
import { DataStreamHandler } from '@/components/data-stream-handler';
import { saveChatModelAsCookie } from './actions';
import type { VisibilityType } from '@/components/visibility-selector';
import { AgentDataProvider } from '@/lib/agent-data-context';
import { SubscriptionDataUpdater } from '@/components/subscription-data-updater';
import { SessionChecker } from '@/components/session-checker';
import { useSession } from 'next-auth/react';

export function AgentChatClient({
  chatId,
  initialMessages,
  modelId,
  visibilityType,
  isReadonly,
  agent,
  bootstrapData,
  clientAuthState,
}: {
  chatId: string;
  initialMessages: Array<any>;
  modelId: string;
  visibilityType: VisibilityType;
  isReadonly: boolean;
  agent: any;
  bootstrapData?: any; // Server-side fetched bootstrap data
  clientAuthState?: { isLoggedIn: boolean; userId?: string }; // Client-side auth state
}) {
  const [selectedModel, setSelectedModel] = useState(modelId);

  // State for tracking pending state during transitions
  const [isPending, startTransition] = useTransition();

  // Handle model change with transitions for better UX
  const handleModelChange = (newModelId: string) => {
    // Update local state immediately for responsive UI
    setSelectedModel(newModelId);

    // Use transition for server action
    startTransition(async () => {
      try {
        // Save the selection to cookie via server action
        const result = await saveChatModelAsCookie(newModelId);
        console.log('Model selection saved:', result);
      } catch (error) {
        console.error('Failed to save model selection:', error);
      }
    });
  };

  // Get client-side session state
  const { data: clientSession, status: sessionStatus } = useSession();

  // Override isReadonly based on client-side session state
  const [clientSideIsReadonly, setClientSideIsReadonly] = useState(isReadonly);

  useEffect(() => {
    // Only run this effect when session status is authenticated or unauthenticated (not loading)
    if (sessionStatus !== 'loading') {
      // IMPORTANT: Always trust the client-side session state as the source of truth
      // This is because the server-side session may be stale due to SSR
      const isLoggedIn = !!clientSession?.user?.id;

      // Log session state mismatch for debugging
      if (clientAuthState && isLoggedIn !== clientAuthState.isLoggedIn) {
        console.log('Session state mismatch detected:', {
          clientIsLoggedIn: isLoggedIn,
          clientUserId: clientSession?.user?.id || 'undefined',
          serverIsLoggedIn: clientAuthState.isLoggedIn,
          serverUserId: clientAuthState.userId || 'undefined',
          sessionStatus,
          chatId,
        });
      }

      // For new chats (no chatId or empty chatId)
      if (!chatId || chatId === 'new') {
        // If logged in, allow editing
        if (isLoggedIn) {
          setClientSideIsReadonly(false);
        } else {
          setClientSideIsReadonly(true);
        }
      }
      // For existing chats, we need to check ownership and visibility
      else if (isLoggedIn && bootstrapData?.chat && clientSession?.user) {
        const chat = bootstrapData.chat;
        const isOwner = clientSession.user.id === chat.userId;
        const isPrivate = chat.visibility === 'private';

        if (isPrivate && !isOwner) {
          // Redirect to a new chat page for this agent
          console.error('Access denied: This is a private chat you do not own');
          // Immediately redirect to a new chat for this agent
          window.location.href = `/agent/${agent.slug}/`;
          // Keep readonly true while redirecting
          setClientSideIsReadonly(true);
        } else {
          // User is either the owner or the chat is not private
          setClientSideIsReadonly(false);
        }
      } else if (!isLoggedIn) {
        // Always readonly for logged-out users
        setClientSideIsReadonly(true);
      } else {
        // Fallback case - if we have a logged-in user but no chat data
        setClientSideIsReadonly(false);
      }
    }
  }, [
    clientSession,
    sessionStatus,
    clientAuthState,
    chatId,
    bootstrapData,
    isReadonly,
    agent.slug, // Added to fix dependency warning
  ]);

  return (
    <>
      {/* Add SessionChecker to debug client-side session state */}
      <SessionChecker clientAuthState={clientAuthState} />

      {/* Add SubscriptionDataUpdater to hydrate the global subscription context */}
      {bootstrapData && (
        <SubscriptionDataUpdater bootstrapData={bootstrapData} />
      )}

      <AgentDataProvider
        agentId={agent.id}
        chatId={chatId}
        initialData={
          bootstrapData || {
            agent,
            profile: {},
            subscription: {
              remainingMessages: 0,
              isPaid: false,
              isCanceled: false,
            },
            votes: [],
          }
        }
      >
        <div className="flex flex-col min-h-screen">
          <Chat
            id={chatId}
            initialMessages={initialMessages}
            selectedChatModel={selectedModel}
            selectedVisibilityType={visibilityType}
            isReadonly={clientSideIsReadonly}
            agent={agent}
            onModelChange={handleModelChange}
          />
          <DataStreamHandler id={chatId} />
        </div>
      </AgentDataProvider>
    </>
  );
}
