import { S3Client, PutObjectCommand, ObjectCannedACL } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from "uuid";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

// Bucket name from environment variables
const bucketName = process.env.S3_BUCKET_NAME || "";

/**
 * Upload a file to S3
 * @param file The file buffer to upload
 * @param fileName Original file name for content type detection
 * @param folder Optional folder path within the bucket
 * @returns The URL of the uploaded file
 */
export async function uploadToS3(
  file: Buffer,
  fileName: string,
  folder: string = "agent-logos"
): Promise<string> {
  // Generate a unique file name to prevent overwrites
  const fileExtension = fileName.split(".").pop() || "";
  const uniqueFileName = `${folder}/${uuidv4()}.${fileExtension}`;
  
  // Determine content type based on file extension
  const contentType = getContentType(fileExtension);

  // Create upload parameters
  const params = {
    Bucket: bucketName,
    Key: uniqueFileName,
    Body: file,
    ContentType: contentType,
    // Remove the ACL parameter since the bucket doesn't support it
  };

  try {
    // Upload to S3
    await s3Client.send(new PutObjectCommand(params));
    
    // Return the public URL
    // Format depends on your bucket configuration - this assumes the bucket is configured for public access
    const region = process.env.AWS_REGION || "us-east-1";
    return `https://${bucketName}.s3.${region}.amazonaws.com/${uniqueFileName}`;
  } catch (error) {
    console.error("Error uploading to S3:", error);
    throw new Error("Failed to upload file to S3");
  }
}

/**
 * Get content type based on file extension
 */
function getContentType(extension: string): string {
  const contentTypes: Record<string, string> = {
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    webp: "image/webp",
    svg: "image/svg+xml",
  };

  return contentTypes[extension.toLowerCase()] || "application/octet-stream";
}
