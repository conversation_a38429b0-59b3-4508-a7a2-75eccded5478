import PDFParser from "pdf2json";
import * as mammoth from "mammoth";
import WordExtractor from "word-extractor";

// Custom error class for text extraction errors
class TextExtractionError extends Error {
  constructor(
    message: string,
    public readonly fileType: string,
    public readonly cause?: unknown
  ) {
    super(message);
    this.name = "TextExtractionError";
  }
}

export async function extractTextFromFile(fileBlob: Blob): Promise<string> {
  const fileType = fileBlob.type;

  if (fileType === "application/pdf") {
    return extractTextFromPdf(fileBlob);
  } else if (fileType === "text/plain" || fileType === "text/markdown") {
    return fileBlob.text();
  } else if (fileType === "text/html" || fileType.includes("html")) {
    // Handle HTML files as plain text for now
    // A more sophisticated approach would use an HTML-to-text library
    return fileBlob.text();
  } else if (
    fileType === "application/msword" ||
    fileType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  ) {
    return extractTextFromWord(fileBlob);
  } else if (fileType === "application/octet-stream") {
    // For generic binary files, try to extract as text first
    // This handles cases where markdown files are detected as octet-stream
    try {
      const text = await fileBlob.text();
      // Basic validation - if it looks like text content, return it
      if (text && text.length > 0 && isTextContent(text)) {
        return text;
      } else {
        throw new TextExtractionError("File appears to be binary, not text: " + fileType, fileType);
      }
    } catch (error) {
      throw new TextExtractionError("Failed to extract text from binary file: " + fileType, fileType);
    }
  } else {
    throw new TextExtractionError("Unsupported file type: " + fileType, fileType);
  }
}

// Helper function to check if content looks like text
function isTextContent(content: string): boolean {
  // Check if the content contains mostly printable characters
  const printableChars = content.replace(/[\s\n\r\t]/g, '').length;
  const totalChars = content.length;
  
  if (totalChars === 0) return false;
  
  // If more than 90% of non-whitespace characters are printable, consider it text
  const printableRatio = printableChars / totalChars;
  
  // Also check for common markdown indicators
  const hasMarkdownIndicators = /[#*_`\[\]\(\)]/.test(content) || 
                                content.includes('##') || 
                                content.includes('**') || 
                                content.includes('```');
  
  return printableRatio > 0.7 || hasMarkdownIndicators;
}

async function extractTextFromPdf(pdfBlob: Blob): Promise<string> {
  try {
    const arrayBuffer = await pdfBlob.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return new Promise<string>((resolve, reject) => {
      // Create a new PDFParser instance
      const pdfParser = new PDFParser(null, true); // Set needRawText to true

      // Set up event handlers
      pdfParser.on("pdfParser_dataError", (errData) => {
        reject(new Error(errData.parserError.message));
      });

      pdfParser.on("pdfParser_dataReady", (pdfData) => {
        try {
          // Option 1: Use getRawTextContent if needRawText was set to true
          const rawText = pdfParser.getRawTextContent();
          if (rawText && rawText.length > 0) {
            resolve(rawText);
            return;
          }

          // Option 2: Extract text from the parsed JSON structure
          let text = "";

          if (pdfData && pdfData.Pages) {
            for (const page of pdfData.Pages) {
              if (page.Texts) {
                for (const textElement of page.Texts) {
                  if (textElement.R) {
                    for (const r of textElement.R) {
                      if (r.T) {
                        // Decode the URI-encoded text
                        text += decodeURIComponent(r.T) + " ";
                      }
                    }
                  }
                }
                text += "\n\n"; // Add spacing between pages
              }
            }
          }

          resolve(text.trim());
        } catch (error) {
          reject(error);
        }
      });

      // Parse the PDF buffer
      pdfParser.parseBuffer(buffer);
    });
  } catch (error) {
    console.error("Error extracting text from PDF:", error);
    throw new TextExtractionError(
      `Failed to extract text from PDF: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      "application/pdf",
      error
    );
  }
}

async function extractTextFromWord(wordBlob: Blob): Promise<string> {
  try {
    const arrayBuffer = await wordBlob.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Check if it's a .docx file (starts with PK signature) or .doc file
    const isDocx = buffer.length > 4 && buffer[0] === 0x50 && buffer[1] === 0x4B;
    
    if (isDocx) {
      // Use mammoth for .docx files
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } else {
      // Use word-extractor for .doc files
      const extractor = new WordExtractor();
      const extracted = await extractor.extract(buffer);
      return extracted.getBody();
    }
  } catch (error) {
    console.error("Error extracting text from Word document:", error);
    throw new TextExtractionError(
      `Failed to extract text from Word document: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      error
    );
  }
}
