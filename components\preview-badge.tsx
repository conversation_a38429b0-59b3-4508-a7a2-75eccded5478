import React from 'react';
import { Badge } from './ui/badge';
import { BeakerIcon } from 'lucide-react';

interface PreviewBadgeProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function PreviewBadge({ size = 'md', className = '' }: PreviewBadgeProps) {
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-sm px-2 py-0.5',
    lg: 'text-base px-2.5 py-1',
  };

  const iconSizes = {
    sm: 'h-2.5 w-2.5',
    md: 'h-3 w-3',
    lg: 'h-4 w-4',
  };

  return (
    <Badge 
      variant="outline" 
      className={`
        bg-gradient-to-r from-blue-200 to-purple-300 
        dark:from-blue-700/40 dark:to-purple-500/40 
        text-blue-800 dark:text-blue-300 
        border-blue-300 dark:border-blue-700/60
        font-medium flex items-center gap-1
        ${sizeClasses[size]} ${className}
      `}
    >
      <BeakerIcon className={iconSizes[size]} />
      <span>PREVIEW</span>
    </Badge>
  );
}
