'use client';

import { useEffect } from 'react';

// Microsoft Clarity configuration
const CLARITY_PROJECT_ID = process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID || 'r2ia2c6wna';

export function ClarityAnalytics() {
  useEffect(() => {
    // Only run on client-side, in production, and on the specific production domain
    if (
      typeof window !== 'undefined' && 
      process.env.NODE_ENV === 'production' && 
      window.location.hostname === 'app.buildthatidea.com'
    ) {
      console.log('Initializing Clarity Analytics on production domain');
      
      // Initialize Microsoft Clarity
      window.clarity = window.clarity || function () { (window.clarity.q = window.clarity.q || []).push(arguments) };
      
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.clarity.ms/tag/${CLARITY_PROJECT_ID}`;
      
      // Add the script to the document
      document.head.appendChild(script);
      
      // Clean up on unmount
      return () => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      };
    } else if (process.env.NODE_ENV === 'production') {
      console.log(`Clarity Analytics not loaded: not on production domain (current: ${window.location.hostname})`);
    }
  }, []);

  // This component doesn't render anything
  return null;
}

// Add TypeScript declaration
declare global {
  interface Window {
    clarity: any;
  }
}
