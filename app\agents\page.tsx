'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowUpR<PERSON>, Bot, Plus, PlusCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { AgentCard, AgentCardSkeleton, Agent } from '@/components/agents/agent-card';

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    document.title = 'My Agents | BuildThatIdea';
  }, []);

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/agents/all');
        
        if (response.ok) {
          const data = await response.json();
          if (data.agents && Array.isArray(data.agents)) {
            setAgents(data.agents);
          }
        }
      } catch (error) {
        console.error('Error fetching agents:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAgents();
  }, []);

  return (
    <div className="p-6 space-y-6 w-full">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">My Agents</h1>
          <p className="text-sm text-muted-foreground">
            Manage all your AI agents
          </p>
        </div>
      </div>

      {/* Agents Grid */}
      <div className="mt-6">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i}>
                <AgentCardSkeleton />
              </div>
            ))}
          </div>
        ) : agents.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {agents.map((agent) => (
              <div key={agent.id}>
                <AgentCard agent={agent} />
              </div>
            ))}
          </div>
        ) : (
          <Card className="border shadow-sm">
            <CardContent className="p-6 flex flex-col items-center justify-center text-center">
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Bot className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-2">No agents yet</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Create your first agent to get started
              </p>
              <ShimmerButton
                shimmerColor="rgba(255, 255, 255, 0.2)"
                shimmerSize="0.1em"
                shimmerDuration="2s"
                className="h-10 rounded-lg bg-gradient-to-r from-[#f97316] to-[#ec4899] text-white font-medium hover:shadow-md transition-all px-4"
                asChild
              >
                <Link href="/create" className="flex items-center gap-2">
                  <PlusCircle className="h-4 w-4" />
                  Create Agent
                </Link>
              </ShimmerButton>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
