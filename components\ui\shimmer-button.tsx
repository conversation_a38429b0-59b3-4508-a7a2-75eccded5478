'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Slot } from '@radix-ui/react-slot';

interface ShimmerButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  shimmerColor?: string;
  shimmerSize?: string;
  shimmerDuration?: string;
  background?: string;
  borderRadius?: string;
  children: React.ReactNode;
  asChild?: boolean;
}

export function ShimmerButton({
  shimmerColor = 'rgba(255, 255, 255, 0.5)',
  shimmerSize = '0.1em',
  shimmerDuration = '2s',
  background = 'linear-gradient(90deg, #f97316, #ec4899)',
  borderRadius = '9999px',
  className,
  children,
  asChild = false,
  ...props
}: ShimmerButtonProps) {
  // Create the shimmer style
  const shimmerStyle = {
    position: 'relative',
    background,
    borderRadius,
    overflow: 'hidden',
  } as React.CSSProperties;
  
  // Create the shimmer animation keyframes
  const shimmerKeyframes = `
    @keyframes shimmer {
      100% {
        transform: translateX(100%);
      }
    }
  `;
  
  if (asChild) {
    return (
      <div className="inline-block" style={shimmerStyle}>
        <style jsx>{shimmerKeyframes}</style>
        <div
          className="absolute inset-0"
          style={{
            background: `linear-gradient(to right, transparent 0%, ${shimmerColor} 50%, transparent 100%)`,
            transform: 'translateX(-100%)',
            animation: `shimmer ${shimmerDuration} infinite`,
          }}
        />
        <Slot
          className={cn(
            'relative z-10 inline-flex items-center justify-center overflow-hidden whitespace-nowrap font-medium transition-all focus:outline-none border border-white/20 shadow-[0_0_0_1px_rgba(255,255,255,0.1)_inset]',
            className
          )}
          style={{
            background: 'transparent',
            borderRadius,
          }}
          {...props}
        >
          {children}
        </Slot>
      </div>
    );
  }
  
  return (
    <button
      className={cn(
        'relative inline-flex items-center justify-center overflow-hidden whitespace-nowrap font-medium transition-all focus:outline-none border border-white/20 shadow-[0_0_0_1px_rgba(255,255,255,0.1)_inset]',
        className
      )}
      style={{
        background,
        borderRadius,
      }}
      {...props}
    >
      {/* Shimmer effect */}
      <span
        className="absolute inset-0"
        style={{
          background: `linear-gradient(to right, transparent 0%, ${shimmerColor} 50%, transparent 100%)`,
          transform: 'translateX(-100%)',
          animation: `shimmer ${shimmerDuration} infinite`,
        }}
      />
      
      {/* Button content */}
      <span className="relative z-10 flex items-center gap-2">
        {children}
      </span>
      
      {/* Keyframes for shimmer animation */}
      <style jsx>{shimmerKeyframes}</style>
    </button>
  );
}
