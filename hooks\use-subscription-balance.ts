import { useState, useEffect } from 'react';

interface SubscriptionBalance {
  tokensUsed: number;
  tokensTotal: number;
  tokensLeft: number;
  tokensPercentage: number;
  
  agentsUsed: number;
  agentsTotal: number;
  agentsLeft: number;
  agentsPercentage: number;
  
  knowledgeBaseUsed: number;
  knowledgeBaseTotal: number;
  knowledgeBaseLeft: number;
  knowledgeBasePercentage: number;
}

interface SubscriptionData {
  currentPlan: {
    id: string;
    name: string;
    monthlyPrice: number;
    yearlyPrice: number;
    tokensAllowed: number;
    knowledgeBaseSize: number; // This is in bytes
    agentsAllowed: number;
    balance?: {
      tokensLeft: number;
      agentsLeft: number;
      knowledgeBaseLeft: number; // This is in bytes
      expiryDate: string | null;
    }
  } | null;
  hasPlan: boolean;
  subscriptionType: 'monthly' | 'yearly' | 'trial' | null;
}

// Helper function to convert bytes to MB
function bytesToMB(bytes: number): number {
  return bytes / (1024 * 1024);
}

export function useSubscriptionBalance() {
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [balanceData, setBalanceData] = useState<SubscriptionBalance>({
    tokensUsed: 0,
    tokensTotal: 0,
    tokensLeft: 0,
    tokensPercentage: 0,
    
    agentsUsed: 0,
    agentsTotal: 0,
    agentsLeft: 0,
    agentsPercentage: 0,
    
    knowledgeBaseUsed: 0,
    knowledgeBaseTotal: 0,
    knowledgeBaseLeft: 0,
    knowledgeBasePercentage: 0
  });

  useEffect(() => {
    const fetchSubscriptionData = async () => {
      setIsLoading(true);
      try {
        // Fetch from our new subscription API
        const response = await fetch('/api/billing/user-subscription');
        
        if (!response.ok) {
          throw new Error('Failed to fetch subscription data');
        }
        
        const data = await response.json();
        setSubscriptionData(data);
        
        // Calculate balance data from the subscription information
        if (data.currentPlan?.balance) {
          const plan = data.currentPlan;
          const balance = plan.balance;
          
          // Calculate used amounts and percentages
          const tokensUsed = plan.tokensAllowed - balance.tokensLeft;
          const tokensPercentage = Math.round((tokensUsed / plan.tokensAllowed) * 100);
          
          const agentsUsed = plan.agentsAllowed - balance.agentsLeft;
          const agentsPercentage = Math.round((agentsUsed / plan.agentsAllowed) * 100);
          
          // For knowledge base, convert from bytes to MB
          // Both knowledgeBaseSize and knowledgeBaseLeft are already in bytes
          const knowledgeBaseTotal = bytesToMB(plan.knowledgeBaseSize);
          const knowledgeBaseLeft = bytesToMB(balance.knowledgeBaseLeft);
          const knowledgeBaseUsed = knowledgeBaseTotal - knowledgeBaseLeft;
          
          // Calculate percentage based on bytes to ensure accuracy
          const knowledgeBasePercentage = Math.round((knowledgeBaseUsed / knowledgeBaseTotal) * 100);
          
          setBalanceData({
            tokensUsed,
            tokensTotal: plan.tokensAllowed,
            tokensLeft: balance.tokensLeft,
            tokensPercentage,
            
            agentsUsed,
            agentsTotal: plan.agentsAllowed,
            agentsLeft: balance.agentsLeft,
            agentsPercentage,
            
            knowledgeBaseUsed,
            knowledgeBaseTotal,
            knowledgeBaseLeft,
            knowledgeBasePercentage
          });
        } else if (data.currentPlan) {
          // If we have a plan but no balance, use plan limits as defaults
          const plan = data.currentPlan;
          
          // Convert knowledge base size from bytes to MB
          const knowledgeBaseTotal = bytesToMB(plan.knowledgeBaseSize);
          
          setBalanceData({
            tokensUsed: 0,
            tokensTotal: plan.tokensAllowed,
            tokensLeft: plan.tokensAllowed,
            tokensPercentage: 0,
            
            agentsUsed: 0,
            agentsTotal: plan.agentsAllowed,
            agentsLeft: plan.agentsAllowed,
            agentsPercentage: 0,
            
            knowledgeBaseUsed: 0,
            knowledgeBaseTotal,
            knowledgeBaseLeft: knowledgeBaseTotal,
            knowledgeBasePercentage: 0
          });
        }
        
        // If we don't have a plan or balance, the default values (zeros) will be used
      } catch (error) {
        console.error('Error fetching subscription data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscriptionData();
  }, []);

  return {
    isLoading,
    subscriptionData,
    balanceData,
    hasPlan: subscriptionData?.hasPlan || false,
    planName: subscriptionData?.currentPlan?.name || '',
    subscriptionType: subscriptionData?.subscriptionType || null
  };
}
