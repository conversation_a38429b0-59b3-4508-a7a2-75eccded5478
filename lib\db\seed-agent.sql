-- Seed an initial agent into the database
INSERT INTO agents (
  id,
  slug,
  "createdAt",
  "updatedAt",
  email,
  "agentName",
  instruction,
  description,
  status,
  visibility,
  monetization,
  logo,
  "quickMessages",
  files
) VALUES (
  gen_random_uuid(), -- Generate a random UUID
  'finance-gpt', -- Human-friendly slug
  NOW(), -- Current timestamp for createdAt
  NOW(), -- Current timestamp for updatedAt
  '<EMAIL>', -- Admin email
  'Finance GPT', -- Agent name
  'You are a helpful financial assistant. Your job is to provide accurate and helpful information about personal finance, investments, budgeting, and financial planning. Always be clear about the limitations of your advice and remind users to consult with a professional financial advisor for personalized advice.', -- Instruction/system prompt
  'Your personal finance assistant, helping with budgeting, investments, and financial planning.', -- Description
  'active', -- Status
  '{"public": true}', -- Visibility (public)
  '{"type": "free", "model": "gpt-4o"}', -- Monetization info
  '{"url": "https://placehold.co/400x400/2563eb/ffffff?text=F"}', -- Logo placeholder
  '["How do I create a budget?", "What are index funds?", "How much should I save for retirement?", "Explain dollar-cost averaging"]', -- Quick messages
  '[]' -- No files initially
);
