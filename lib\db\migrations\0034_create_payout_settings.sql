-- Create payout_settings table
CREATE TABLE IF NOT EXISTS "payout_settings" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL REFERENCES "User"("id") ON DELETE CASCADE,
  "paypal_email" text NOT NULL,
  "created_at" timestamp DEFAULT now(),
  "updated_at" timestamp DEFAULT now()
);

-- Create index for faster lookups by user ID
CREATE INDEX IF NOT EXISTS "payout_settings_user_id_idx" ON "payout_settings" ("userId");
