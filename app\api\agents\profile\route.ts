import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { db } from '@/lib/db';
import {
  agents,
  agentProfiles,
  chat,
  message,
  agentKnowledgeFiles,
} from '@/lib/db/schema';
import { eq, count, and, gt, sum } from 'drizzle-orm';

// Helper function to check if a date is older than 24 hours
function isOlderThan24Hours(date: Date) {
  const oneDayInMs = 24 * 60 * 60 * 1000;
  return new Date().getTime() - date.getTime() > oneDayInMs;
}

// Helper function to update profile with real data
async function updateProfileWithRealData(agentId: string) {
  // Count total messages for this agent
  const messageResult = await db
    .select({ count: count() })
    .from(message)
    .innerJoin(chat, eq(message.chatId, chat.id))
    .where(eq(chat.agentId, agentId));
  const totalChats = messageResult[0]?.count || 0;

  // Get all knowledge files for this agent
  const knowledgeFiles = await db
    .select({
      fileSize: agentKnowledgeFiles.fileSize,
    })
    .from(agentKnowledgeFiles)
    .where(eq(agentKnowledgeFiles.agentId, agentId));

  // Calculate total file size by parsing each file size string to a number
  let totalFileSize = 0;
  for (const file of knowledgeFiles) {
    try {
      const sizeStr = file.fileSize.toString();
      // Simple numeric string
      if (/^\d+$/.test(sizeStr)) {
        totalFileSize += parseInt(sizeStr, 10);
      }
      // Size with MB, KB, etc.
      else if (sizeStr.includes('MB')) {
        const size = parseFloat(sizeStr.replace('MB', '').trim());
        totalFileSize += size * 1024 * 1024; // Convert MB to bytes
      } else if (sizeStr.includes('KB')) {
        const size = parseFloat(sizeStr.replace('KB', '').trim());
        totalFileSize += size * 1024; // Convert KB to bytes
      } else if (sizeStr.includes('GB')) {
        const size = parseFloat(sizeStr.replace('GB', '').trim());
        totalFileSize += size * 1024 * 1024 * 1024; // Convert GB to bytes
      } else {
        // Default to treating as bytes
        totalFileSize += parseInt(sizeStr, 10) || 0;
      }
    } catch (error) {
      console.warn(`Could not parse file size: ${file.fileSize}`);
    }
  }

  // Convert to KB for a more manageable number
  const datasets = Math.round(totalFileSize / 1024);

  return { totalChats, datasets };
}

// Helper function to call the generate-features API
async function generateFeaturesForAgent(agentId: string) {
  try {
    // Check if agent is active before generating features
    const agent = await db.query.agents.findFirst({
      where: eq(agents.id, agentId),
    });
    
    // Only generate features for active agents
    if (!agent || agent.status !== 'active') {
      console.log(`Skipping feature generation for non-active agent: ${agentId}`);
      return null;
    }
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/agents/generate-features`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ agentId }),
    });
    
    if (!response.ok) {
      console.error(`Error generating features: ${response.status}`);
      return null;
    }
    
    const data = await response.json();
    return data.features;
  } catch (error) {
    console.error('Error generating features:', error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const forceGenerate = searchParams.get('forceGenerate') === 'true';
    
    console.log(`GET /api/agents/profile for agent ID: ${agentId}, forceGenerate: ${forceGenerate}`);
    
    if (!agentId) {
      return NextResponse.json(
        { error: 'Agent ID is required' },
        { status: 400 }
      );
    }
    
    // Get the agent to check its status
    const agent = await db.query.agents.findFirst({
      where: eq(agents.id, agentId),
    });
    
    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }
    
    console.log(`Found agent for profile: ${agent.agentName} (${agentId}), Status: ${agent.status}`);
    
    // Check if profile exists
    const existingProfile = await db.query.agentProfiles.findFirst({
      where: eq(agentProfiles.agentId, agentId),
    });
    
    if (existingProfile) {
      console.log(`Found existing profile for agent: ${agentId}, pendingFeatureGeneration: ${existingProfile.pendingFeatureGeneration}, features count: ${existingProfile.features && Array.isArray(existingProfile.features) ? existingProfile.features.length : 0}`);
      
      // If force generate is true or the agent is active and has pending feature generation, generate features
      if (forceGenerate || (agent.status === 'active' && (existingProfile.pendingFeatureGeneration || (existingProfile.features && Array.isArray(existingProfile.features) && existingProfile.features.length === 0)))) {
        console.log(`Triggering feature generation for agent: ${agentId}`);
        
        // Generate features for this agent
        const features = await generateFeaturesForAgent(agentId);
        console.log(`Generated features for agent ${agentId}:`, features);
        
        if (features && Array.isArray(features) && features.length > 0) {
          // Update the profile with the new features
          await db
            .update(agentProfiles)
            .set({
              features,
              pendingFeatureGeneration: false,
              updatedAt: new Date(),
            })
            .where(eq(agentProfiles.agentId, agentId));
          
          // Fetch the updated profile
          const updatedProfile = await db.query.agentProfiles.findFirst({
            where: eq(agentProfiles.agentId, agentId),
          });
          
          return NextResponse.json({ profile: updatedProfile });
        }
      }
      
      // If we couldn't generate features or agent isn't active, return existing profile
      return NextResponse.json({ profile: existingProfile });
    }
    
    // If profile doesn't exist, create one with empty features
    // Create a new profile with default values
    const newProfile = {
      agentId,
      rating: 0,
      totalChats: 0,
      datasets: 0,
      features: [],
      pendingFeatureGeneration: true, // Mark as pending for feature generation
    };
    
    await db.insert(agentProfiles).values(newProfile);
    
    // If the agent is active, generate features immediately
    if (agent.status === 'active') {
      // Generate features for this agent
      const features = await generateFeaturesForAgent(agentId);
      
      if (features && features.length > 0) {
        // Update the profile with the new features
        await db
          .update(agentProfiles)
          .set({
            features,
            pendingFeatureGeneration: false,
            updatedAt: new Date(),
          })
          .where(eq(agentProfiles.agentId, agentId));
      }
    }
    
    // Fetch the final profile to return
    const finalProfile = await db.query.agentProfiles.findFirst({
      where: eq(agentProfiles.agentId, agentId),
    });
    
    return NextResponse.json({ profile: finalProfile });
  } catch (error) {
    console.error('Error in agent profile GET:', error);
    return NextResponse.json(
      { error: 'Failed to get agent profile' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { agentId, rating, totalChats, datasets, model, features } = await request.json();

    if (!agentId) {
      return NextResponse.json(
        { error: 'Agent ID is required' },
        { status: 400 },
      );
    }

    const existingProfile = await db.query.agentProfiles.findFirst({
      where: eq(agentProfiles.agentId, agentId),
    });

    let profile;

    if (existingProfile) {
      // Update existing profile
      profile = await db
        .update(agentProfiles)
        .set({
          rating: rating ?? existingProfile.rating,
          totalChats: totalChats ?? existingProfile.totalChats,
          datasets: datasets ?? existingProfile.datasets,
          model: model ?? existingProfile.model,
          features: features ?? existingProfile.features,
          updatedAt: new Date(),
        })
        .where(eq(agentProfiles.agentId, agentId))
        .returning();
    } else {
      // If no profile exists, get real data first
      const realData = await updateProfileWithRealData(agentId);

      // Try to generate features using the API
      const generatedFeatures = await generateFeaturesForAgent(agentId);

      // Create new profile with generated or default features
      profile = await db
        .insert(agentProfiles)
        .values({
          agentId,
          rating: rating ?? 0,
          totalChats: totalChats ?? realData.totalChats,
          datasets: datasets ?? realData.datasets,
          model: model ?? 'gpt-4',
          features: features ?? generatedFeatures ?? [], // Keep empty if no features are provided or generated
          pendingFeatureGeneration: !features && !generatedFeatures, // Only set to true if no features are available
        })
        .returning();
    }

    return NextResponse.json(profile[0]);
  } catch (error) {
    console.error('Error updating agent profile:', error);
    return NextResponse.json(
      { error: 'Failed to update agent profile' },
      { status: 500 },
    );
  }
}
