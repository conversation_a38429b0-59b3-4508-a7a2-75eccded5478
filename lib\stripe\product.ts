import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-03-31.basil',
});

export interface AgentProductData {
  agentId: string;
  agentName: string;
  description?: string;
  priceAmount: number; // in cents
  interval?: 'month' | 'year';
  currency?: string;
}

export async function createAgentProduct(data: AgentProductData) {
  try {
    // First, create or retrieve the product
    const product = await stripe.products.create({
      name: data.agentName,
      description: data.description || `Subscription for ${data.agentName}`,
      metadata: {
        agentId: data.agentId,
      },
    });

    // Then, create the price for the product
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: data.priceAmount,
      currency: data.currency || 'usd',
      recurring: data.interval ? {
        interval: data.interval,
      } : undefined,
      metadata: {
        agentId: data.agentId,
      },
    });
    console.log("Product created:", product);
    console.log("Price created:", price);
    
    return {
      productId: product.id,
      priceId: price.id,
      product,
      price,
    };
  } catch (error) {
    console.error('Error creating Stripe product and price:', error);
    throw error;
  }
}

export async function getOrCreateAgentProduct(data: AgentProductData) {
  try {
    // Search for existing product with the agent ID in metadata
    const existingProducts = await stripe.products.search({
      query: `metadata['agentId']:'${data.agentId}'`,
    });

    if (existingProducts.data.length > 0) {
      // Product exists, get its price
      const product = existingProducts.data[0];
      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
        limit: 1,
      });

      if (prices.data.length > 0) {
        console.log("Price found:", prices.data[0]);
        return {
          productId: product.id,
          priceId: prices.data[0].id,
          product,
          price: prices.data[0],
        };
      }
    }
 
    // If no existing product or price found, create new ones
    return createAgentProduct(data);
  } catch (error) {
    console.error('Error getting or creating Stripe product:', error);
    throw error;
  }
}

export async function updateAgentProduct(data: AgentProductData & { productId: string }) {
  try {
    // Update the product
    const product = await stripe.products.update(data.productId, {
      name: data.agentName,
      description: data.description || `Subscription for ${data.agentName}`,
      metadata: {
        agentId: data.agentId,
      },
    });

    // Create a new price (Stripe prices cannot be updated)
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: data.priceAmount,
      currency: data.currency || 'usd',
      recurring: data.interval ? {
        interval: data.interval,
      } : undefined,
      metadata: {
        agentId: data.agentId,
      },
    });

    return {
      productId: product.id,
      priceId: price.id,
      product,
      price,
    };
  } catch (error) {
    console.error('Error updating Stripe product:', error);
    throw error;
  }
}
