'use client';

import type { User } from 'next-auth';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useAuth } from './auth-context';

import { PlusIcon, CompassIcon } from '@/components/icons';
import { SidebarHistory } from '@/components/sidebar-history';
import { SidebarUserNav } from '@/components/sidebar-user-nav';
import { Button } from '@/components/ui/button';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';

export function AppSidebar({ user: serverUser }: { user: User | undefined }) {
  const router = useRouter();
  const pathname = usePathname();
  const { setOpenMobile } = useSidebar();
  const [agentId, setAgentId] = useState<string | undefined>(undefined);
  const [currentAgent, setCurrentAgent] = useState<any | undefined>(undefined);
  
  // Use client-side session to ensure UI updates when auth state changes
  const { data: clientSession } = useSession();
  const { authState } = useAuth();
  
  // Use either the client-side user or server-side user
  const user = clientSession?.user || serverUser;

  // Extract agent slug from pathname if we're on an agent page
  // The URL pattern is /agent/[slug] or /agent/[slug]/[chatId]
  const agentMatch = pathname.match(/^\/agent\/([^\/]+)(?:\/([^\/]+))?/);
  const agentSlug = agentMatch ? agentMatch[1] : undefined;
  // We don't need to use the chatId for fetching agent data, just for UI highlighting
  const chatId = agentMatch && agentMatch[2] ? agentMatch[2] : undefined;



  // Fetch agent data when slug changes
  useEffect(() => {
    async function fetchAgentData() {
      if (agentSlug) {
        try {
          const response = await fetch(`/api/agents?slug=${agentSlug}`);
          if (response.ok) {
            const data = await response.json();
            if (data && data.agent && data.agent.id) {
              setAgentId(data.agent.id);
              setCurrentAgent(data.agent);
            } else {
            }
          } else {
          }
        } catch (error) {
        }
      } else {
        setAgentId(undefined);
        setCurrentAgent(undefined);
      }
    }

    fetchAgentData();
  }, [agentSlug, chatId]);

  // Handle new chat button click
  const handleNewChat = () => {
    setOpenMobile(false);
    
    // Check if we're in an agent chat
    if (agentSlug) {
      // If we're in an agent chat, create a new chat with the same agent
      router.push(`/agent/${agentSlug}`);
    } else {
      // Otherwise, create a new regular chat
      router.push('/');
    }
    router.refresh();
  };

  // Login button component for the sidebar
  function LoginButton() {
    const { showAuthModal } = useAuth();
    const { setOpenMobile } = useSidebar();
    
    const handleSignIn = () => {
      // First close the mobile sidebar
      setOpenMobile(false);
      
      // Then show the auth modal after a small delay
      setTimeout(() => {
        showAuthModal();
      }, 100);
    };
    
    return (
      <SidebarMenu>
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={handleSignIn}
        >
          <span className="truncate">Sign In</span>
        </Button>
      </SidebarMenu>
    );
  }

  return (
    <Sidebar className="group-data-[side=left]:border-r-0">
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex flex-row justify-between items-center">
            <Link
              href={agentSlug ? `/agent/${agentSlug}` : "/"}
              onClick={() => {
                setOpenMobile(false);
              }}
              className="flex flex-row gap-2 items-center"
            >
              {currentAgent ? (
                <>
                  {currentAgent.logo ? (
                    <img 
                      src={currentAgent.logo} 
                      alt={currentAgent.agentName} 
                      className="w-8 h-8 object-cover rounded-md shadow-sm"
                    />
                  ) : null}
                  <span className="text-lg font-semibold hover:bg-muted rounded-md cursor-pointer">
                    {currentAgent.agentName || 'Agent'}
                  </span>
                </>
              ) : (
                <span className="text-lg font-semibold px-2 hover:bg-muted rounded-md cursor-pointer">
                  {currentAgent?.agentName || 'Chatbot'}
                </span>
              )}
            </Link>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  type="button"
                  className="p-2 h-fit"
                  onClick={handleNewChat}
                >
                  <PlusIcon />
                </Button>
              </TooltipTrigger>
              <TooltipContent align="end">New Chat</TooltipContent>
            </Tooltip>
          </div>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {/* Removed Discover Agents button */}
        <SidebarHistory user={user} agentId={agentId} />
      </SidebarContent>
      <SidebarFooter>
        {user && <SidebarUserNav user={user} />}
        {!user && <LoginButton />}
      </SidebarFooter>
    </Sidebar>
  );
}
