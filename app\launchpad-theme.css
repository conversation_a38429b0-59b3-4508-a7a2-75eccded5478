/* 
 * Launchpad Theme - Global CSS Variables
 * Based on AI Agents Launchpad design system
 */

:root {
  /* Base colors from AI Agents Launchpad */
  --launchpad-orange: #f97316;
  --launchpad-pink: #ec4899;
  --launchpad-orange-dark: #ea580c;
  --launchpad-pink-dark: #db2777;
  
  /* Gradients */
  --launchpad-gradient: linear-gradient(to right, var(--launchpad-orange), var(--launchpad-pink));
  --launchpad-gradient-hover: linear-gradient(to right, var(--launchpad-orange-dark), var(--launchpad-pink-dark));
  
  /* Override the Fin theme colors with Launchpad colors */
  --primary: 20.5 90.2% 48.2%; /* Orange */
  --primary-foreground: 60 9.1% 97.8%;
  --ring: 20.5 90.2% 48.2%;
}

/* Button variants */
.btn-launchpad-primary {
  background: var(--launchpad-gradient);
  color: white;
  border: none;
  transition: all 150ms ease;
}

.btn-launchpad-primary:hover {
  background: var(--launchpad-gradient-hover);
}

.btn-launchpad-primary:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.5);
}

.btn-launchpad-secondary {
  background: white;
  color: var(--launchpad-orange);
  border: 1px solid var(--launchpad-orange);
  transition: all 150ms ease;
}

.btn-launchpad-secondary:hover {
  background: rgba(249, 115, 22, 0.05);
}

.btn-launchpad-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

/* Text styles */
.text-gradient {
  background: var(--launchpad-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Card styles */
.card-launchpad {
  border-radius: 1rem;
  border: 1px solid #f3f3f3;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  background-color: white;
  overflow: hidden;
}

/* Input styles */
.input-launchpad {
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.input-launchpad:focus {
  border-color: var(--launchpad-orange);
  outline: none;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

/* Background styles */
.bg-gradient-launchpad {
  background: var(--launchpad-gradient);
}

.bg-subtle-launchpad {
  background: linear-gradient(to bottom, #ffffff, #f9f5ff);
}

/* Shimmer effect */
.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}
