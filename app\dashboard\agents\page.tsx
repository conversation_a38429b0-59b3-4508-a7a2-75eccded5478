'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowUpR<PERSON>, Bot, Plus, PlusCircle } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AgentCard, AgentCardSkeleton, Agent as AgentType } from '@/components/agents/agent-card';
import { ShimmerButton } from '@/components/ui/shimmer-button';

interface Agent {
  id: string;
  agentName: string;
  slug: string;
  description: string;
  status: 'active' | 'draft' | 'inactive';
  visibility: 'public' | 'private';
  accessLevel: 'free' | 'subscription' | 'lifetime';
  price?: string;
  logo?: string;
  createdAt: string;
  updatedAt: string;
}

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchAgents = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/agents/all');
      
      if (response.ok) {
        const data = await response.json();
        if (data.agents && Array.isArray(data.agents)) {
          setAgents(data.agents);
        }
      }
    } catch (error) {
      console.error('Error fetching agents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAgents();
  }, []);

  return (
    <div className="p-6 space-y-6 w-full">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">My Agents</h1>
          <p className="text-sm text-muted-foreground">
            Manage all your AI agents
          </p>
        </div>
      </div>

      {/* Agents Grid */}
      <div className="mt-6">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <AgentCardSkeleton key={i} />
            ))}
          </div>
        ) : agents.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {agents.map((agent) => (
              <AgentCard 
                key={agent.id} 
                agent={agent} 
              />
            ))}
          </div>
        ) : (
          <Card className="border shadow-sm">
            <CardContent className="p-6 flex flex-col items-center justify-center text-center">
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Bot className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-2">No agents yet</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Create your first agent to get started
              </p>
              <ShimmerButton
                shimmerColor="rgba(255, 255, 255, 0.2)"
                shimmerSize="0.1em"
                shimmerDuration="2s"
                className="h-10 rounded-lg bg-gradient-to-r from-[#f97316] to-[#ec4899] text-white font-medium hover:shadow-md transition-all px-4"
                asChild
              >
                <Link href="/create" className="flex items-center gap-2">
                  <PlusCircle className="h-4 w-4" />
                  Create Agent
                </Link>
              </ShimmerButton>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
