import { db } from "@/lib/db/client";
import { agents } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export interface ValidateAgentParams {
  agentSlug: string;
  price?: number;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  suggestedPrice?: number;
}

export async function validateAgentForCheckout({ agentSlug, price }: ValidateAgentParams): Promise<ValidationResult> {
  try {
    // Check if agent exists and is active
    const existingAgent = await db
      .select()
      .from(agents)
      .where(eq(agents.slug, agentSlug))
      .limit(1);

    if (existingAgent.length === 0) {
      return {
        isValid: false,
        error: "Agent not found",
      };
    }

    const agent = existingAgent[0];

    // Check if agent is in draft status
    if (agent.status === "draft") {
      return {
        isValid: false,
        error: "Agent is still in draft status",
      };
    }

    // Validate price if provided
    if (price !== undefined) {
      // Minimum price check
      if (price < 100) { // $1.00 in cents
        return {
          isValid: false,
          error: "Price must be at least $1.00",
          suggestedPrice: 100,
        };
      }

      // Maximum price check
      if (price > 100000) { // $1,000.00 in cents
        return {
          isValid: false,
          error: "Price cannot exceed $1,000.00",
          suggestedPrice: 100000,
        };
      }
    }

    return {
      isValid: true,
    };
  } catch (error) {
    console.error("Error validating agent:", error);
    return {
      isValid: false,
      error: "Failed to validate agent",
    };
  }
}
