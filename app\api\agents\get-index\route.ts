import { NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';
import { auth } from '@/auth';
import { encrypt } from '@/lib/utils';

// Configuration constant
const AGENTS_PER_INDEX = 20; // Configurable limit of agents per index

// Initialize Redis client
const redis = new Redis({
  url: process.env.KV_REST_API_URL || '',
  token: process.env.KV_REST_API_TOKEN || '',
});

// Logging wrapper for Redis operations
async function loggedRedisOperation(operation: string, callback: Function, ...args: any[]) {
  console.log(`[REDIS] Starting operation: ${operation} with args:`, args);
  try {
    const result = await callback(...args);
    console.log(`[REDIS] Completed operation: ${operation} with result:`, result);
    return result;
  } catch (error) {
    console.error(`[REDIS] Error in operation: ${operation}`, error);
    throw error;
  }
}

export async function POST(request: Request) {
  const operationId = Date.now().toString();
  console.log(`[${operationId}] Starting get-index request`);
  
  try {

    // const session = await auth();
    // if (!session?.user?.id) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    // Parse request body to get the agent ID
    const body = await request.json();
    const { agentId } = body;
    console.log(`[${operationId}] Processing request for agentId: ${agentId}`);

    if (!agentId) {
      console.log(`[${operationId}] Missing required agentId`);
      return NextResponse.json({ error: 'Agent ID is required' }, { status: 400 });
    }

    // Check if agent already has an assigned index
    const existingIndexId = await loggedRedisOperation(
      'get agent:index',
      redis.get.bind(redis),
      `agent:index:${agentId}`
    );
    
    console.log(`[${operationId}] Existing index for agent: ${existingIndexId || 'none'}`);
    
    if (existingIndexId) {
      // Get existing index info
      const indexInfo = await loggedRedisOperation(
        'get index:info',
        redis.get.bind(redis),
        `index:info:${existingIndexId}`
      );
      
      if (indexInfo) {
        console.log(`[${operationId}] Returning existing index info for: ${existingIndexId}`);
        return NextResponse.json(indexInfo);
      }
      
      console.log(`[${operationId}] Index info not found for existing index: ${existingIndexId}`);
      // If index info not found, continue to assign a new index
    }

    // Find an index with space or create a new one
    console.log(`[${operationId}] Finding or creating index for agent: ${agentId}`);
    const indexId = await findOrCreateIndex(operationId);
    console.log(`[${operationId}] Selected index: ${indexId}`);
    
    // Assign the agent to this index
    await assignAgentToIndex(agentId, indexId, operationId);

    // Return the index info
    const indexInfo = await loggedRedisOperation(
      'get final index:info',
      redis.get.bind(redis),
      `index:info:${indexId}`
    );
    
    console.log(`[${operationId}] Returning index info for: ${indexId}`);
    return NextResponse.json(indexInfo);
  } catch (error) {
    console.error(`[${operationId}] Error in get-index route:`, error);
    return NextResponse.json(
      { error: 'Failed to get index' },
      { status: 500 },
    );
  }
}

// Helper function to find an index with available capacity or create a new one
async function findOrCreateIndex(operationId: string): Promise<string> {
  // Get all indices we're tracking
  const indexKeys = await loggedRedisOperation(
    'keys index:counter:*',
    redis.keys.bind(redis),
    'index:counter:*'
  );
  
  console.log(`[${operationId}] Found ${indexKeys.length} existing indices`);
  
  // Extract index IDs from keys
  const indexIds = indexKeys.map(key => key.replace('index:counter:', ''));
  
  // Find an index with available capacity
  for (const indexId of indexIds) {
    const count = await loggedRedisOperation(
      `get index:counter:${indexId}`,
      redis.get.bind(redis),
      `index:counter:${indexId}`
    );
    
    console.log(`[${operationId}] Index ${indexId} has ${count} agents (limit: ${AGENTS_PER_INDEX})`);
    
    if (count !== null && Number(count) < AGENTS_PER_INDEX) {
      console.log(`[${operationId}] Selected existing index: ${indexId} with agent count: ${count}`);
      return indexId;
    }
  }
  
  // No available index found, create a new one
  console.log(`[${operationId}] No available index found, creating new one`);
  return createNewIndex(operationId);
}

// Helper function to create a new index
async function createNewIndex(operationId: string): Promise<string> {
  // Generate a unique ID for the new index
  const indexId = `idx_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
  console.log(`[${operationId}] Creating new index with ID: ${indexId}`);
  
  // Call the generate-index API to create a new index in Upstash
  console.log(`[${operationId}] Calling generate-index API to create index in Upstash`);
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_APP_URL}/api/agents/generate-index`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: indexId,
      }),
    },
  );
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`[${operationId}] Failed to create index in Upstash:`, errorText);
    throw new Error('Failed to create index in Upstash');
  }
  
  const indexData = await response.json();
  console.log(`[${operationId}] Successfully created index in Upstash with endpoint: ${indexData.endpoint}`);
  
  // Encrypt the token
  const encryptedToken = encrypt(indexData.token);
  
  // Store index info in Redis
  await loggedRedisOperation(
    'set index:info',
    redis.set.bind(redis),
    `index:info:${indexId}`,
    {
      endpoint: indexData.endpoint,
      token: encryptedToken,
    }
  );
  
  // Initialize counter and agents set
  await loggedRedisOperation(
    'set index:counter',
    redis.set.bind(redis),
    `index:counter:${indexId}`,
    0
  );
  
  await loggedRedisOperation(
    'sadd index:agents',
    redis.sadd.bind(redis),
    `index:agents:${indexId}`,
    []
  );
  
  console.log(`[${operationId}] Index ${indexId} initialized in Redis`);
  return indexId;
}

// Helper function to assign an agent to an index
async function assignAgentToIndex(agentId: string, indexId: string, operationId: string): Promise<void> {
  console.log(`[${operationId}] Assigning agent ${agentId} to index ${indexId}`);
  
  // Store the index ID for this agent
  await loggedRedisOperation(
    'set agent:index',
    redis.set.bind(redis),
    `agent:index:${agentId}`,
    indexId
  );
  
  // Add agent to the index's set of agents
  await loggedRedisOperation(
    'sadd index:agents',
    redis.sadd.bind(redis),
    `index:agents:${indexId}`,
    agentId
  );
  
  // Increment the agent counter for this index
  await loggedRedisOperation(
    'incr index:counter',
    redis.incr.bind(redis),
    `index:counter:${indexId}`
  );
  
  console.log(`[${operationId}] Successfully assigned agent ${agentId} to index ${indexId}`);
}