"use client";

import { useRouter } from 'next/navigation';
import { signOut } from '@/auth';
import { resetAppState } from '@/lib/reset-app-state';

export const SignOutForm = () => {
  const router = useRouter();
  
  // <PERSON>le sign out with browser storage clearing
  const handleSignOut = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Reset all application state (contexts, browser storage, cookies)
      await resetAppState();
      console.log('App state reset before sign-out');
      
      // Sign out using NextAuth
      await signOut();
    } catch (error) {
      console.error('Error during sign-out:', error);
      
      // Fallback: try to sign out directly if resetAppState fails
      try {
        console.warn('Proceeding with sign-out after resetAppState failure');
        await signOut();
      } catch (fallbackError) {
        console.error('Error in sign-out fallback:', fallbackError);
        // Last resort: force redirect
        window.location.href = '/login';
      }
    }
  };

  return (
    <form
      className="w-full"
      onSubmit={handleSignOut}
    >
      <button
        type="submit"
        className="w-full text-left px-1 py-0.5 text-red-500"
      >
        Sign Out
      </button>
    </form>
  );
};
