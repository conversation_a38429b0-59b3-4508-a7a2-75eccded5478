import { sql } from 'drizzle-orm';
import { db } from '../client';

/**
 * Migration to add token count columns to the Message_v2 table
 */
export async function addTokenCountColumns() {
  console.log('🔄 Adding token count columns to Message_v2 table...');
  
  try {
    // Add prompt_tokens column
    await db.execute(sql`
      ALTER TABLE "Message_v2"
      ADD COLUMN IF NOT EXISTS "prompt_tokens" INTEGER;
    `);
    
    // Add completion_tokens column
    await db.execute(sql`
      ALTER TABLE "Message_v2"
      ADD COLUMN IF NOT EXISTS "completion_tokens" INTEGER;
    `);
    
    // Add total_tokens column
    await db.execute(sql`
      ALTER TABLE "Message_v2"
      ADD COLUMN IF NOT EXISTS "total_tokens" INTEGER;
    `);
    
    console.log('✅ Token count columns added successfully!');
  } catch (error) {
    console.error('❌ Failed to add token count columns:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  addTokenCountColumns()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
