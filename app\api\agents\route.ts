import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { agents, agentProfiles } from '@/lib/db/schema';
import {
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  gte,
  inArray,
  like,
  ne,
  sql,
} from 'drizzle-orm';
import { auth } from '@/auth';
import { z } from 'zod';
import { createProductAndPriceForAgentOnStripe } from '@/lib/stripe/create-product';
import {
  agentProfileAndFeatureGenerationHandler,
  checkSubscriptionLimit,
  updateCreatorBalance,
} from '@/app/utils/agent-utils';

// Form validation schema
const agentSchema = z.object({
  id: z.string().optional().nullable(),
  agentName: z.string().min(4, 'Name must be at least 4 characters'),
  slug: z.string().min(4, 'URL path must be at least 4 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  instruction: z
    .string()
    .min(10, 'Instructions must be at least 10 characters')
    .default(''),
  model: z.string().default('gpt-3.5-turbo'),
  isPublic: z.boolean().default(false),
  isPaid: z.boolean().default(false),
  pricingType: z.enum(['subscription', 'lifetime']).default('subscription'),
  price: z.string().default(''),
  accessLevel: z.enum(['free', 'subscription', 'lifetime']).default('free'),
  quickMessages: z.array(z.string()).default([]),
  selectedLinks: z
    .array(
      z.object({
        website: z.string(),
        links: z.array(
          z.object({
            url: z.string(),
            status: z.enum(['selected', 'active', 'to-delete', 'removed']),
          }),
        ),
      }),
    )
    .default([]),
  isDraft: z.boolean().optional(),
  logo: z.union([z.string(), z.null()]).optional(),
});

// Draft schema with partial validation
const draftSchema = z.object({
  id: z.string().optional().nullable(),
  agentName: z.string().optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  instruction: z.string().optional(),
  model: z.string().optional(),
  isPublic: z.boolean().optional(),
  isPaid: z.boolean().optional(),
  pricingType: z.enum(['subscription', 'lifetime']).optional(),
  price: z.string().optional(),
  accessLevel: z.enum(['free', 'subscription', 'lifetime']).optional(),
  quickMessages: z.array(z.string()).optional(),
  selectedLinks: z
    .array(
      z.object({
        website: z.string(),
        links: z.array(
          z.object({
            url: z.string(),
            status: z.enum(['selected', 'active', 'to-delete', 'removed']),
          }),
        ),
      }),
    )
    .optional(),
  isDraft: z.boolean().optional(),
  logo: z.union([z.string(), z.null()]).optional(),
});

export async function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl;
  const slug = searchParams.get('slug');
  const id = searchParams.get('id');
  const draftId = searchParams.get('draftId');

  if (!slug && !id && !draftId) {
    return Response.json(
      { message: 'Either slug, id, or draftId must be provided!' },
      { status: 400 },
    );
  }

  try {
    // Verify authentication
    const session = await auth();
    if (!session || !session.user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    if (!userId) {
      return Response.json({ message: 'User ID not found' }, { status: 401 });
    }

    // Define the fields to select
    const fields = {
      id: agents.id,
      agentName: agents.agentName,
      slug: agents.slug,
      description: agents.description,
      instruction: agents.instruction,
      model: agents.model,
      visibility: agents.visibility,
      accessLevel: agents.accessLevel,
      price: agents.price,
      quickMessages: agents.quickMessages,
      selectedLinks: agents.selectedLinks,
      logo: agents.logo,
      status: agents.status,
      createdAt: agents.createdAt,
      updatedAt: agents.updatedAt,
      files: agents.files, // Added files field to include in the response
    };

    let agent;

    if (draftId === 'true') {
      // Find the most recent draft agent for the user
      const drafts = await db
        .select(fields)
        .from(agents)
        .where(
          and(
            eq(agents.userId, userId),
            eq(agents.status, 'draft'),
            ne(agents.status, 'deleted'),
          ),
        )
        .orderBy(desc(agents.updatedAt))
        .limit(1);
      // If no draft found, return empty response with 200 status
      if (drafts.length === 0) {
        return Response.json({ agent: null }, { status: 200 });
      }

      agent = drafts[0];
    } else if (draftId) {
      // Find a specific draft agent by ID
      [agent] = await db
        .select(fields)
        .from(agents)
        .where(
          and(
            eq(agents.id, draftId),
            eq(agents.userId, userId),
            eq(agents.status, 'draft'),
            ne(agents.status, 'deleted'),
          ),
        );
    } else if (slug) {
      // Find agent by slug, optionally filtering for drafts
      const isDraft = searchParams.get('isDraft') === 'true';
      const conditions = [
        eq(agents.slug, slug),
        ne(agents.status, 'deleted'),
        ...(isDraft ? [eq(agents.status, 'draft')] : []),
      ];
      [agent] = await db
        .select(fields)
        .from(agents)
        .where(and(...conditions));
    } else if (id) {
      // Find agent by ID
      [agent] = await db
        .select(fields)
        .from(agents)
        .where(and(eq(agents.id, id), ne(agents.status, 'deleted')));
    }

    if (!agent) {
      return Response.json({ message: 'Agent not found!' }, { status: 404 });
    }

    // Parse JSON fields if they're stored as strings
    if (agent.quickMessages && typeof agent.quickMessages === 'string') {
      try {
        agent.quickMessages = JSON.parse(agent.quickMessages);
      } catch (e) {
        agent.quickMessages = [];
      }
    }

    // Parse selectedLinks field if it's stored as a string
    if (agent.selectedLinks && typeof agent.selectedLinks === 'string') {
      try {
        agent.selectedLinks = JSON.parse(agent.selectedLinks);
      } catch (e) {
        agent.selectedLinks = [];
      }
    }

    // Parse files field if it's stored as a string
    if (agent.files && typeof agent.files === 'string') {
      try {
        agent.files = JSON.parse(agent.files);
      } catch (e) {
        agent.files = [];
      }
    }

    // Return the agent data
    return Response.json({ agent });
  } catch (error) {
    // console.error('Failed to fetch agent:', error);
    return Response.json(
      { message: 'Failed to fetch agent!' },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth();
    if (!session || !session.user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    if (!userId) {
      return Response.json({ message: 'User ID not found' }, { status: 401 });
    }

    // Parse request body
    const data = await request.json();

    // Check if this is a draft save or a final submission
    const isDraft = data.isDraft === true;

    // Determine schema based on whether this is a draft or final submission
    let validationSchema = isDraft ? draftSchema : agentSchema;

    // call the function here
    if (!isDraft) {
      const result = await checkSubscriptionLimit(userId);
      if (!result) {
        return Response.json(
          {
            message: 'Subscription limit reached',
          },
          { status: 400 },
        );
      }
    }

    // Validate data
    try {
      validationSchema.parse(data);
    } catch (validationError: any) {
      //console.error('Validation error:', validationError.errors);
      return Response.json(
        {
          message: 'Validation failed',
          errors: validationError.errors,
        },
        { status: 400 },
      );
    }

    // Prepare agent data for database
    const agentData: any = {
      userId,
      agentName: data.agentName,
      slug: data.slug,
      description: data.description,
      instruction:
        data.instruction ||
        `You are ${data.agentName}, an AI assistant that helps users with their questions related to ${data.description}.`,
      model: data.model === 'chat-model' ? 'gpt-4o' : data.model || 'gpt-4o',
      visibility: data.isPublic ? 'public' : 'private',
      selectedLinks: data.selectedLinks || [],
    };

    // Only set status to draft for new agents, preserve status for existing ones
    if (!data.id) {
      agentData.status = isDraft ? 'draft' : 'active';
    } else if (!isDraft) {
      // Only update status to active if this is a final submission
      agentData.status = 'active';
    }
    // For existing agents with isDraft=true, we don't set the status at all to preserve it

    // Set monetization fields
    if (data.isPaid) {
      agentData.accessLevel = data.pricingType || 'subscription';
      agentData.price = data.price ? Number.parseFloat(data.price) : 4.99;
    } else {
      agentData.accessLevel = 'free';
      agentData.price = 0;
    }

    // Add logo if provided
    if (data.logo) {
      if (typeof data.logo === 'object' && data.logo.url) {
        agentData.logo = data.logo.url;
      } else if (typeof data.logo === 'string') {
        agentData.logo = data.logo;
      }
    }

    // Add quick messages if provided
    if (data.quickMessages) {
      agentData.quickMessages = data.quickMessages;
    }

    // Add selected links if provided
    if (data.selectedLinks) {
      agentData.selectedLinks = data.selectedLinks;
    }

    // Add files if provided
    if (data.files && Array.isArray(data.files)) {
      agentData.files = data.files;
    }

    let result;

    // Create new agent
    try {
      // Create a new agent
      const newAgentId = crypto.randomUUID();

      // Ensure files are properly included in the agent data
      if (data.files && Array.isArray(data.files) && data.files.length > 0) {
        agentData.files = data.files;
      }

      try {
        result = await db
          .insert(agents)
          .values({
            ...agentData,
            id: newAgentId,
            createdAt: new Date(),
          })
          .returning();
        if (agentData.status === 'active') {
          await updateCreatorBalance(userId);
          //console.log(`Agent status changed from draft to active. Triggering feature generation for agent: ${agentId}`);
          await agentProfileAndFeatureGenerationHandler(
            newAgentId,
            agentData.model,
          );
          if (agentData.price > 0) {
            // Use parseFloat instead of parseInt to handle decimal values correctly
            const priceInCents = Math.round(parseFloat(agentData.price) * 100);
            await createProductAndPriceForAgentOnStripe(
              agentData.slug,
              priceInCents,
              newAgentId,
              isDraft ? 'inactive' : 'active',
            );
          }
        }
      } catch (insertError: any) {
        // Re- throw other errors
        throw insertError;
      }
      // Track draft agent creation in ConvertKit if this is a draft agent
      if (isDraft && session?.user?.email) {
        try {
          const { trackDraftAgentCreated } = await import('@/lib/user-journey');
          await trackDraftAgentCreated(session.user.email, agentData.agentName);
          console.log(
            `[ConvertKit] Added HAS_DRAFT_AGENT tag for ${agentData.agentName}`,
          );
        } catch (error) {
          // Don't fail the agent creation if tracking fails
          console.error('[ConvertKit] Error tracking draft agent:', error);
        }
      }

      return Response.json(
        {
          message: 'Agent created successfully',
          agent: result[0],
        },
        { status: 201 },
      );
    } catch (error) {
      // console.error('Error creating new agent:', error);

      // Create a structured error response with detailed information
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorDetails =
        error instanceof Error && 'code' in error
          ? {
              code: (error as any).code,
              detail: (error as any).detail,
              constraint: (error as any).constraint_name,
            }
          : undefined;

      return Response.json(
        {
          message: 'Failed to create new agent',
          error: errorMessage,
          details: errorDetails,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error with agent:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return Response.json(
      { message: 'Failed to process agent', error: errorMessage },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session || !session.user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 });
    }
    const userId = session.user.id;
    if (!userId) {
      return Response.json({ message: 'User ID not found' }, { status: 401 });
    }

    // Parse request body
    const data = await request.json();

    // Check for agent ID in both the root object and inside an agent object
    const agentId = data.id ?? data.agent?.id;

    if (!agentId) {
      return NextResponse.json(
        { error: 'Agent ID is required' },
        { status: 400 },
      );
    }

    // Extract agent data, handling both formats
    const agentData = data.agent || data;

    // Get the agent with the provided ID
    const existingAgent = await db
      .select()
      .from(agents)
      .where(eq(agents.id, agentId));

    if (existingAgent.length === 0) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }

    // Prepare agent data for update
    const updatedAgentData: any = {
      agentName: agentData.agentName,
      slug: agentData.slug,
      description: agentData.description,
      instruction: agentData.instruction || existingAgent[0].instruction,
      model:
        agentData.model === 'chat-model'
          ? 'gpt-4o'
          : agentData.model || existingAgent[0].model || 'gpt-4o',
      visibility:
        agentData.visibility ||
        (agentData.isPublic ? 'public' : 'private') ||
        existingAgent[0].visibility,
      accessLevel:
        agentData.accessLevel ||
        (agentData.isPaid ? agentData.pricingType || 'subscription' : 'free') ||
        existingAgent[0].accessLevel,
      logo: agentData.logo || existingAgent[0].logo,
      price:
        agentData.price !== undefined
          ? Number.parseFloat(agentData.price)
          : existingAgent[0].price,
      quickMessages:
        agentData.quickMessages || existingAgent[0].quickMessages || [],
      selectedLinks:
        agentData.selectedLinks || existingAgent[0].selectedLinks || [],
      files: agentData.files || existingAgent[0].files || [],
      endpoint: agentData.endpoint || existingAgent[0].endpoint,
      token: agentData.token || existingAgent[0].token,
      updatedAt: new Date(),
      status: agentData.status || existingAgent[0].status,
    };

    // Update the agent
    const updatedAgent = await db
      .update(agents)
      .set(updatedAgentData)
      .where(eq(agents.id, agentId))
      .returning();

    if (!updatedAgent || updatedAgent.length === 0) {
      throw new Error('Failed to update agent');
    }

    // Track draft agent in ConvertKit if this is a draft agent
    if (updatedAgentData.status === 'draft' && session?.user?.email) {
      try {
        const { trackDraftAgentCreated } = await import('@/lib/user-journey');
        await trackDraftAgentCreated(
          session.user.email,
          updatedAgentData.agentName,
        );
        console.log(
          `[ConvertKit] Added HAS_DRAFT_AGENT tag for ${updatedAgentData.agentName}`,
        );
      } catch (error) {
        // Don't fail the agent update if tracking fails
        console.error('[ConvertKit] Error tracking draft agent:', error);
      }
    }

    // Check if status is changing from draft to active
    //console.log(`PUT /api/agents - Status check: previous=${existingAgent[0].status}, new=${updatedAgentData.status}`);
    if (
      existingAgent[0].status === 'draft' &&
      updatedAgentData.status === 'active'
    ) {
      if (agentData.price > 0) {
        // Use parseFloat instead of parseInt to handle decimal values correctly
        const priceInCents = Math.round(parseFloat(agentData.price) * 100);
        await createProductAndPriceForAgentOnStripe(
          agentData.slug,
          priceInCents,
          agentId,
          agentData.isDraft ? 'inactive' : 'active',
        );
      }

      // Create a subscription with 25 free messages for the creator
      try {
        const { getOrCreateUserSubscription } = await import(
          '@/lib/db/subscription-queries'
        );
        await getOrCreateUserSubscription({
          userId,
          agentId,
          isCreator: true,
        });
        console.log(
          `Created subscription with 25 free messages for creator of agent: ${agentId}`,
        );
      } catch (error) {
        console.error('Error creating free subscription for creator:', error);
        // Don't fail the agent activation if subscription creation fails
      }

      // Check if there are any remaining draft agents and update ConvertKit tag
      if (session?.user?.email) {
        try {
          // Count remaining draft agents for this user (excluding the one just activated)
          const draftAgentsCount = await db
            .select({ value: count() })
            .from(agents)
            .where(
              and(
                eq(agents.userId, userId),
                eq(agents.status, 'draft'),
                // Exclude the current agent that's being activated
                ne(agents.id, agentId),
              ),
            );

          const remainingDraftCount = draftAgentsCount[0]?.value || 0;
          console.log(
            `[ConvertKit] User has ${remainingDraftCount} remaining draft agents after activating ${updatedAgentData.agentName}`,
          );

          // Only remove the tag if there are no other draft agents
          const { trackAgentActivated } = await import('@/lib/user-journey');
          await trackAgentActivated(session.user.email, remainingDraftCount);

          if (remainingDraftCount <= 0) {
            console.log(
              '[ConvertKit] Removed HAS_DRAFT_AGENT tag - no draft agents remaining',
            );
          } else {
            console.log(
              `[ConvertKit] Keeping HAS_DRAFT_AGENT tag - ${remainingDraftCount} draft agents remaining`,
            );
          }
        } catch (error) {
          // Don't fail the agent activation if ConvertKit tracking fails
          console.error(
            '[ConvertKit] Error handling draft agent tag removal:',
            error,
          );
        }
      }

      //console.log(`Agent status changed from draft to active. Triggering feature generation for agent: ${agentId}`);
      await agentProfileAndFeatureGenerationHandler(
        agentId,
        updatedAgentData.model,
      );
    }
    if (updatedAgentData.status === 'active') {
      await updateCreatorBalance(userId);
    }

    return NextResponse.json({
      message: 'Agent updated successfully',
      agent: updatedAgent[0],
    });
  } catch (error) {
    console.error('Error updating agent:', error);
    return NextResponse.json(
      { error: 'Failed to update agent' },
      { status: 500 },
    );
  }
}
