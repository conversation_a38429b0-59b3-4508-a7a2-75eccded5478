import { db } from '@/lib/db';
import { creatorSubscriptions, creatorCancelationRequests, user } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { markAgentsAsInactive } from '@/lib/db/subscription-utils';
import { trackSubscriptionCancelled } from '@/lib/user-journey';

export async function handleDeletedSubscriptionForCreator(
  userId: string,
) {
  // Find the active subscription for this user
  console.log("Finding active subscription for user:", userId);
  const [existingSubscription] = await db
    .select()
    .from(creatorSubscriptions)
    .where(
      and(
        eq(creatorSubscriptions.userId, userId),
        eq(creatorSubscriptions.status, 'active')
      )
    );

  if (!existingSubscription) {
    console.log('No active subscription found to cancel');
    return;
  }

  try {
    // Update the subscription status to cancelled
    console.log('Updating subscription status to inactive...');
    const [updatedSubscription] = await db
      .update(creatorSubscriptions)
      .set({ 
        status: 'inactive',
        updated_at: new Date()
      })
      .where(eq(creatorSubscriptions.id, existingSubscription.id))
      .returning();
    console.log('Successfully updated status of subscription:', updatedSubscription.id);

    // Update the cancellation request status if it exists
    console.log('Checking for cancellation request...');
    const [existingRequest] = await db
      .select()
      .from(creatorCancelationRequests)
      .where(eq(creatorCancelationRequests.subscriptionId, existingSubscription.id));

    if (existingRequest) {
      console.log('Updating cancellation request status...');
      const [updatedRequest] = await db
        .update(creatorCancelationRequests)
        .set({
          status: 'canceled',
          updatedAt: new Date()
        })
        .where(eq(creatorCancelationRequests.subscriptionId, existingSubscription.id))
        .returning();
      console.log('Successfully updated cancellation request:', updatedRequest.id);
    } else {
      console.log('No cancellation request found for this subscription');
    }
      // Mark all active agents as inactive
      console.log('Marking active agents as inactive...');
      const updatedAgents = await markAgentsAsInactive(userId);
      console.log(`Successfully marked ${updatedAgents.length} agents as inactive`);
  } catch (error) {
    console.error('Error updating subscription or cancellation request:', error);
    throw error;
  }

  console.log('Subscription cancelled:', {
    userId,
    subscriptionId: existingSubscription.id,
  });
  
  // Track the cancellation in ConvertKit
  try {
    // Get user email for ConvertKit tracking
    const userRecord = await db
      .select({ email: user.email })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1)
      .then(rows => rows[0]);
      
    if (userRecord?.email) {
      await trackSubscriptionCancelled(userRecord.email);
      console.log(`Added Cancelled tag in ConvertKit for user: ${userRecord.email}`);
    }
  } catch (error) {
    console.error('Error updating ConvertKit cancellation tag:', error);
    // Don't throw here - we don't want to fail the subscription cancellation if ConvertKit fails
  }
}