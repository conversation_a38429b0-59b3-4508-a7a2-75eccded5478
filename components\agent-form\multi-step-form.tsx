'use client';

import React, { useState, useC<PERSON>back, use<PERSON><PERSON><PERSON>, <PERSON>, isValidElement, cloneElement } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ChevronLeft, Check } from "lucide-react";
import Image from "next/image";

// Mobile horizontal stepper component
const MobileStepIndicator = ({
  currentStep,
  steps,
}: {
  currentStep: number;
  steps: {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
  }[];
}) => {
  return (
    <div className="w-full py-2 overflow-x-auto">
      <div className="flex items-start justify-between min-w-max px-4">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            {/* Step circle and label */}
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  'flex items-center justify-center h-8 w-8 rounded-full border transition-all duration-300',
                  index === currentStep
                    ? 'bg-primary border-primary text-primary-foreground ring-2 ring-primary/20'
                    : index < currentStep
                      ? 'bg-orange-600 border-orange-600 text-white'
                      : 'bg-slate-200 dark:bg-slate-700 border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-200',
                )}
              >
                {index < currentStep ? (
                  <Check className="size-4 text-white" />
                ) : index === currentStep ? (
                  <div className="flex items-center justify-center w-full h-full">{step.icon}</div>
                ) : (
                  <span className="text-xs font-semibold">{index + 1}</span>
                )}
              </div>
              <span className="text-sm mt-1 whitespace-nowrap max-w-16 truncate dark:text-slate-200">
                {step.title}
              </span>
            </div>
            
            {/* Connecting line - aligned with circle center */}
            {index < steps.length - 1 && (
              <div className="flex-1 flex items-center px-2 h-8 mt-0">
                <div
                  className={cn(
                    'h-px w-full',
                    index < currentStep
                      ? 'bg-orange-500'
                      : 'bg-slate-300 dark:bg-slate-600',
                  )}
                />
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

// Desktop vertical step indicator component
const StepIndicator = ({
  currentStep,
  steps,
}: {
  currentStep: number;
  steps: {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
  }[];
}) => {
  return (
    <div className="w-full max-w-xs">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-start gap-3 relative">
          {/* Circle and connecting line container */}
          <div className="flex flex-col items-center shrink-0">
            <div
              className={cn(
                'flex items-center justify-center h-7 w-7 rounded-full border transition-all duration-300 relative z-10',
                index === currentStep
                  ? 'bg-primary border-primary text-primary-foreground ring-1 ring-primary/20'
                  : index < currentStep
                    ? 'bg-orange-600 border-orange-600 text-white'
                    : 'bg-slate-200 dark:bg-slate-700 border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-200',
              )}
            >
              {index < currentStep ? (
                <Check className="h-3.5 w-3.5 text-white" />
              ) : index === currentStep ? (
                <div className="flex items-center justify-center w-full h-full">{step.icon}</div>
              ) : (
                <span className="text-xs font-semibold">{index + 1}</span>
              )}
            </div>
            {/* Connecting line - only show if not the last step */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  'w-px h-8 mt-2 mb-2',
                  index < currentStep
                    ? 'bg-orange-500'
                    : 'bg-slate-300 dark:bg-slate-600',
                )}
              />
            )}
          </div>
          
          {/* Text content */}
          <div className="flex flex-col pt-1 min-w-0 pb-6">
            <span
              className={cn(
                'text-sm font-medium transition-colors duration-200',
                index === currentStep
                  ? 'text-foreground dark:text-white font-semibold'
                  : index < currentStep
                    ? 'text-foreground dark:text-slate-200'
                    : 'text-muted-foreground dark:text-slate-300',
              )}
            >
              {step.title}
            </span>
            <span
              className={cn(
                'text-xs transition-colors duration-200 mt-1',
                index === currentStep
                  ? 'text-muted-foreground'
                  : 'text-muted-foreground/70',
              )}
            >
              {step.description}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

interface MultiStepFormProps {
  children: React.ReactNode[];
  steps: {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
  }[];
  onComplete: () => void;
  onSaveStep?: () => Promise<any>;
  isSubmitting?: boolean;
  isSavingDraft?: boolean;
  submitButtonText?: string;
  showBackToLogin?: boolean;
  logoPreview?: string | null;
  isEditMode?: boolean;
  draftAgentId?: string | null;
  // New loading states
  isUploadingLogo?: boolean;
  isCheckingSlug?: boolean;
  isUploadingFiles?: boolean;
  isImprovingInstruction?: boolean;
  // Plan limits and handlers
  planLimits?: {
    canCreate: boolean;
    message: string;
    agentsLeft: number;
    totalAgents: number;
    currentAgentCount: number;
    plan?: any;
  } | null;
  onStartFreeTrial?: () => void;
  onUpgradeToEnterprise?: () => void;
  onUpgradeToPro?: () => void;
  onImprovingInstructionChange?: (isImproving: boolean) => void;
  onUploadingLogoChange?: (isUploading: boolean) => void;
}

export function MultiStepForm({
  children,
  steps,
  onComplete,
  onSaveStep,
  isSubmitting = false,
  isSavingDraft = false,
  submitButtonText = "Complete",
  showBackToLogin = false,
  logoPreview = null,
  isEditMode = false,
  draftAgentId = null,
  // New loading states
  isUploadingLogo = false,
  isCheckingSlug = false,
  isUploadingFiles = false,
  isImprovingInstruction = false,
  // Plan limits and handlers
  planLimits = null,
  onImprovingInstructionChange,
  onUploadingLogoChange,
}: MultiStepFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isValid, setIsValid] = useState<boolean[]>(Array(children.length).fill(false));
  const [isNavigating, setIsNavigating] = useState(false);

  // Memoize updateValidity to prevent infinite re-renders
  const updateValidity = useCallback((index: number) => {
    return useCallback((valid: boolean) => {
      setIsValid(prev => {
        const newValid = [...prev];
        if (newValid[index] !== valid) {
          newValid[index] = valid;
          return newValid;
        }
        return prev; // Return previous state if no change to prevent unnecessary re-renders
      });
    }, [index]);
  }, []);

  const goToNextStep = async () => {
    if (currentStep < children.length - 1) {
      // Save draft before proceeding to any next step
      if (onSaveStep) {
        setIsNavigating(true);
        try {
          await onSaveStep();
        } catch (error) {
          console.error('Error saving draft before navigation:', error);
          // Continue navigation even if save fails
        } finally {
          setIsNavigating(false);
        }
      }
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    } else {
      onComplete();
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const isCurrentStepValid = isValid[currentStep];
  const isLastStep = currentStep === children.length - 1;

  // Check if any loading states are active
  const isAnyLoading = isUploadingLogo || isCheckingSlug || isUploadingFiles || isImprovingInstruction;

  // Determine submit button state and behavior for last step
  const getSubmitButtonConfig = () => {
    if (!isLastStep) return null;

    // Always use the parent's submitButtonText and onComplete handler
    // The parent component handles all the logic for plan limits and button text
    return {
      text: submitButtonText,
      handler: onComplete,
      disabled: !isCurrentStepValid || isSubmitting || isSavingDraft || isNavigating || isAnyLoading
    };
  };

  const submitConfig = getSubmitButtonConfig();

  const handleSubmitClick = () => {
    if (submitConfig?.handler) {
      submitConfig.handler();
    }
  };

  return (
    <div className="flex flex-col lg:flex-row w-full mx-auto min-h-screen lg:min-h-full lg:h-full max-w-full overflow-hidden safari-input-fix">
      {/* Mobile header - only visible on mobile */}
      <div className="lg:hidden w-full p-3 sm:p-4 border-b border-border bg-card dark:bg-card sticky top-0 z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {logoPreview ? (
              <div className="h-8 w-8 rounded-full overflow-hidden relative flex-shrink-0">
                <Image
                  src={logoPreview}
                  alt="Agent Logo"
                  fill
                  className="object-cover"
                />
              </div>
            ) : null}
            <div className="flex flex-col">
              <span className="text-sm font-semibold tracking-tight">
                {isEditMode ? 'Edit Agent' : 'New Agent Creation'}
              </span>
              <span className="text-xs sm:text-sm text-muted-foreground dark:text-slate-400 transition-colors duration-200">
                {isSavingDraft ? 'Saving...' : `${currentStep + 1} of ${steps.length}`}
              </span>
            </div>
          </div>
        </div>

        <MobileStepIndicator currentStep={currentStep} steps={steps} />
      </div>

      {/* Desktop sidebar with steps - hidden on mobile */}
      <div className="hidden lg:flex lg:w-72 p-4 flex-col h-full border-r border-border overflow-y-auto bg-card dark:bg-card self-stretch">
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-1">
            {logoPreview ? (
              <div className="h-6 w-6 rounded-full overflow-hidden relative flex-shrink-0">
                <Image
                  src={logoPreview}
                  alt="Agent Logo"
                  fill
                  className="object-cover"
                />
              </div>
            ) : null}
            <h1 className="text-sm font-semibold tracking-tight dark:text-white">
              {isEditMode ? 'Edit Agent' : 'New Agent Creation'}
            </h1>
          </div>
          <p className="text-sm text-muted-foreground dark:text-slate-300 leading-relaxed">
            {isEditMode
              ? "Update your agent's settings"
              : 'Create your custom AI agent in just a few steps'}
          </p>
        </div>
        
        <div className="grow overflow-y-auto">
          <StepIndicator currentStep={currentStep} steps={steps} />
        </div>
      </div>

      {/* Right content area - Optimized for mobile scrolling */}
      <div className="flex-1 flex flex-col min-h-0 lg:h-full self-stretch">
        {/* Form content - Improved mobile scrolling */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden">
          <div className="p-3 sm:p-4 lg:p-6 pb-32 sm:pb-6 m-2 lg:m-0">
            <div className="max-w-3xl mx-auto lg:mx-0 w-full overflow-x-hidden">
              {/* Content container with proper spacing */}
              <div className="space-y-4 sm:space-y-6">
                {Children.map(children, (child, index) => {
                  if (index === currentStep && isValidElement(child)) {
                    return cloneElement(child as React.ReactElement<any>, {
                      updateValidity: updateValidity(index),
                      draftAgentId: draftAgentId,
                      onSaveDraft: onSaveStep,
                      isEditMode: isEditMode, // Pass through isEditMode prop
                      planLimits: planLimits, // Pass through planLimits prop
                      onImprovingStateChange: (isImproving: boolean) => {
                        // For the Configuration step (index 2), notify parent about instruction improvement state
                        if (index === 2 && onImprovingInstructionChange) {
                          onImprovingInstructionChange(isImproving);
                        }
                      },
                      onUploadingLogoChange: (isUploading: boolean) => {
                        // For the BasicInfo step (index 0), notify parent about logo uploading state
                        if (index === 0 && onUploadingLogoChange) {
                          onUploadingLogoChange(isUploading);
                        }
                      },
                    });
                  }
                  return null;
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation buttons - Fixed at viewport bottom for mobile only, static for tablet/desktop */}
        <div className="fixed bottom-0 left-0 right-0 sm:static flex flex-col sm:flex-row gap-2 sm:gap-0 sm:justify-between sm:items-center py-3 px-3 sm:px-4 lg:px-6 bg-card dark:bg-card border-t border-border shadow-lg sm:shadow-sm z-20">
          <Button
            type="button"
            variant="outline"
            onClick={goToPreviousStep}
            disabled={currentStep === 0}
            className="order-1 sm:order-none w-full sm:w-auto h-10 sm:h-9 px-3 text-sm font-medium transition-all"
          >
            <ChevronLeft className="mr-1 h-3.5 w-3.5" />
            Previous
          </Button>

          <div className="hidden sm:block flex-1 mx-0 sm:mx-4 order-0 sm:order-none" />

          <Button
            type="button"
            onClick={goToNextStep}
            disabled={!isCurrentStepValid || (isLastStep && isSubmitting) || (isSavingDraft || isNavigating) || isAnyLoading}
            className="order-0 sm:order-none w-full sm:w-auto h-10 sm:h-9 px-3 text-sm font-medium shadow-sm transition-all"
          >
            {isSubmitting && isLastStep ? (
              <>
                <div className="h-3.5 w-3.5 mr-1.5 border-t-2 border-b-2 border-current rounded-full animate-spin" />
                <span className="truncate">Creating agent...</span>
              </>
            ) : isSavingDraft ? (
              <>
                <div className="size-4 mr-2 border-y-2 border-current rounded-full animate-spin" />
                <span className="truncate">Saving...</span>
              </>
            ) : isAnyLoading && !isLastStep ? (
              <>
                <div className="size-4 mr-2 border-y-2 border-current rounded-full animate-spin" />
                <span className="truncate">
                  {isUploadingLogo ? 'Uploading logo...' :
                   isCheckingSlug ? 'Checking availability...' :
                   isUploadingFiles ? 'Uploading files...' :
                   isImprovingInstruction ? 'Improving instruction...' :
                   'Loading...'}
                </span>
              </>
            ) : isLastStep ? (
              <>
                {submitButtonText}
                <Check className="ml-1 h-3.5 w-3.5" />
              </>
            ) : currentStep === 0 ? (
              <>
                Continue<span className="ml-1">→</span>
              </>
            ) : (
              <>
                Continue<span className="ml-1">→</span>
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
