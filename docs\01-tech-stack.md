# Tech Stack Overview

## Core Technologies

### Frontend
- **Next.js 15** - React framework for server-side rendering and static site generation
- **React 19** - UI library for building component-based interfaces
- **TypeScript** - Typed JavaScript for improved developer experience and code quality
- **Tailwind CSS** - Utility-first CSS framework for styling

### Backend
- **Next.js API Routes** - Serverless functions for backend API endpoints
- **Drizzle ORM** - TypeScript ORM for database interactions
- **PostgreSQL** - Relational database for data storage
- **Upstash Vector** - Vector database for embeddings and similarity search

### Authentication & Security
- **NextAuth.js 5** - Authentication library for Next.js applications
- **bcrypt** - Password hashing for secure authentication
- **JWT (JSON Web Tokens)** - For secure session management

### Payment Processing
- **Stripe** - Payment processing platform for subscriptions and one-time payments
- **Custom payment tracking** - For managing creator payouts and user subscriptions

### AI & Machine Learning
- **AI SDK** - Integration with various AI providers
- **Anthropic Claude** - AI model integration
- **OpenAI** - AI model integration
- **Vector embeddings** - For knowledge base search and retrieval

### File Storage
- **AWS S3** - Cloud storage for file uploads (knowledge base documents, agent logos)
- **Vercel Blob** - For certain file storage needs

### Deployment & Infrastructure
- **Vercel** - Hosting platform for the application
- **GitHub** - Version control and collaboration

## Package Dependencies

### Key Frontend Dependencies
- **@radix-ui** components - Accessible UI components
- **shadcn/ui** - Component library built on Radix UI
- **Lucide React** - Icon library
- **react-hook-form** - Form handling
- **zod** - Schema validation
- **sonner** - Toast notifications
- **framer-motion** - Animation library
- **TailwindCSS** - Utility CSS framework

### Key Backend Dependencies
- **drizzle-orm** - Database ORM
- **@vercel/postgres** - PostgreSQL client
- **@upstash/vector** - Vector database client
- **stripe** - Payment processing
- **bcrypt-ts** - Password hashing

### File Processing
- **mammoth** - Word document processing
- **pdf2json** - PDF processing
- **papaparse** - CSV processing

### Development Tools
- **TypeScript** - Type checking
- **Biome** - Linting and formatting
- **Playwright** - Testing framework
- **ESLint** - Code linting

## Development Workflow

The project uses pnpm as its package manager (version 9.12.3) and includes various scripts for development:

- `pnpm dev` - Start development server with Turbo
- `pnpm build` - Build the application
- `pnpm lint` - Lint the codebase
- `pnpm format` - Format code with Biome
- `pnpm db:*` - Various database management commands

## Database Management

Drizzle ORM is used for database operations with scripts for:
- Schema generation
- Migrations
- Database studio for visual exploration
- Seeding test data

## Testing

The project uses Playwright for end-to-end testing with the command:
- `pnpm test` - Run Playwright tests
