'use client';

import { Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

function NotFoundContent() {
  // Wrap the useSearchParams in a client component
  const searchParams = useSearchParams();
  const from = searchParams?.get('from') || '';
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 text-center">
      <h1 className="text-6xl font-bold text-gray-900 dark:text-white">404</h1>
      <h2 className="mt-4 text-xl font-medium text-gray-600 dark:text-gray-400">
        Page not found
      </h2>
      <p className="mt-2 text-gray-500 dark:text-gray-400 max-w-md">
        The page you are looking for doesn't exist or has been moved.
      </p>
      <div className="mt-8">
        <Button asChild variant="default">
          <Link href={from || '/'} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Go back
          </Link>
        </Button>
      </div>
    </div>
  );
}

export default function NotFound() {
  return (
    <Suspense fallback={
      <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 text-center">
        <h1 className="text-6xl font-bold text-gray-900 dark:text-white">404</h1>
        <h2 className="mt-4 text-xl font-medium text-gray-600 dark:text-gray-400">
          Page not found
        </h2>
        <div className="mt-8">
          <Button asChild variant="default">
            <Link href="/" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Go back home
            </Link>
          </Button>
        </div>
      </div>
    }>
      <NotFoundContent />
    </Suspense>
  );
}
