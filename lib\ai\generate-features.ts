// Using built-in fetch from Next.js
import { Agent } from '@/lib/db/schema';

/**
 * Generate features for an agent using AI based on its instruction and description
 * @param agent The agent data
 * @returns Array of features as strings
 */
export async function generateAgentFeatures(agent: Agent): Promise<string[]> {
  // If no instruction or description, return default features
  if (!agent.instruction && !agent.description) {
    console.log('No instruction or description provided, using default features');
    return ['AI Assistant', 'Text Generation', 'Conversational'];
  }

  // Prepare the prompt for the AI
  const prompt = `
  Based on the following agent information, generate 3-5 key features or capabilities that this agent has.
  Each feature should be a short phrase (6-8 words) that highlights a specific capability. 
  Keep the language personable and human. 
  See if you can use the agent name in the features.

  Agent Name: ${agent.agentName || 'AI Assistant'}
  Description: ${agent.description || ''}
  Instruction: ${agent.instruction || ''}


  Return ONLY the features as a JSON array of strings, nothing else.
  `;

  try {
    // Check if we have OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      console.warn('No OpenAI API key found, using default features');
      return ['AI Assistant', 'Text Generation', 'Conversational'];
    }

    // Call OpenAI API
    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates feature lists for AI agents. Return only JSON arrays.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 150
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`OpenAI API error: ${response.status} ${errorText}`);
        return ['AI Assistant', 'Text Generation', 'Conversational'];
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        console.error('No content in OpenAI API response');
        return ['AI Assistant', 'Text Generation', 'Conversational'];
      }
      
      // Clean the content - remove markdown code blocks if present
      let cleanedContent = content;
      if (content.includes('```')) {
        cleanedContent = content
          .replace(/```json\n/g, '')
          .replace(/```\n/g, '')
          .replace(/```/g, '')
          .trim();
      }
    
      // Parse the response - handle both JSON format and plain text list
      try {
        // Try to parse as JSON
        let features: string[] = [];
        try {
          features = JSON.parse(cleanedContent);
          
          // If features are strings with quotes, remove the quotes
          if (Array.isArray(features)) {
            features = features.map((feature: string) => {
              if (typeof feature === 'string') {
                return feature.replace(/^"(.*)"$/, '$1').trim();
              }
              return feature;
            });
          }
        } catch (jsonError) {
          // If not JSON, try to extract features from text
          const lines = cleanedContent.split('\n');
          features = lines
            .map((line: string) => line.trim())
            .filter((line: string) => line.startsWith('-') || line.startsWith('*'))
            .map((line: string) => line.substring(1).trim())
            .filter(Boolean);
            
          // If no bullet points found, try to extract comma-separated list
          if (features.length === 0) {
            features = cleanedContent.split(',').map((f: string) => f.trim()).filter(Boolean);
          }
        }
        
        if (Array.isArray(features) && features.length > 0) {
          return features.slice(0, 5); // Limit to 5 features
        } else {
          return ['AI Assistant', 'Text Generation', 'Conversational'];
        }
      } catch (e) {
        console.error('Error parsing OpenAI API response:', e);
        return ['AI Assistant', 'Text Generation', 'Conversational'];
      }
    } catch (error) {
      console.error('Error generating agent features:', error);
      return ['AI Assistant', 'Text Generation', 'Conversational'];
    }
  } catch (error) {
    console.error('Error in generateAgentFeatures:', error);
    return ['AI Assistant', 'Text Generation', 'Conversational'];
  }
}
