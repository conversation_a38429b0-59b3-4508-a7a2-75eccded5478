import { db } from "@/lib/db";
import { and, eq } from "drizzle-orm";
import { creatorSubscriptions, subscriptionBalance, agentProfiles } from "@/lib/db/schema";
import { getTotalKnowledgebaseSize, getTotalAgentsCreatedByUser } from "@/lib/db/agent-queries";

/**
 * Updates the subscription balance for a creator based on their current usage
 * @param userId - The ID of the user/creator
 */
export async function updateCreatorBalance(userId: string) {
  console.log('Updating creator balance for user:', userId);
  try {
    // Get active subscription for the user
    const [activeSubscription] = await db
      .select()
      .from(creatorSubscriptions)
      .where(
        and(
          eq(creatorSubscriptions.userId, userId),
          eq(creatorSubscriptions.status, 'active')
        )
      );

    if (!activeSubscription) {
      console.log('No active subscription found for user:', userId)
      return;
    }

    // Get current usage stats
    const [knowledgebaseSizeUsed, totalAgentsCreated] = await Promise.all([
      getTotalKnowledgebaseSize(userId),
      getTotalAgentsCreatedByUser(userId)
    ]);

    // Get subscription balance to get plan limits
    const [balance] = await db
      .select()
      .from(subscriptionBalance)
      .where(eq(subscriptionBalance.subscription_id, activeSubscription.id));

    if (!balance) {
      // console.log('No balance record found for subscription:', activeSubscription.id);
      return;
    }

    // Update the balance with current usage
    await db
      .update(subscriptionBalance)
      .set({
        agentsLeft: balance.total_agents - totalAgentsCreated,
        knowledgeBaseLeft: balance.total_knowledgebase - knowledgebaseSizeUsed,
        updated_at: new Date()
      })
      .where(eq(subscriptionBalance.subscription_id, activeSubscription.id));
  } catch (error) {
    // console.error('Error updating creator balance:', error);
  }
}

/**
 * Creates an agent profile and triggers feature generation
 * @param agentId - The ID of the agent
 * @param model - The model to use for the agent
 */
export async function agentProfileAndFeatureGenerationHandler(
  agentId: string,
  model: string
): Promise<void> {
  try {
    // Create a new profile with empty features
    await db.insert(agentProfiles).values({
      agentId,
      rating: 0,
      totalChats: 0,
      datasets: 0,
      model: model || 'gpt-4o',
      features: [], // Empty array for features
      pendingFeatureGeneration: true, // Mark as pending feature generation
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Use the absolute URL with the app URL from environment
    const apiUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/agents/generate-features`;

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ agentId }),
      });

      if (response.ok) {
        await response.json();
      } else {
        console.error(`Feature generation API error: ${response.status}`);
      }
    } catch (fetchError) {
      console.error('Error calling feature generation API:', fetchError);
    }
  } catch (profileError) {
    console.error('Error handling agent profile for feature generation:', profileError);
  }
}

export async function checkSubscriptionLimit(userId: string): Promise<boolean | { error: { message: string } }> {
 
  try {
    // Check if user has an active creator subscription
    const [activeSubscription] = await db
      .select()
      .from(creatorSubscriptions)
      .where(
        and(
          eq(creatorSubscriptions.userId, userId),
          eq(creatorSubscriptions.status, "active")
        )
      );

    if (activeSubscription) {
      // Check subscription balance for available agents
      const [balance] = await db
        .select()
        .from(subscriptionBalance)
        .where(
          and(
            eq(subscriptionBalance.subscription_id, activeSubscription.id),
          )
        );

      if (balance && balance.agentsLeft <= 0) {
        return false;
      }
      return true;
    }

    return false; // No active subscription means no limit available
  } catch (error) {
    return {
      error: {
        message: "Error checking subscription limit"
      }
    };
  }
}
