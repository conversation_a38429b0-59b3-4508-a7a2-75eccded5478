import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { sql } from '@vercel/postgres';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(request: Request) {
  console.log('🔄 Starting tokens update cron job...');
  try {
    // Verify the request is from a trusted source (e.g., Vercel Cron)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      console.log('❌ Authorization failed: Invalid cron secret');
      return new NextResponse('Unauthorized', { status: 401 });
    }
    console.log('✅ Authorization successful');

    console.log('📊 Calculating token consumption...');
    // First get the consumed tokens for each subscription with low balance
    const tokensConsumedQuery = `
      SELECT 
        cs.id as subscription_id,
        COALESCE(SUM(m.tokens), 0) as total_tokens_used
      FROM "CreatorSubscriptions" cs
      LEFT JOIN agents a ON a."userId" = cs."userId"
      LEFT JOIN "Chat" c ON c."agentId" = a.id
      LEFT JOIN "Message_v2" m ON m."chatId" = c.id
      WHERE cs.id IN (
        SELECT "subscription_id"
        FROM "CreatorBalance" cb
        WHERE (cb."tokensLeft"::float / cb."total_tokens"::float) < 0.1
      )
      GROUP BY cs.id
    `;

    // Update tokensLeft for each subscription
    const updateQuery = `
      UPDATE "CreatorBalance" cb
      SET "tokensLeft" = "total_tokens" - tc.total_tokens_used
      FROM (${tokensConsumedQuery}) tc
      WHERE cb."subscription_id" = tc.subscription_id
    `;

    console.log('💫 Executing update query...');
    const result = await sql.query(updateQuery);
    console.log('✅ Update complete:', result);

    return NextResponse.json({ 
      success: true, 
      message: 'Tokens updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error updating tokens:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({ 
      error: 'Failed to update tokens',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
