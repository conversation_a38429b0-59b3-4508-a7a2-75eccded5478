"use client";

import { redirect, usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardSidebar } from '@/components/dashboard-sidebar';
import { SidebarProvider, useSidebar } from '@/components/ui/sidebar';
import { PanelLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';

function CreateAgentContent({ children, showSidebar = true }: { children: React.ReactNode, showSidebar?: boolean }) {
  const { state, toggleSidebar } = useSidebar();
  const isCollapsed = state === 'collapsed';
  const [isMobile, setIsMobile] = useState(false);
  
  // Check if we're on a mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  
  return (
    <main 
      className="flex-1 overflow-auto transition-all duration-300 ease-in-out"
      style={{ 
        marginLeft: !showSidebar ? '0' : (isMobile ? '0' : (isCollapsed ? '3rem' : '16rem'))
      }}
    >
      {showSidebar && (isCollapsed || isMobile) && (
        <Button
          variant="outline"
          size="icon"
          className="fixed top-4 left-4 z-10 h-8 w-8"
          onClick={toggleSidebar}
        >
          <PanelLeft className="h-4 w-4" />
        </Button>
      )}
      {children}
    </main>
  );
}

export default function CreateAgentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const [isMobile, setIsMobile] = useState(false);
  const pathname = usePathname();
  
  // Check if we're in the new agent creation or edit flow
  const isNewAgentCreation = pathname === '/create/new' || pathname.includes('/create/new/edit');
  
  // Check if we're on a mobile device for initial sidebar state
  useEffect(() => {
    setIsMobile(window.innerWidth < 768);
  }, []);

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    redirect("/login");
  }

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
      </div>
    );
  }

  // For new agent creation or edit flow, don't show the sidebar
  if (isNewAgentCreation) {
    return (
      <div className="flex h-screen">
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    );
  }

  // For other create pages (like edit), show the sidebar
  return (
    <div className="flex h-screen">
      <SidebarProvider defaultOpen={!isMobile}>
        <DashboardSidebar user={session?.user} />
        <CreateAgentContent showSidebar={true}>
          {children}
        </CreateAgentContent>
      </SidebarProvider>
    </div>
  );
}
