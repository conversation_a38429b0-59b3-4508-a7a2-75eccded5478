"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Check, Copy, Share, ArrowRight, ExternalLink } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

export default function AgentSuccessPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [copied, setCopied] = useState(false);
  const [agentData, setAgentData] = useState<{
    id: string;
    name: string;
    slug: string;
    url: string;
  } | null>(null);

  // Set document title
  useEffect(() => {
    document.title = 'Success | BuildThatIdea';
  }, []);

  useEffect(() => {
    // Get agent data from localStorage
    const storedData = localStorage.getItem('createdAgentData');
    
    if (!storedData) {
      // If no data is found, redirect to home
      router.push("/");
      return;
    }
    
    try {
      // Parse the stored data
      const parsedData = JSON.parse(storedData);
      
      // Construct the agent URL
      const baseUrl = window.location.origin;
      const agentUrl = `${baseUrl}/agent/${parsedData.slug}`;
      
      // Set the agent data
      setAgentData({
        id: parsedData.id,
        name: parsedData.name,
        slug: parsedData.slug,
        url: agentUrl
      });
      
      // Don't clear the stored data immediately
      // This prevents the redirect on initial load
      // We'll clear it when the user navigates away
    } catch (error) {
      console.error("Error parsing agent data:", error);
      router.push("/");
    }
  }, [router]);

  // Add a cleanup function for when the user navigates to dashboard or agent page
  const clearStoredData = () => {
    // Wait a moment before clearing to ensure navigation completes
    setTimeout(() => {
      localStorage.removeItem('createdAgentData');
      // console.log("Cleared agent data from localStorage after navigation");
    }, 1000);
  };

  // Copy agent URL to clipboard
  const copyToClipboard = () => {
    if (!agentData) return;
    
    navigator.clipboard.writeText(agentData.url)
      .then(() => {
        setCopied(true);
        toast.success("URL copied to clipboard");
        
        // Reset copied state after 2 seconds
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((error) => {
        console.error("Error copying to clipboard:", error);
        toast.error("Failed to copy URL");
      });
  };

  // Share on Twitter/X
  const shareOnTwitter = () => {
    if (!agentData) return;
    
    const tweetText = encodeURIComponent(
      `Check out my new AI agent "${agentData.name}" that I just created! Try it out here: ${agentData.url}`
    );
    
    window.open(`https://x.com/intent/tweet?text=${tweetText}`, "_blank");
  };

  const renderContent = () => {
    if (!agentData) {
      return (
        <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
          <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
            <div className="w-full max-w-md px-4">
              <Card className="overflow-hidden border shadow-sm p-6">
                <div className="flex flex-col items-center justify-center space-y-4 py-8">
                  <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center animate-pulse">
                    <div className="h-6 w-6 bg-muted-foreground/20 rounded-full"></div>
                  </div>
                  <div className="space-y-2 text-center">
                    <h2 className="text-xl font-semibold">Loading agent details</h2>
                    <p className="text-sm text-muted-foreground">
                      Please wait while we fetch your agent information
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
          <div className="w-full max-w-md px-4">
            <Card className="overflow-hidden border shadow-sm">
              {/* Success Icon */}
              <div className="bg-primary/5 flex items-center justify-center py-8 border-b">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Check className="h-6 w-6 text-primary" />
                </div>
              </div>
              
              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Header */}
                <div className="space-y-1.5 text-center">
                  <h1 className="text-xl font-semibold tracking-tight">Agent Launched</h1>
                  <p className="text-sm text-muted-foreground">
                    Your agent is now live and ready to use
                  </p>
                </div>
                
                {/* Agent URL */}
                <div className="space-y-2">
                  <label className="text-sm font-medium block text-foreground">Agent URL</label>
                  <div className="flex items-center gap-2">
                    <Input 
                      value={agentData.url} 
                      readOnly 
                      className="h-9 text-sm font-mono text-ellipsis"
                    />
                    <Button 
                      variant="outline" 
                      size="icon" 
                      onClick={copyToClipboard}
                      className={cn(
                        "h-9 w-9 shrink-0 transition-all",
                        copied && "border-green-500 text-green-500"
                      )}
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                
                {/* Actions */}
                <div className="grid gap-3 pt-2">
                  <Button 
                    onClick={shareOnTwitter}
                    variant="outline"
                    className="text-sm h-9 px-4 w-full"
                  >
                    <Share className="mr-2 h-3.5 w-3.5" />
                    Share on X
                  </Button>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 pt-2">
                    <Button 
                      asChild
                      variant="default"
                      className="text-sm h-9 w-full"
                    >
                      <Link href="/dashboard" onClick={clearStoredData}>
                        Dashboard
                        <ArrowRight className="ml-2 h-3.5 w-3.5" />
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild
                      variant="secondary"
                      className="text-sm h-9 w-full"
                    >
                      <Link href={agentData.url} onClick={clearStoredData}>
                        View Agent
                        <ExternalLink className="ml-2 h-3.5 w-3.5" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    );
  };

  return renderContent();
}
