{"crons": [{"path": "/api/cron/subscription-expiry", "schedule": "0 0 * * *"}, {"path": "/api/cron/update-expired-creator-subscriptions", "schedule": "0 0 * * *"}, {"path": "/api/cron/update-tokens", "schedule": "0 * * * *"}, {"path": "/api/cron/update-tokens-with-fewer-left-in-balance", "schedule": "*/5 * * * *"}, {"path": "/api/cron/update-agent-profiles", "schedule": "0 0 * * *"}, {"path": "/api/cron/reset-yearly-subscription-tokens", "schedule": "0 0 * * *"}], "buildCommand": "pnpm run build:production", "functions": {"app/api/agents/process-embeddings/**/*.ts": {"memory": 3008, "maxDuration": 300}, "app/api/agents/initiate-embeddings/**/*.ts": {"memory": 1024, "maxDuration": 60}}, "headers": [{"source": "/api/subscription/webhook", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}]}], "rewrites": [{"source": "/api/subscription/webhook", "destination": "/api/subscription/webhook", "has": [{"type": "header", "key": "stripe-signature"}]}], "public": true}