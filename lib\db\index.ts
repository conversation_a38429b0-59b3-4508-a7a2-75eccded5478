import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Database connection string
const connectionString = process.env.POSTGRES_URL || 'postgres://postgres:postgres@localhost:5432/postgres';

// Global is used here to maintain a cached connection across hot reloads in development
declare global {
  var postgresClient: ReturnType<typeof postgres> | undefined;
}

// Create a postgres client with connection limits
const client = globalThis.postgresClient || 
  postgres(connectionString, { 
    max: 10, // Set maximum number of connections in the pool
    idle_timeout: 20, // Close idle connections after 20 seconds
    connect_timeout: 10, // Connection timeout after 10 seconds
    prepare: false, // Disable prepared statements for better compatibility with some hosting providers
  });

// Only assign to the global object in development to prevent connections from being shared across serverless function invocations
if (process.env.NODE_ENV === 'development') {
  globalThis.postgresClient = client;
}

// Create a drizzle instance
export const db = drizzle(client, { schema });

// For Next.js Edge Runtime compatibility
export const createEdgeClient = () => {
  return postgres(connectionString, { 
    max: 1, // Use minimal connections for edge functions
    prepare: false,
  });
};

export const createEdgeDb = () => {
  const edgeClient = createEdgeClient();
  return drizzle(edgeClient, { schema });
};
