"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Info } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Custom PayPal icon since it's not in lucide-react
function CustomPaypalIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M7 11.5l1.5-9H13c2.2 0 3.5 1.3 3.5 3.5 0 3.2-2 5-5.5 5H7z" />
      <path d="M3.5 21.5L5 13h6c2.2 0 3.5 1.3 3.5 3.5 0 3.2-2 5-5.5 5h-6z" />
    </svg>
  );
}

export default function PayoutsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [paypalEmail, setPaypalEmail] = useState("");
  const [savedEmail, setSavedEmail] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Set document title
  useEffect(() => {
    document.title = 'Payouts | BuildThatIdea';
  }, []);

  // If user is not authenticated, redirect to the verification page
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/verify");
    }
  }, [router, status]);

  // Fetch existing payout information
  useEffect(() => {
    const fetchPayoutInfo = async () => {
      if (status === "authenticated") {
        try {
          setIsLoading(true);
          const response = await fetch('/api/payouts/info');
          
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.payoutSettings) {
              const email = data.payoutSettings.paypalEmail || "";
              setPaypalEmail(email);
              setSavedEmail(email);
            }
          } else {
            const error = await response.json();
            console.error('Error response:', error);
            toast.error('Failed to load payout information');
          }
        } catch (error) {
          console.error('Error fetching payout info:', error);
          toast.error('Failed to load payout information');
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchPayoutInfo();
  }, [status, setIsLoading, setPaypalEmail, setSavedEmail]);

  const handleSavePayoutInfo = async () => {
    if (!paypalEmail) {
      toast.error('Please enter a valid PayPal email address');
      return;
    }

    setIsSaving(true);
    
    try {
      const response = await fetch('/api/payouts/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ paypalEmail })
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSavedEmail(paypalEmail);
          toast.success('Payout information saved successfully');
        } else {
          console.error('Error saving payout info:', data.error);
          toast.error(data.error || 'Failed to save payout information');
        }
      } else {
        const error = await response.json();
        console.error('Error response:', error);
        toast.error(error.error || 'Failed to save payout information');
      }
    } catch (error) {
      console.error('Error saving payout info:', error);
      toast.error('Failed to save payout information');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Payouts</h2>
      </div>
      
      {isLoading ? (
        <Card>
          <CardHeader>
            <Skeleton className="h-7 w-48 mb-2" />
            <Skeleton className="h-4 w-full max-w-md" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-32" />
          </CardFooter>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CustomPaypalIcon className="h-5 w-5 mr-2 text-blue-600" />
              PayPal Email
            </CardTitle>
            <CardDescription>
              Monthly payouts for earnings above $20
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="paypal-email">Email Address</Label>
                <Input
                  id="paypal-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={paypalEmail}
                  onChange={(e) => setPaypalEmail(e.target.value)}
                />
              </div>
              
              <Alert className="bg-amber-50 text-amber-800 border-amber-200">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Payouts: 15th of each month • Minimum: $20 • Method: PayPal
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between border-t pt-6">
            <div className="text-sm text-muted-foreground">
              {savedEmail && (
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                  <span>Current: {savedEmail}</span>
                </div>
              )}
            </div>
            <Button 
              onClick={handleSavePayoutInfo}
              disabled={isSaving || paypalEmail === savedEmail}
            >
              {isSaving ? (
                <>
                  <div className="h-4 w-4 border-t-2 border-b-2 border-current rounded-full animate-spin mr-2" />
                  <span>Saving...</span>
                </>
              ) : (
                'Save'
              )}
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}
