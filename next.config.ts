import { withSentryConfig } from '@sentry/nextjs';

/** @type {import('next').NextConfig} */
const nextConfig = {
  serverExternalPackages: ["pino", "pino-pretty"],
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  experimental: {
    // Enable PPR (Partial Prerendering)
    ppr: true,
    // Limit concurrent static generations to reduce database connections
    concurrentFeatures: false,
    // Increase timeout for static generation
    staticPageGenerationTimeout: 120,
  },
  images: {
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh',
      },
      {
        hostname: 'bti.s3.us-east-1.amazonaws.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'placehold.co',
        protocol: 'https',
        pathname: '/**',
      },
    ],
  },
  // Use standalone output mode for better compatibility with vercel
  output: 'standalone',
};

// Wrap with Sentry
const sentryWebpackPluginOptions = {
  // Additional options for the Sentry webpack plugin
  silent: process.env.NODE_ENV === 'production', // Suppress logs in production
  
  // Only create source maps in production
  disableServerWebpackPlugin: process.env.NODE_ENV !== 'production',
  disableClientWebpackPlugin: process.env.NODE_ENV !== 'production',
  
  // Configure source maps
  include: '.next',
  ignore: ['node_modules', 'webpack.config.js'],
  
  // Configure auth token if available (for private repositories)
  authToken: process.env.SENTRY_AUTH_TOKEN,
  
  // Configure org and project
  org: process.env.SENTRY_ORG || 'your-org-name',
  project: process.env.SENTRY_PROJECT || 'fin',
};

export default withSentryConfig(nextConfig, sentryWebpackPluginOptions);
