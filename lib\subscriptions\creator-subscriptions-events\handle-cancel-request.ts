import { db } from '@/lib/db';
import { creatorSubscriptions, creatorCancelationRequests, creatorPlans } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function handleCancelRequestLogForCreator(
  userId: string,
  productName: string,
  cancelAt: Date | null
) {
  try {
    // Find plan by product name
    const plan = await db
      .select()
      .from(creatorPlans)
      .where(eq(creatorPlans.name, productName))
      .limit(1)
      .then(rows => rows[0]);

    if (!plan) {
      throw new Error(`Plan not found for product: ${productName}`);
    }

    // Find the active subscription in our database
    const activeSubscription = await db
      .select()
      .from(creatorSubscriptions)
      .where(
        and(
          eq(creatorSubscriptions.userId, userId),
          eq(creatorSubscriptions.planId, plan.id),
          eq(creatorSubscriptions.status, 'active')
        )
      )
      .limit(1)
      .then(rows => rows[0]);

    if (activeSubscription) {
      await db.insert(creatorCancelationRequests).values({
        userId,
        subscriptionId: activeSubscription.id,
        status: 'scheduled',
        cancelAt: cancelAt || null
      });
      console.log("Subscription cancel request logged for user:", userId);
    } else {
      console.log('No active subscription found for user:', userId);
    }
  } catch (error) {
    console.error('Error handling cancellation request:', error);
    throw error;
  }
}
