'use client';

import { SidebarProvider } from './ui/sidebar';
import { Chat } from './chat';
import { DataStreamHandler } from './data-stream-handler';
import type { Agent } from '@/lib/db/schema';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';

interface AgentPageWrapperProps {
  agent: Agent;
  id: string;
}

export function AgentPageWrapper({ agent, id }: AgentPageWrapperProps) {
  return (
    <SidebarProvider>
      <div className="flex flex-col min-h-screen">
        <div className="p-4 border-b flex items-center gap-4">
          {agent.logo && (
            <img 
              src={agent.logo} 
              className="w-10 h-10 rounded-full" 
              alt={agent.agentName || 'Agent'} 
            />
          )}
          <div>
            <h1 className="text-xl font-bold">{agent.agentName}</h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">{agent.description}</p>
          </div>
        </div>

        <Chat
          key={id}
          id={id}
          initialMessages={[]}
          selectedChatModel={DEFAULT_CHAT_MODEL}
          selectedVisibilityType="private"
          isReadonly={false}
          agent={agent}
        />
        <DataStreamHandler id={id} />
      </div>
    </SidebarProvider>
  );
}
