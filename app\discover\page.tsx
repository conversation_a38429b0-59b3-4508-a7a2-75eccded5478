'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import Logo from '@/public/images/logo.png';
import { Menu, X } from 'lucide-react';
import { useSession } from 'next-auth/react';

interface Agent {
  id: string;
  slug: string;
  agentName: string;
  description: string;
  logo?: string;
  accessLevel?: 'free' | 'subscription' | 'lifetime';
  price?: number;
  status?: 'active' | 'inactive' | 'draft';
  visibility?: 'public' | 'private';
}

export default function DiscoverPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [showBlogDropdown, setShowBlogDropdown] = useState(false);
  const router = useRouter();
  const { data: session } = useSession();

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        const response = await fetch('/api/agents/discover');
        const data = await response.json();

        // Check if data is in the expected format
        if (Array.isArray(data)) {
          setAgents(data);
        } else if (data.agents && Array.isArray(data.agents)) {
          // Handle case where API returns {agents: [...]} structure
          setAgents(data.agents);
        } else {
          console.error('Unexpected API response format:', data);
          setAgents([]);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching agents:', error);
        setIsLoading(false);
        setAgents([]);
      }
    };

    fetchAgents();
  }, []);

  const handleAgentClick = (slug: string) => {
    router.push(`/agent/${slug}`);
  };

  if (isLoading) {
    return (
      <section className="bg-white dark:bg-zinc-950">
        {/* Brand Header - Same as the loaded state */}
        <header className="sticky top-0 z-50 w-full flex justify-center px-4 pt-4 mb-6">
          <div className="bg-white/90 dark:bg-zinc-900/90 backdrop-blur-sm rounded-full border border-[#E5E7EB]/80 dark:border-zinc-800/80 shadow-sm max-w-4xl w-full">
            <div className="flex items-center h-[56px] px-5">
              {/* Logo section */}
              <div className="flex-shrink-0">
                <Link href="/" className="flex items-center">
                  <Image
                    src={Logo}
                    alt="BuildThatIdea Logo"
                    width={75}
                    height={20}
                    priority={true}
                    className="w-auto h-[20px]"
                    unoptimized={true}
                  />
                </Link>
              </div>

              {/* Desktop Navigation - Centered */}
              <nav className="hidden md:flex items-center space-x-7 mx-6">
                <Link
                  href="https://buildthatidea.com/pricing"
                  className="text-sm font-medium text-[#344054] hover:text-black dark:text-gray-300 dark:hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Pricing
                </Link>
                <div className="relative">
                  <Link
                    href="https://buildthatidea.com/blog"
                    className="text-sm font-medium text-[#344054] hover:text-black dark:text-gray-300 dark:hover:text-white transition-colors relative flex items-center"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <span>Blog</span>
                  </Link>
                </div>
              </nav>

              {/* Right section with Login/Dashboard button */}
              <div className="flex items-center gap-3 ml-auto">
                {session ? (
                  <Link
                    href="/dashboard"
                    className="hidden md:inline-flex items-center justify-center h-9 px-4 text-sm font-medium rounded-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white"
                  >
                    Dashboard
                  </Link>
                ) : (
                  <Link
                    href="/login"
                    className="hidden md:inline-flex items-center justify-center h-9 px-4 text-sm font-medium rounded-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white"
                  >
                    Login
                  </Link>
                )}
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            {/* Skeleton for heading */}
            <div className="flex flex-col items-center mb-10">
              <div className="h-8 w-48 bg-gray-200 dark:bg-zinc-800 rounded-md animate-pulse mb-3" />
              <div className="h-4 w-64 bg-gray-200 dark:bg-zinc-800 rounded-md animate-pulse" />
            </div>

            {/* Skeleton grid for agents */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }, (_, i) => `skeleton-card-${i}`).map(
                (id) => (
                  <div
                    key={id}
                    className="bg-white dark:bg-zinc-900 rounded-xl shadow-sm overflow-hidden animate-pulse"
                  >
                    <div className="py-3 px-4 flex items-center gap-3">
                      {/* Icon skeleton */}
                      <div className="w-12 h-12 rounded-xl bg-gray-200 dark:bg-zinc-800 flex-shrink-0" />

                      {/* Text content and button skeleton */}
                      <div className="flex-grow min-w-0">
                        <div className="flex items-center justify-between gap-2">
                          <div className="flex-grow space-y-2">
                            <div className="h-4 bg-gray-200 dark:bg-zinc-800 rounded w-1/3" />
                            <div className="h-3 bg-gray-200 dark:bg-zinc-800 rounded w-2/3" />
                          </div>
                          <div className="w-[72px] h-6 bg-gray-200 dark:bg-zinc-800 rounded-full" />
                        </div>
                      </div>
                    </div>
                  </div>
                ),
              )}
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Function to truncate description to a specific length
  const truncateDescription = (description: string, maxLength = 60) => {
    if (description.length <= maxLength) return description;
    return `${description.substring(0, maxLength)}...`;
  };

  return (
    <section className="bg-white dark:bg-zinc-950">
      {/* Brand Header */}
      <header className="sticky top-0 z-50 w-full flex justify-center px-4 pt-4 mb-6">
        <div className="bg-white/90 dark:bg-zinc-900/90 backdrop-blur-sm rounded-full border border-[#E5E7EB]/80 dark:border-zinc-800/80 shadow-sm max-w-4xl w-full">
          <div className="flex items-center h-[56px] px-5">
            {/* Logo section */}
            <div className="flex-shrink-0">
              <Link href="/" className="flex items-center">
                <Image
                  src={Logo}
                  alt="BuildThatIdea Logo"
                  width={75}
                  height={20}
                  priority={true}
                  className="w-auto h-[20px]"
                  unoptimized={true}
                />
              </Link>
            </div>

            {/* Desktop Navigation - Centered */}
            <nav className="hidden md:flex items-center space-x-7 mx-6">
              <Link
                href="https://buildthatidea.com/pricing"
                className="text-sm font-medium text-[#344054] hover:text-black dark:text-gray-300 dark:hover:text-white transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                Pricing
              </Link>
              <div className="relative">
                <Link
                  href="https://buildthatidea.com/blog"
                  className="text-sm font-medium text-[#344054] hover:text-black dark:text-gray-300 dark:hover:text-white transition-colors relative flex items-center"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span>Blog</span>
                </Link>

                {/* Blog Dropdown */}
                <div
                  className={`absolute left-1/2 transform -translate-x-1/2 mt-2 w-64 bg-white dark:bg-zinc-900 rounded-xl border border-[#E5E7EB] dark:border-zinc-800 shadow-md overflow-hidden z-50 origin-top transition-all duration-150 ease-out ${
                    showBlogDropdown
                      ? 'opacity-100 scale-100'
                      : 'opacity-0 scale-95 pointer-events-none'
                  }`}
                >
                  <div className="p-3 border-b border-gray-100 dark:border-zinc-800">
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      Latest Articles
                    </h3>
                  </div>
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    <p>Coming soon</p>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-zinc-800 border-t border-gray-100 dark:border-zinc-700">
                    <Link
                      href="https://buildthatidea.com/blog"
                      className="text-sm text-orange-500 font-medium block text-center"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      View All Articles
                    </Link>
                  </div>
                </div>
              </div>
            </nav>

            {/* Right section with Login/Dashboard button */}
            <div className="flex items-center gap-3 ml-auto">
              {session ? (
                <Link
                  href="/dashboard"
                  className="hidden md:inline-flex items-center justify-center h-9 px-4 text-sm font-medium rounded-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  href="/login"
                  className="hidden md:inline-flex items-center justify-center h-9 px-4 text-sm font-medium rounded-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white"
                >
                  Login
                </Link>
              )}

              {/* Mobile Menu Button */}
              <button
                type="button"
                className="md:hidden p-1"
                onClick={() => setIsOpen(!isOpen)}
                aria-label="Toggle menu"
              >
                {isOpen ? (
                  <X className="h-5 w-5 text-gray-600" />
                ) : (
                  <Menu className="h-5 w-5 text-gray-600" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`mobile-menu fixed inset-x-0 top-[76px] z-40 transition-all duration-200 ease-in-out ${
            isOpen
              ? 'opacity-100 translate-y-0 pointer-events-auto'
              : 'opacity-0 -translate-y-4 pointer-events-none'
          }`}
        >
          <div className="bg-white dark:bg-zinc-900 mx-4 rounded-xl border border-gray-200 dark:border-zinc-800 shadow-lg overflow-hidden">
            <nav className="flex flex-col py-2">
              <div className="px-4 py-3 border-b border-gray-100 dark:border-zinc-800">
                <div className="flex flex-col space-y-1">
                  <Link
                    href="https://buildthatidea.com/pricing"
                    className="flex items-center px-4 py-3 text-sm font-medium text-[#344054] dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Pricing
                  </Link>
                  <Link
                    href="https://buildthatidea.com/blog"
                    className="flex items-center px-4 py-3 text-sm font-medium text-[#344054] dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Blog
                  </Link>
                  {session ? (
                    <Link
                      href="/dashboard"
                      className="flex items-center px-4 py-3 text-sm font-medium text-[#344054] dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
                      onClick={() => setIsOpen(false)}
                    >
                      Dashboard
                    </Link>
                  ) : (
                    <Link
                      href="/login"
                      className="flex items-center px-4 py-3 text-sm font-medium text-orange-500 hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
                      onClick={() => setIsOpen(false)}
                    >
                      Login
                    </Link>
                  )}
                </div>
              </div>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-semibold mb-4 text-center">
            Discover{' '}
            <span className="bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text text-transparent">
              Agents
            </span>
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto text-center mb-10">
            Explore AI agents created by other users and enhance your
            productivity.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {agents.map((agent) => (
              <div
                key={agent.id}
                className="bg-white dark:bg-zinc-900 rounded-xl shadow-sm hover:shadow transition-all duration-300 overflow-hidden border border-gray-100 dark:border-zinc-800 group p-5"
              >
                <div className="flex items-center justify-between gap-4">
                  {/* Left side: Icon and content */}
                  <div className="flex items-start gap-4 flex-grow">
                    {/* Icon */}
                    <div className="w-14 h-14 rounded-lg overflow-hidden flex-shrink-0">
                      {agent.logo ? (
                        <Image
                          src={agent.logo}
                          alt={agent.agentName}
                          width={56}
                          height={56}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-14 h-14 rounded-lg bg-orange-500 flex items-center justify-center text-white text-lg font-bold">
                          {agent.agentName[0]}
                        </div>
                      )}
                    </div>

                    {/* Content area with title and description */}
                    <div className="min-w-0 flex flex-col">
                      <h3 className="font-semibold text-base text-orange-500 dark:text-orange-400 truncate">
                        {agent.agentName}
                      </h3>

                      {/* Description below title */}
                      <p className="text-xs text-gray-700 dark:text-gray-400 mt-0.5 line-clamp-2">
                        {agent.description}
                      </p>
                    </div>
                  </div>

                  {/* Right side: Action Button or Price */}
                  <div className="flex-shrink-0 ml-4">
                    <Link
                      href={`/agent/${agent.slug}`}
                      onClick={(e) => {
                        e.preventDefault();
                        handleAgentClick(agent.slug);
                      }}
                      className="block"
                    >
                      <div className="flex flex-col items-center">
                        <button
                          type="button"
                          className="px-6 py-1.5 text-sm font-bold text-black dark:text-white bg-gray-100 dark:bg-zinc-800 hover:bg-orange-500 hover:text-white dark:hover:bg-orange-500 rounded-full transition-all duration-200 text-center whitespace-nowrap"
                        >
                          Chat
                        </button>
                        {agent.accessLevel !== 'free' && agent.price ? (
                          <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            ${agent.price}
                          </span>
                        ) : null}
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {agents.length === 0 && (
            <div className="text-center py-12">
              <p className="text-[15px] text-gray-600 dark:text-gray-400">
                No agents available at the moment.
              </p>
              <p className="mt-2 text-[13px] text-gray-500 dark:text-gray-500">
                Check back soon for new AI agents!
              </p>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
