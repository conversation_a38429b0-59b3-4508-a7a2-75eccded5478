import fs from 'fs';
import path from 'path';

// Define types for the agent data structures
export interface RawAgent {
  id?: string;
  slug?: string;
  email?: string;
  name?: string;
  instruction?: string;
  description?: string;
  status?: string;
  visibility?: {
    type?: string;
    public?: boolean;
  };
  access_level?: 'free' | 'subscription' | 'lifetime';
  price?: number | string;
  logo?: {
    url?: string;
  } | string;
  quick_messages?: string[];
  quickMessages?: string[];
  files?: any[];
}

export interface ConvertedAgent {
  slug: string;
  email: string;
  agentName: string;
  instruction: string;
  description: string;
  status: string;
  visibility: 'public' | 'private';
  access_level: 'free' | 'subscription' | 'lifetime';
  price?: number;
  logo: string;
  quickMessages: string[];
  files: any[];
}

/**
 * This script converts the agents-seed.json file to a format compatible with our database schema
 */
async function convertAgents() {
  try {
    // Read the agents data from the JSON file
    const agentsDataPath = path.join(process.cwd(), 'agents-seed.json');
    const rawAgentsData: RawAgent[] = JSON.parse(fs.readFileSync(agentsDataPath, 'utf8'));
    
    console.log(`Found ${rawAgentsData.length} agents to convert`);

    // Convert the agents to the format expected by our schema
    const convertedAgents: ConvertedAgent[] = rawAgentsData.map((agent: RawAgent) => {
      // Generate a slug from the agent name if not present
      let slug = agent.slug || '';
      if (!slug) {
        if (agent.name) {
          slug = agent.name.toLowerCase().replace(/\s+/g, '-');
        } else {
          // Generate a random slug
          slug = Math.random().toString(36).substring(7);
        }
      }
      
      // Convert visibility format
      let visibility: 'public' | 'private' = 'private';
      if (agent.visibility) {
        if (agent.visibility.public || agent.visibility.type === 'public') {
          visibility = 'public';
        }
      }

      // Convert access level and price
      const access_level = agent.access_level || 'free';
      let price = null;
      
      if (agent.access_level === 'subscription' || agent.access_level === 'lifetime') {
        price = typeof agent.price === 'string' ? 
               parseFloat(agent.price) : 
               (typeof agent.price === 'number' ? agent.price : 0);
      }

      // Convert quick messages format
      let quickMessages: string[] = [];
      if (agent.quick_messages) {
        if (typeof agent.quick_messages[0] === 'string') {
          quickMessages = agent.quick_messages as string[];
        } else {
          // Handle array of objects format for quick messages
          const objectMessages = agent.quick_messages as Array<{action?: string; title?: string}>;
          quickMessages = objectMessages.map(qm => qm.action || qm.title || '');
        }
      } else if (agent.quickMessages) {
        quickMessages = agent.quickMessages;
      }

      return {
        slug,
        email: agent.email || '',
        agentName: agent.name || slug,
        instruction: agent.instruction || '',
        description: agent.description || '',
        status: agent.status === 'ready' ? 'active' : (agent.status || 'active'),
        visibility,
        access_level,
        price: price !== null ? price : undefined,
        logo: (typeof agent.logo === 'string' ? agent.logo : agent.logo?.url) || `https://placehold.co/400x400/2563eb/ffffff?text=${slug.charAt(0).toUpperCase()}`,
        quickMessages: agent.quick_messages || [],
        files: agent.files || []
      };
    });

    // Write the converted agents to a new file
    const outputPath = path.join(process.cwd(), 'converted-agents.json');
    fs.writeFileSync(outputPath, JSON.stringify(convertedAgents, null, 2));
    
    console.log(`Successfully converted ${convertedAgents.length} agents to ${outputPath}`);
    return true;
  } catch (error) {
    console.error('Error converting agents:', error);
    return false;
  }
}

// Run the conversion
convertAgents()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
