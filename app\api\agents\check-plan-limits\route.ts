import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/db';
import { agents, subscriptionBalance, creatorSubscriptions, creatorPlans } from '@/lib/db/schema';
import { eq, and, gte, sql } from 'drizzle-orm';

/**
 * Check if a user is within their plan limits for creating a new agent
 * Returns:
 * - canCreate: boolean - Whether the user can create another agent
 * - message: string - Message explaining why they can't create another agent (if applicable)
 * - agentsLeft: number - Number of agents left in their plan
 * - totalAgents: number - Total number of agents allowed in their plan
 * - currentAgentCount: number - Number of agents they've already created
 * - plan: object - Plan details
 */
export async function GET() {
  try {
    console.log('Checking plan limits - start');
    const session = await auth();
    
    if (!session || !session.user) {
      console.log('No session or user found');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id as string;
    const userEmail = session.user.email as string;
    
    console.log('Checking plan limits for user:', { userId, userEmail });
    
    // 1. Find the user's active subscription
    const activeSubscription = (await db
      .select()
      .from(creatorSubscriptions)
      .where(
        and(
          eq(creatorSubscriptions.userId, userId),
          eq(creatorSubscriptions.status, 'active')
        )
      )
      .limit(1))[0];
    
    console.log('Active subscription query result:', activeSubscription);
    
    // If no active subscription, check if they're eligible for a trial
    if (!activeSubscription) {
      console.log('No active subscription found for user');
      // Count their existing agents
      const agentCount = await db
        .select({ count: sql`count(*)` })
        .from(agents)
        .where(
          and(
            eq(agents.userId, userId),
            // Only count active agents, not drafts
            eq(agents.status, 'active')
          )
        )
        .then(result => Number(result[0]?.count || 0));
      
      // Check if they've had a trial before
      const hasHadTrial = await db
        .select()
        .from(creatorSubscriptions)
        .where(
          and(
            eq(creatorSubscriptions.userId, userId),
            eq(creatorSubscriptions.subscription_type, 'trial')
          )
        )
        .limit(1)
        .then(rows => rows.length > 0);
      
      // New users should NOT be able to create agents until they activate a trial
      return NextResponse.json({
        canCreate: false,
        message: "You need to activate your free trial before creating your first agent.",
        agentsLeft: 0,
        totalAgents: 1,
        currentAgentCount: agentCount
      });
    }
    
    const balance = await db
      .select()
      .from(subscriptionBalance)
      .where(eq(subscriptionBalance.subscription_id, activeSubscription.id))
      .limit(1)
      .then(rows => rows[0]);
    
    if (!balance) {
      console.log('No balance found for subscription:', activeSubscription.id);
      return NextResponse.json(
        { error: 'No balance found for your subscription' },
        { status: 404 }
      );
    }
    
    console.log('Found subscription balance:', {
      balanceId: balance.id,
      agentsLeft: balance.agentsLeft,
      totalAgents: balance.total_agents
    });
    
    // Get the plan details
    const plan = await db
      .select()
      .from(creatorPlans)
      .where(eq(creatorPlans.id, activeSubscription.planId))
      .limit(1)
      .then(rows => rows[0]);
    
    if (!plan) {
      console.log('No plan found for id:', activeSubscription.planId);
      return NextResponse.json(
        { error: 'Plan details not found' },
        { status: 404 }
      );
    }
    
    // 3. Count the number of agents the user has already created
    const agentCount = await db
      .select({ count: sql`count(*)` })
      .from(agents)
      .where(
        and(
          eq(agents.userId, userId),
          // Only count active agents, not drafts
          eq(agents.status, 'active')
        )
      )
      .then(result => Number(result[0]?.count || 0));
    

    // 4. Check if they can create another agent
    const canCreate = balance.agentsLeft > 0;
    
    return NextResponse.json({
      canCreate,
      message: canCreate 
        ? `You can create ${balance.agentsLeft} more agent(s).`
        : `You've reached your plan's agent limit. Please upgrade to create more agents.`,
      agentsLeft: balance.agentsLeft,
      totalAgents: balance.total_agents,
      currentAgentCount: agentCount,
      plan: {
        name: plan.name,
        monthlyPrice: plan.monthlyPrice,
        yearlyPrice: plan.yearlyPrice,
        agentsAllowed: plan.agentsAllowed,
        subscriptionType: activeSubscription.subscription_type,
        expiryDate: activeSubscription.expiry_date
      }
    });
    
  } catch (error) {
    console.error('Error checking plan limits:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    return NextResponse.json(
      { error: 'Failed to check plan limits' },
      { status: 500 }
    );
  }
}