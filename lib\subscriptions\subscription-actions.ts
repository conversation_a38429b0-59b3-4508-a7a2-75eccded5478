import { toast } from 'sonner';

/**
 * Interface for cancellation response
 */
interface CancellationResponse {
  success: boolean;
  url?: string;
  message?: string;
  isCanceled?: boolean;
}

/**
 * Centralized function to handle agent subscription cancellation
 * @param agentId The ID of the agent whose subscription is being canceled
 * @returns Object containing success status, portal URL (if successful), and message
 */
export async function cancelAgentSubscription(agentId: string): Promise<CancellationResponse> {
  if (!agentId) {
    return { 
      success: false, 
      message: 'No agent ID provided' 
    };
  }
  
  try {
    // Call the API to create a cancellation portal session for this specific agent
    const response = await fetch('/api/subscription/cancel-agent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ agentId })
    });
    
    // Parse the JSON response once
    const data = await response.json();
    
    // Check for error in the response
    if (!response.ok) {
      // Check specifically for the "already canceled" error
      if (data.error && data.error.includes('already set to be canceled at period end')) {
        // Handle this as a success case with a different message
        toast.info('This subscription is already scheduled for cancellation');
        return { 
          success: true, 
          message: 'Subscription is already scheduled for cancellation',
          isCanceled: true
        };
      }
      
      // Handle other errors
      const errorMessage = data.error || 'Failed to create cancellation portal';
      toast.error(errorMessage);
      return { 
        success: false, 
        message: errorMessage 
      };
    }
    
    // If we have a URL, return it for opening the portal
    if (data.url) {
      return { 
        success: true, 
        url: data.url 
      };
    } else {
      toast.error('No portal URL returned from the server');
      return { 
        success: false, 
        message: 'No portal URL returned from the server' 
      };
    }
  } catch (error: any) {
    // Provide more specific error message if it's about no Stripe customer
    if (error.message && error.message.includes('No Stripe customer found')) {
      const message = 'You need to have an active subscription to cancel it';
      toast.error(message);
      return { 
        success: false, 
        message 
      };
    } else if (error.message && error.message.includes('already set to be canceled')) {
      const message = 'This subscription is already scheduled for cancellation';
      toast.info(message);
      return { 
        success: true, 
        message,
        isCanceled: true
      };
    }
    
    // Generic error message
    toast.error('Failed to process cancellation request');
    return { 
      success: false, 
      message: error.message || 'Failed to process cancellation request' 
    };
  }
}

/**
 * Opens the cancellation portal in a new tab
 * @param url The URL to the Stripe portal
 */
export function openCancellationPortal(url: string): void {
  window.open(url, '_blank');
}
