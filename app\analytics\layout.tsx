'use client';

import { DashboardSidebar } from '@/components/dashboard-sidebar';
import { SidebarProvider, useSidebar } from '@/components/ui/sidebar';
import { useSession } from 'next-auth/react';
import { PanelLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';

function AnalyticsContent({ children }: { children: React.ReactNode }) {
  const { state, toggleSidebar } = useSidebar();
  const isCollapsed = state === 'collapsed';
  const [isMobile, setIsMobile] = useState(false);
  
  // Check if we're on a mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  
  return (
    <main 
      className="flex-1 overflow-auto transition-all duration-300 ease-in-out"
      style={{ 
        marginLeft: isMobile ? '0' : (isCollapsed ? '3rem' : '16rem')
      }}
    >
      {(isCollapsed || isMobile) && (
        <Button
          variant="outline"
          size="icon"
          className="fixed top-4 left-4 z-10 h-8 w-8"
          onClick={toggleSidebar}
        >
          <PanelLeft className="h-4 w-4" />
        </Button>
      )}
      {children}
    </main>
  );
}

export default function AnalyticsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session } = useSession();
  const [isMobile, setIsMobile] = useState(false);
  
  // Check if we're on a mobile device for initial sidebar state
  useEffect(() => {
    setIsMobile(window.innerWidth < 768);
  }, []);
  
  return (
    <div className="flex h-screen">
      <SidebarProvider defaultOpen={!isMobile}>
        <DashboardSidebar user={session?.user} />
        <AnalyticsContent>
          {children}
        </AnalyticsContent>
      </SidebarProvider>
    </div>
  );
}
