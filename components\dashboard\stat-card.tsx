'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { LucideIcon } from 'lucide-react';

// Helper function to format token numbers
export const formatTokens = (tokens: number): string => {
  if (tokens >= 1000000) {
    return `${(tokens / 1000000).toFixed(1)}M`;
  } else if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(1)}K`;
  }
  return tokens.toString();
};

// Helper function to format MB values with 1 decimal place
export const formatMB = (mb: number): string => {
  return mb.toFixed(1);
};

interface StatCardProps {
  title: string;
  icon: LucideIcon;
  isLoading?: boolean;
  value: string | number;
  description?: string;
  progressValue?: number;
  showProgress?: boolean;
}

export function StatCard({
  title,
  icon: Icon,
  isLoading = false,
  value,
  description,
  progressValue = 0,
  showProgress = true,
}: StatCardProps) {
  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2 p-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-28 mb-2" />
            {description && <Skeleton className="h-4 w-32" />}
            {showProgress && (
              <div className="mt-2">
                <Skeleton className="h-1.5 w-full" />
              </div>
            )}
          </div>
        ) : (
          <>
            <div className="text-xl font-bold">
              {value}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground">
                {description}
              </p>
            )}
            {showProgress && (
              <div className="mt-2">
                <Progress value={progressValue} className="h-1.5" />
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

export function RevenueCard({ revenue, isLoading }: { revenue: number; isLoading: boolean }) {
  return (
    <StatCard
      title="Revenue"
      icon={require('lucide-react').DollarSign}
      isLoading={isLoading}
      value={`$${typeof revenue === 'number' ? revenue.toFixed(2) : Number(revenue).toFixed(2)}`}
      description="Total revenue"
      showProgress={false}
    />
  );
}

export function TokensCard({ 
  tokensUsed, 
  tokensTotal, 
  tokensPercentage, 
  isLoading 
}: { 
  tokensUsed: number; 
  tokensTotal: number; 
  tokensPercentage: number; 
  isLoading: boolean 
}) {
  return (
    <StatCard
      title="Tokens"
      icon={require('lucide-react').Zap}
      isLoading={isLoading}
      value={formatTokens(tokensUsed)}
      description={`of ${formatTokens(tokensTotal)} tokens used`}
      progressValue={tokensPercentage}
    />
  );
}

export function KnowledgeBaseCard({ 
  knowledgeBaseUsed, 
  knowledgeBaseTotal, 
  knowledgeBasePercentage, 
  isLoading 
}: { 
  knowledgeBaseUsed: number; 
  knowledgeBaseTotal: number; 
  knowledgeBasePercentage: number; 
  isLoading: boolean 
}) {
  return (
    <StatCard
      title="Knowledge Base"
      icon={require('lucide-react').Database}
      isLoading={isLoading}
      value={`${formatMB(knowledgeBaseUsed)} MB`}
      description={`of ${formatMB(knowledgeBaseTotal)} MB used`}
      progressValue={knowledgeBasePercentage}
    />
  );
}

export function AgentsCard({ 
  agentsUsed, 
  agentsTotal, 
  agentsPercentage, 
  isLoading 
}: { 
  agentsUsed: number; 
  agentsTotal: number; 
  agentsPercentage: number; 
  isLoading: boolean 
}) {
  return (
    <StatCard
      title="Agents"
      icon={require('lucide-react').Bot}
      isLoading={isLoading}
      value={agentsUsed}
      description={`of ${agentsTotal} agents used`}
      progressValue={agentsPercentage}
    />
  );
}
