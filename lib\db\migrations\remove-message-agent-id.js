const { sql } = require('drizzle-orm');
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL environment variable is not set');
}

const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

/**
 * Migration to remove agent_id from Message_v2 table
 */
async function removeAgentIdColumn() {
  console.log('🔄 Removing agent_id from Message_v2 table...');
  
  try {
    // Remove agent_id from Message_v2 table
    await db.execute(sql`
      ALTER TABLE "Message_v2"
      DROP COLUMN IF EXISTS "agent_id";
    `);
    
    console.log('✅ agent_id removed successfully!');
  } catch (error) {
    console.error('❌ Failed to remove agent_id:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  removeAgentIdColumn()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
