import { pgTable, uuid, boolean, date, timestamp, real, pgEnum } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { user } from '../schema';
import { creatorPlans } from '../schema';

export const subscriptionTypeEnum = pgEnum('subscription_type', ['trial', 'monthly', 'yearly']);
export const subscriptionStatusEnum = pgEnum('subscription_status', ['active', 'inactive']);

export const creatorSubscriptions = pgTable('CreatorSubscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').notNull().references(() => user.id, { onDelete: 'cascade' }),
  planId: uuid('planId').notNull().references(() => creatorPlans.id, { onDelete: 'cascade' }),
  isPaid: boolean('isPaid').notNull().default(false),
  subscription_type: subscriptionTypeEnum('subscription_type').notNull().default('monthly'),
  status: subscriptionStatusEnum('status').notNull().default('inactive'),
  expiry_date: date('expiry_date'),
  amount_paid: real('amount_paid').notNull().default(0),
  created_at: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`),
  updated_at: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
