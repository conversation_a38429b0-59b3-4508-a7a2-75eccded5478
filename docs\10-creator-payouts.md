# Creator Payouts

## Overview

The BTI platform implements a creator payout system that enables agent creators to monetize their work and receive payments from user subscriptions and lifetime access purchases. This document details the complete payout system, including revenue sharing, payout processing, and financial reporting.

## Payout Model

The platform uses a revenue sharing model:

1. **Platform Fee**: A percentage of each transaction is retained by the platform
2. **Creator Revenue**: The remaining percentage is allocated to the creator
3. **Payment Schedule**: Payouts are processed on a regular schedule (typically monthly)
4. **Minimum Threshold**: A minimum amount may be required before processing a payout

## Database Schema

The payout system uses several database tables:

### Creator Payments

Tracks payments to creators:

```typescript
export const creatorPayments = pgTable('creator_payments', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').notNull().references(() => creatorSubscriptions.id, { onDelete: 'cascade' }),
  amount: integer('amount').notNull(),
  status: text('status').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  paidAt: timestamp('paid_at'),
});
```

### Payout Settings

Stores creator payout preferences:

```typescript
export const payoutSettings = pgTable('payout_settings', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('userId').notNull().references(() => user.id, { onDelete: 'cascade' }),
  paypalEmail: text('paypal_email').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
```

## Payout Process

### 1. Revenue Calculation

The system calculates creator revenue based on:

1. **Subscription Revenue**: Monthly or yearly subscription payments
2. **Lifetime Access Revenue**: One-time payments for lifetime access
3. **Platform Fee**: Deduction of the platform's percentage
4. **Tax Considerations**: Handling of applicable taxes

### 2. Payout Scheduling

Payouts are scheduled according to the platform's policy:

1. **Regular Schedule**: Typically monthly
2. **Holding Period**: A period before funds are available for payout
3. **Minimum Threshold**: Minimum amount required for payout
4. **Manual Approval**: Optional manual approval step

### 3. Payout Processing

Payouts are processed through the configured method:

1. **PayPal**: Direct transfer to creator's PayPal account
2. **Bank Transfer**: Direct deposit to creator's bank account
3. **Other Methods**: Additional payout methods as supported

### 4. Record Keeping

The system maintains detailed records of all payouts:

1. **Payout Amounts**: Amount of each payout
2. **Payout Dates**: When payouts were processed
3. **Payout Status**: Status of each payout (pending, processed, failed)
4. **Transaction IDs**: Reference IDs for each payout transaction

## Creator Dashboard

Creators can manage their payouts through a dedicated dashboard:

### 1. Revenue Overview

The dashboard provides a revenue overview:

1. **Total Revenue**: Total revenue earned
2. **Available Balance**: Funds available for payout
3. **Pending Revenue**: Funds in the holding period
4. **Historical Revenue**: Revenue trends over time

### 2. Payout Settings

Creators can configure their payout settings:

1. **Payout Method**: Selection of preferred payout method
2. **Payout Details**: Configuration of payout details (e.g., PayPal email)
3. **Tax Information**: Management of tax information
4. **Automatic Payouts**: Configuration of automatic payout preferences

### 3. Payout History

The dashboard provides a payout history:

1. **Past Payouts**: List of all past payouts
2. **Payout Details**: Details of each payout
3. **Payout Status**: Status of each payout
4. **Transaction References**: Reference information for each payout

## API Endpoints

The payout system uses the following API endpoints:

### 1. Payout Settings

```
GET /api/payouts/settings - Get payout settings
POST /api/payouts/settings - Update payout settings
```

### 2. Payout History

```
GET /api/payouts/history - Get payout history
GET /api/payouts/history/:id - Get details of a specific payout
```

### 3. Revenue Analytics

```
GET /api/revenue/overview - Get revenue overview
GET /api/revenue/analytics - Get detailed revenue analytics
```

## Implementation Details

### 1. Revenue Calculation

The system calculates revenue when payments are received:

```typescript
// Simplified revenue calculation
async function calculateCreatorRevenue(payment: any) {
  const { amount, agentId } = payment;
  
  // Get the agent to find the creator
  const agent = await getAgentById({ agentId });
  
  if (!agent) {
    throw new Error('Agent not found');
  }
  
  // Calculate platform fee (e.g., 20%)
  const platformFeePercentage = 0.2;
  const platformFee = Math.round(amount * platformFeePercentage);
  
  // Calculate creator revenue
  const creatorRevenue = amount - platformFee;
  
  // Create revenue record
  await db.insert(creatorPayments).values({
    userId: agent.userId,
    subscriptionId: payment.subscriptionId,
    amount: creatorRevenue,
    status: 'pending',
  });
  
  return {
    platformFee,
    creatorRevenue,
  };
}
```

### 2. Payout Processing

The system processes payouts on a regular schedule:

```typescript
// Simplified payout processing
async function processCreatorPayouts() {
  // Get all pending payments that meet the threshold
  const pendingPayments = await db.query.creatorPayments.findMany({
    where: and(
      eq(creatorPayments.status, 'pending'),
      gte(creatorPayments.createdAt, subtractDays(new Date(), 30)), // 30-day holding period
    ),
  });
  
  // Group payments by creator
  const paymentsByCreator = groupBy(pendingPayments, 'userId');
  
  // Process payouts for each creator
  for (const [userId, payments] of Object.entries(paymentsByCreator)) {
    // Calculate total amount
    const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);
    
    // Check if amount meets minimum threshold
    if (totalAmount < MINIMUM_PAYOUT_AMOUNT) {
      continue;
    }
    
    // Get payout settings
    const payoutSettings = await db.query.payoutSettings.findFirst({
      where: eq(payoutSettings.userId, userId),
    });
    
    if (!payoutSettings) {
      continue;
    }
    
    try {
      // Process payout (e.g., via PayPal)
      const payoutResult = await processPaypalPayout(payoutSettings.paypalEmail, totalAmount);
      
      // Update payment records
      await db.transaction(async (tx) => {
        for (const payment of payments) {
          await tx.update(creatorPayments)
            .set({
              status: 'paid',
              paidAt: new Date(),
            })
            .where(eq(creatorPayments.id, payment.id));
        }
      });
      
      // Notify creator
      await notifyCreatorOfPayout(userId, totalAmount);
    } catch (error) {
      console.error('Payout processing error:', error);
      // Handle payout failure
    }
  }
}
```

### 3. PayPal Integration

The system integrates with PayPal for payouts:

```typescript
// Simplified PayPal payout processing
async function processPaypalPayout(paypalEmail: string, amount: number) {
  try {
    // Create PayPal payout
    const payoutResult = await paypalClient.createPayout({
      sender_batch_header: {
        sender_batch_id: `PAYOUT_${Date.now()}`,
        email_subject: 'You have a payout from BuildThatIdea',
        email_message: 'Your creator payout has been processed.',
      },
      items: [
        {
          recipient_type: 'EMAIL',
          amount: {
            value: (amount / 100).toFixed(2), // Convert cents to dollars
            currency: 'USD',
          },
          note: 'Thanks for creating on BuildThatIdea!',
          receiver: paypalEmail,
        },
      ],
    });
    
    return payoutResult;
  } catch (error) {
    console.error('PayPal payout error:', error);
    throw error;
  }
}
```

## Security Considerations

The payout system includes several security measures:

### 1. Access Control

1. **Creator Verification**: Ensuring only verified creators can receive payouts
2. **Identity Verification**: Verifying creator identity before processing payouts
3. **Authorization**: Restricting access to payout information

### 2. Financial Security

1. **Secure Storage**: Securely storing financial information
2. **Encryption**: Encrypting sensitive financial data
3. **Audit Logging**: Logging all financial transactions
4. **Reconciliation**: Regular reconciliation of financial records

### 3. Fraud Prevention

1. **Suspicious Activity Monitoring**: Monitoring for suspicious payout activity
2. **Verification Checks**: Additional verification for large payouts
3. **Rate Limiting**: Limiting the frequency of payout setting changes

## Compliance

The payout system ensures compliance with relevant regulations:

### 1. Tax Compliance

1. **Tax Information Collection**: Collecting necessary tax information from creators
2. **Tax Reporting**: Generating required tax reports
3. **Tax Withholding**: Withholding taxes as required by law

### 2. Financial Regulations

1. **Anti-Money Laundering (AML)**: Compliance with AML regulations
2. **Know Your Customer (KYC)**: Implementing KYC procedures as required
3. **Payment Processor Compliance**: Ensuring compliance with payment processor requirements

## Error Handling

The payout system includes comprehensive error handling:

### 1. Payout Failures

1. **Retry Logic**: Automatically retrying failed payouts
2. **Manual Intervention**: Allowing manual intervention for persistent failures
3. **Notification**: Notifying creators of payout issues

### 2. Data Inconsistencies

1. **Reconciliation**: Regular reconciliation of financial records
2. **Correction Procedures**: Procedures for correcting data inconsistencies
3. **Audit Trails**: Maintaining audit trails of all corrections

## Reporting

The payout system includes comprehensive reporting:

### 1. Creator Reports

1. **Earnings Reports**: Detailed reports of creator earnings
2. **Payout Reports**: Reports of all payouts
3. **Tax Reports**: Tax-related reports for creators

### 2. Platform Reports

1. **Revenue Reports**: Reports of platform revenue
2. **Fee Reports**: Reports of platform fees
3. **Payout Summary Reports**: Summary reports of all payouts

## Future Enhancements

Planned enhancements to the payout system:

1. **Additional Payout Methods**: Support for more payout methods
2. **Faster Payouts**: Reducing the holding period for trusted creators
3. **Advanced Analytics**: More detailed revenue analytics
4. **Automated Tax Handling**: Improved tax handling automation
5. **Direct Bank Transfers**: Support for direct bank transfers
