import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getOrCreateUserSubscription } from '@/lib/db/subscription-queries';
import { db } from '@/lib/db/client';
import { agents, agentProfiles, vote } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * Consolidated bootstrap API that returns all necessary data for agent chat page
 * Combines agent profile, subscription data, vote data, etc. to reduce separate API calls
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    const { id: agentId } = await params;
    const url = new URL(request.url);
    const chatId = url.searchParams.get('chatId');
    
    if (!agentId) {
      return NextResponse.json(
        { error: 'Agent ID is required' },
        { status: 400 }
      );
    }

    // Get agent data
    const [agent] = await db
      .select()
      .from(agents)
      .where(eq(agents.id, agentId));
    
    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }
    
    // Parallel data fetching for all needed information
    const [
      profileData,
      subscriptionData,
      voteData,
    ] = await Promise.all([
      // 1. Get agent profile data
      (async () => {
        const profile = await db.query.agentProfiles.findFirst({
          where: eq(agentProfiles.agentId, agentId),
        });
        return profile || { agentId, features: [] };
      })(),
      
      // 2. Get subscription data (only if user is logged in)
      (async () => {
        if (!session?.user?.id) {
          return { remainingMessages: 0, isPaid: false };
        }
        
        // Check if agent is free
        if (agent.accessLevel === 'free') {
          return { remainingMessages: Number.POSITIVE_INFINITY, isPaid: false };
        }
        
        // Get subscription data for paid agents
        const subscriptions = await getOrCreateUserSubscription({
          userId: session.user.id as string,
          agentId,
        });
        
        const subscription = Array.isArray(subscriptions) ? subscriptions[0] : subscriptions;
        
        // Check cancellation status
        let isCanceled = false;
        let cancelDate = null;
        
        try {
          // Import the necessary schema
          const { userCancelationRequest } = await import('@/lib/db/schema');
          
          // Query for cancellation requests
          const cancelRequests = await db
            .select()
            .from(userCancelationRequest)
            .where(
              and(
                eq(userCancelationRequest.subscriptionId, subscription.id),
                eq(userCancelationRequest.status, 'scheduled')
              )
            );
          
          if (cancelRequests && cancelRequests.length > 0) {
            isCanceled = true;
            cancelDate = subscription.expiryDate;
          }
        } catch (error) {
          // Continue without cancellation info if there's an error
        }
        
        return {
          remainingMessages: subscription.allowedFreeMessages,
          isPaid: subscription.isPaid,
          expiryDate: subscription.expiryDate,
          isCanceled,
          cancelDate,
        };
      })(),
      
      // 3. Get vote data for the current chat
      (async () => {
        if (!chatId) {
          return { votes: [] };
        }
        
        const votes = await db
          .select()
          .from(vote)
          .where(eq(vote.chatId, chatId));
        
        return { votes };
      })(),
    ]);

    // Add cache-control header to enable browser caching for 30 seconds
    return NextResponse.json(
      {
        agent,
        profile: profileData,
        subscription: subscriptionData,
        votes: voteData.votes,
      },
      {
        headers: {
          'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60'
        }
      }
    );
  } catch (error) {
    console.error('Error in bootstrap API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bootstrap data' },
      { status: 500 }
    );
  }
}
