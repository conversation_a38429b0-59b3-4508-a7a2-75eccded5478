import { NextResponse } from 'next/server';
import { updateAgentProfiles } from '@/lib/db/update-agent-profiles';

// This endpoint is meant to be called by a cron job
export async function POST(request: Request) {
  try {
    // Check for a secret to ensure this is called by the cron job
    const authHeader = request.headers.get('authorization');
    
    if (process.env.CRON_SECRET && authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Run the update script
    await updateAgentProfiles();
    
    return NextResponse.json({ success: true, message: 'Agent profiles updated successfully' });
  } catch (error) {
    console.error('Error updating agent profiles:', error);
    return NextResponse.json(
      { error: 'Failed to update agent profiles' },
      { status: 500 }
    );
  }
}
