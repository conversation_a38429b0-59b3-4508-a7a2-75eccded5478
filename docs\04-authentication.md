# Authentication Flow

## Overview

The Fin platform implements a secure authentication system using NextAuth.js 5 with a custom credentials provider. The authentication flow includes user registration, login, email verification, password reset, and session management.

## Technology Stack

- **NextAuth.js 5**: Authentication framework for Next.js
- **bcrypt-ts**: Password hashing library
- **JWT (JSON Web Tokens)**: For secure session management
- **PostgreSQL**: For storing user credentials and session data
- **Drizzle ORM**: For database interactions

## Authentication Components

### 1. Auth Configuration

The authentication configuration is defined in `/app/auth/auth.config.ts`:

```typescript
// Key configuration options for NextAuth
export const authConfig = {
  pages: {
    signIn: '/login',
    signOut: '/login',
    error: '/login',
    verifyRequest: '/login',
    newUser: '/register',
  },
  callbacks: {
    authorized({ auth, request }) {
      const isLoggedIn = !!auth?.user;
      const isOnDashboard = request.nextUrl.pathname.startsWith('/dashboard');
      const isOnAgent = request.nextUrl.pathname.startsWith('/agent');
      const isOnChat = request.nextUrl.pathname.startsWith('/chat');
      
      if (!isLoggedIn && (isOnDashboard || isOnAgent || isOnChat)) {
        return false;
      }
      
      return true;
    },
  },
  providers: [],
};
```

### 2. Auth Implementation

The main authentication implementation is in `/app/auth/auth.ts`:

```typescript
export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  secret: process.env.AUTH_SECRET,
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        try {
          const users = await getUser(email);
          
          if (users.length === 0) {
            return null;
          }
          
          const passwordsMatch = await compare(password, users[0].password!);
          
          if (!passwordsMatch) {
            return null;
          }
          
          return users[0] as any;
        } catch (error) {
          console.error('Error in authorize function:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
});
```

### 3. Authentication Actions

The authentication actions are defined in `/app/auth/actions.ts` and include functions for user registration, login, and logout.

## Authentication Flows

### 1. Registration Flow

1. **User Input**: User enters email, password, and optional referral code on the registration page (`/register`)
2. **Form Validation**: Client-side validation using React Hook Form and Zod
3. **API Request**: Form data is submitted to the registration API endpoint
4. **Password Hashing**: Password is hashed using bcrypt
5. **User Creation**: New user record is created in the database
6. **Email Verification**: Verification email is sent to the user (if enabled)
7. **Redirect**: User is redirected to the login page or dashboard

### 2. Login Flow

1. **User Input**: User enters email and password on the login page (`/login`)
2. **Form Validation**: Client-side validation using React Hook Form and Zod
3. **API Request**: Credentials are submitted to NextAuth.js
4. **Authentication**: NextAuth.js verifies credentials against the database
   - Retrieves user by email
   - Compares hashed password
5. **Session Creation**: JWT session is created for authenticated users
6. **Redirect**: User is redirected to the dashboard

### 3. Email Verification Flow

1. **Verification Email**: User receives an email with a verification link
2. **Link Activation**: User clicks the verification link
3. **Token Validation**: System validates the verification token
4. **User Update**: User's `verified` status is updated in the database
5. **Redirect**: User is redirected to the login page or dashboard

### 4. Password Reset Flow

1. **Forgot Password**: User requests a password reset on the forgot password page (`/forgot-password`)
2. **Email Submission**: User enters their email address
3. **Reset Token**: System generates a password reset token and sends it via email
4. **Reset Form**: User clicks the link in the email and is taken to the reset password form
5. **Token Validation**: System validates the reset token
6. **Password Update**: User enters a new password, which is hashed and updated in the database
7. **Redirect**: User is redirected to the login page

### 5. Logout Flow

1. **Logout Request**: User clicks the logout button
2. **Session Termination**: NextAuth.js terminates the user's session
3. **Cookie Removal**: Authentication cookies are removed
4. **Redirect**: User is redirected to the login page

## Session Management

The Fin platform uses JWT-based sessions with the following characteristics:

1. **JWT Storage**: Tokens are stored in HTTP-only cookies
2. **Session Duration**: Sessions last for 30 days by default
3. **Session Renewal**: Sessions are automatically renewed on activity
4. **Session Validation**: Middleware validates sessions on protected routes

## Protected Routes

The following routes are protected and require authentication:

- `/dashboard/*`: User dashboard and settings
- `/agent/*`: Agent creation and management
- `/chat/*`: Chat interface
- `/billing/*`: Billing and subscription management
- `/edit/*`: Agent editing
- `/create/*`: Agent creation (except the initial page)

## Middleware

The authentication middleware in `/middleware.ts` handles route protection and redirects:

```typescript
export default authMiddleware({
  publicRoutes: [
    '/',
    '/login',
    '/register',
    '/forgot-password',
    '/verify',
    '/api/auth/(.*)',
    '/api/webhook/(.*)',
    '/api/cron/(.*)',
  ],
});
```

## Security Considerations

1. **Password Hashing**: All passwords are hashed using bcrypt before storage
2. **HTTPS**: All authentication requests are made over HTTPS
3. **CSRF Protection**: NextAuth.js includes CSRF protection
4. **Rate Limiting**: API routes implement rate limiting for security
5. **HTTP-Only Cookies**: Session tokens are stored in HTTP-only cookies
6. **Secure Headers**: Security headers are set for all responses

## User Types and Permissions

The Fin platform supports multiple user types with different permissions:

1. **User**: Can use agents, create chats, and subscribe to paid agents
2. **Creator**: Can create and manage agents, receive payments
3. **Both**: Has both user and creator permissions

User types are stored in the `type` field of the `User` table and are used for authorization checks throughout the application.

## Database Schema

The authentication system uses the following database tables:

1. **User**: Stores user credentials and profile information
2. **Session**: Managed by NextAuth.js for session storage (if using database sessions)
3. **VerificationToken**: For email verification tokens
4. **PasswordResetToken**: For password reset tokens

## API Endpoints

The authentication system exposes the following API endpoints:

1. **POST /api/auth/register**: For user registration
2. **GET/POST /api/auth/signin**: For user login
3. **GET/POST /api/auth/signout**: For user logout
4. **POST /api/auth/verify**: For email verification
5. **POST /api/auth/forgot-password**: For password reset requests
6. **POST /api/auth/reset-password**: For password reset

## Client-Side Authentication

On the client side, authentication state is managed using the `useSession` hook from NextAuth.js:

```typescript
import { useSession } from 'next-auth/react';

function MyComponent() {
  const { data: session, status } = useSession();
  
  if (status === 'loading') {
    return <div>Loading...</div>;
  }
  
  if (status === 'unauthenticated') {
    return <div>Please log in</div>;
  }
  
  return <div>Welcome, {session.user.email}</div>;
}
```

## Error Handling

The authentication system includes comprehensive error handling:

1. **Invalid Credentials**: Returns appropriate error messages
2. **Rate Limiting**: Prevents brute force attacks
3. **Expired Tokens**: Handles expired verification and reset tokens
4. **Database Errors**: Gracefully handles database connection issues
5. **Validation Errors**: Returns detailed validation error messages
