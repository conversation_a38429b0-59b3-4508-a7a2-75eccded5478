import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { fileUrl } = await request.json();

    if (!fileUrl) {
      return NextResponse.json(
        { error: 'File URL is required' },
        { status: 400 }
      );
    }

    console.log(`Testing file URL: ${fileUrl}`);

    // Test 1: Basic fetch
    const response = await fetch(fileUrl, {
      method: 'HEAD', // Just get headers, not the full file
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; BotAgent/1.0)'
      }
    });

    const responseInfo = {
      url: fileUrl,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      contentType: response.headers.get('content-type'),
      contentLength: response.headers.get('content-length'),
    };

    console.log('Response info:', responseInfo);

    // Test 2: Try to fetch first few bytes
    if (response.ok) {
      const getResponse = await fetch(fileUrl, {
        headers: {
          'Range': 'bytes=0-500', // Just get first 500 bytes
          'User-Agent': 'Mozilla/5.0 (compatible; BotAgent/1.0)'
        }
      });

      const contentType = getResponse.headers.get('content-type');
      
      if (contentType?.includes('text/html')) {
        const text = await getResponse.text();
        return NextResponse.json({
          ...responseInfo,
          warning: 'File URL returns HTML content',
          htmlPreview: text.substring(0, 200),
          recommendation: 'This URL might be a login page, error page, or the file might have expired'
        });
      }

      return NextResponse.json({
        ...responseInfo,
        success: true,
        message: 'File appears to be accessible',
      });
    }

    // Test 3: If not OK, try to get error details
    if (!response.ok) {
      let errorDetails = '';
      
      try {
        // Try to read the response body
        const errorResponse = await fetch(fileUrl);
        const contentType = errorResponse.headers.get('content-type');
        
        if (contentType?.includes('text/html')) {
          const text = await errorResponse.text();
          errorDetails = text.substring(0, 500);
        } else if (contentType?.includes('application/json')) {
          const json = await errorResponse.json();
          errorDetails = JSON.stringify(json, null, 2);
        } else {
          errorDetails = await errorResponse.text();
        }
      } catch (e) {
        errorDetails = 'Could not read error response body';
      }

      return NextResponse.json({
        ...responseInfo,
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        errorDetails: errorDetails.substring(0, 500),
      });
    }

  } catch (error) {
    console.error('Error testing file URL:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.name : 'Unknown',
    });
  }
}

// Simple GET endpoint to test if the debug route is working
export async function GET() {
  return NextResponse.json({
    message: 'Debug endpoint is working. Send a POST request with { "fileUrl": "..." }'
  });
}