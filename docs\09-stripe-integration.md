# Stripe Integration

## Overview

The BuildThatIdea platform integrates with Stripe for payment processing, subscription management, and financial operations. This document details the technical implementation of the Stripe integration, including API usage, webhook handling, and data management.

## Technology Stack

- **Stripe API**: Core payment processing functionality
- **Stripe SDK**: JavaScript/TypeScript client for Stripe API
- **Stripe Checkout**: Hosted payment pages
- **Stripe Webhooks**: Event notifications for payment lifecycle
- **Stripe Customer Portal**: Self-service subscription management

## Integration Components

### 1. Stripe Configuration

The Stripe integration is configured in the platform's environment variables:

```
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

The Stripe client is initialized in various parts of the codebase, for example in `/lib/stripe/checkout.ts`:

```typescript
import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-03-31.basil',
});
```

### 2. Product and Price Management

The platform manages Stripe products and prices for agents in `/lib/stripe/product.ts`:

```typescript
export async function getOrCreateAgentProduct({
  agentId,
  agentName,
  description,
  priceAmount,
  interval = 'month',
  currency = 'usd',
}: CreateAgentProductParams) {
  try {
    // Check if the product already exists
    const existingProduct = await stripe.products.search({
      query: `metadata['agentId']:'${agentId}'`,
    });

    if (existingProduct.data.length > 0) {
      // Product exists, get the associated price
      const product = existingProduct.data[0];
      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
      });

      // Return the first active price
      if (prices.data.length > 0) {
        return {
          productId: product.id,
          priceId: prices.data[0].id,
        };
      }

      // No active price found, create a new one
      const newPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: priceAmount,
        currency,
        recurring: interval === 'once' ? undefined : {
          interval: interval === 'year' ? 'year' : 'month',
        },
      });

      return {
        productId: product.id,
        priceId: newPrice.id,
      };
    }

    // Create a new product
    const product = await stripe.products.create({
      name: agentName,
      description: description || `Subscription for ${agentName}`,
      metadata: {
        agentId,
      },
    });

    // Create a price for the product
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: priceAmount,
      currency,
      recurring: interval === 'once' ? undefined : {
        interval: interval === 'year' ? 'year' : 'month',
      },
    });

    return {
      productId: product.id,
      priceId: price.id,
    };
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}
```

### 3. Checkout Session Creation

The platform creates Stripe checkout sessions for payments in `/lib/stripe/checkout.ts`:

```typescript
export async function createAgentCheckoutSession(params: CreateCheckoutSessionParams) {
  try {
    // First, create or get the product and price
    const { priceId } = await getOrCreateAgentProduct({
      agentId: params.agentId,
      agentName: params.agentName,
      description: params.description || `Subscription for ${params.agentName}`,
      priceAmount: params.priceAmount || 999, // Default to $9.99 in cents
      interval: params.interval || 'month',
      currency: params.currency || 'usd',
    });

    // Create the checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/create/success?agent=${params.agentSlug}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/create`,
      metadata: {
        agentId: params.agentId,
        userId: params.userId,
      },
    });

    return {
      sessionId: session.id,
      sessionUrl: session.url,
      priceId,
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
}
```

### 4. Customer Management

The platform manages Stripe customers and associates them with users:

```typescript
export async function getOrCreateStripeCustomer(userId: string, email: string) {
  // Check if the user already has a Stripe customer
  const existingCustomer = await db.query.stripeCustomers.findFirst({
    where: eq(stripeCustomers.userId, userId),
  });

  if (existingCustomer) {
    return existingCustomer.stripeCustomerId;
  }

  // Create a new Stripe customer
  const customer = await stripe.customers.create({
    email,
    metadata: {
      userId,
    },
  });

  // Store the customer ID in the database
  await db.insert(stripeCustomers).values({
    userId,
    stripeCustomerId: customer.id,
  });

  return customer.id;
}
```

## Webhook Implementation

### 1. Webhook Endpoint

The platform implements a webhook endpoint to receive Stripe events:

```typescript
export async function POST(req: Request) {
  const body = await req.text();
  const signature = req.headers.get('stripe-signature') as string;
  
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
    
    // Store the event in the database
    await db.insert(stripeEvents).values({
      id: event.id,
      type: event.type,
      data: event.data.object,
    });
    
    // Handle different event types
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      // Handle other event types
    }
    
    return new Response(JSON.stringify({ received: true }));
  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(
      JSON.stringify({ error: 'Webhook signature verification failed' }),
      { status: 400 }
    );
  }
}
```

### 2. Event Handlers

The platform implements handlers for various Stripe events:

```typescript
async function handleCheckoutSessionCompleted(session: any) {
  const { agentId, userId } = session.metadata;
  
  // Create subscription record
  await db.insert(userSubscriptions).values({
    agentId,
    userId,
    status: 'active',
    stripeSubscriptionId: session.subscription,
    currentPeriodStart: new Date(session.created * 1000),
    currentPeriodEnd: new Date((session.created + 30 * 24 * 60 * 60) * 1000), // 30 days later
  });
}

async function handleSubscriptionCreated(subscription: any) {
  // Update subscription record with details
}

async function handleSubscriptionUpdated(subscription: any) {
  // Update subscription status and period
}

async function handleSubscriptionDeleted(subscription: any) {
  // Mark subscription as cancelled
}
```

## Database Schema

The platform uses several tables to track Stripe data:

### 1. Stripe Events

```typescript
export const stripeEvents = pgTable('stripe_events', {
  id: text('id').primaryKey().notNull(),
  type: text('type').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  data: jsonb('data'),
});
```

### 2. Stripe Customers

```typescript
export const stripeCustomers = pgTable('stripe_customers', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  stripeCustomerId: text('stripe_customer_id').notNull().unique(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

### 3. Agent Stripe Info

```typescript
export const agentStripeInfo = pgTable('agent_stripe_info', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  agentId: uuid('agent_id').notNull().references(() => agents.id, { onDelete: 'cascade' }),
  stripeProductId: text('stripe_product_id').notNull(),
  stripePriceId: text('stripe_price_id').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

## Subscription Management

### 1. Creating Subscriptions

When a user subscribes to an agent, the platform:

1. Creates a Stripe checkout session
2. Redirects the user to the Stripe checkout page
3. Receives a webhook notification when the checkout is completed
4. Creates a subscription record in the database

### 2. Updating Subscriptions

When a subscription is updated, the platform:

1. Receives a webhook notification from Stripe
2. Updates the subscription record in the database
3. Updates access permissions accordingly

### 3. Cancelling Subscriptions

When a subscription is cancelled, the platform:

1. Cancels the subscription in Stripe
2. Updates the subscription record in the database
3. Sets the cancellation date and end date
4. Maintains access until the end of the billing period

## Payment Processing

### 1. Processing Payments

The platform uses Stripe Checkout for payment processing:

1. User initiates checkout
2. Stripe handles payment collection
3. Platform receives webhook notification
4. Platform updates subscription status

### 2. Handling Failed Payments

The platform handles failed payments through Stripe's automatic retry system:

1. Stripe attempts to charge the payment method
2. If payment fails, Stripe sends a webhook notification
3. Platform updates subscription status
4. Stripe automatically retries according to the configured schedule
5. If all retries fail, the subscription is cancelled

## Security Considerations

### 1. API Key Security

The platform secures Stripe API keys:

1. Secret key is stored in environment variables
2. Secret key is never exposed to clients
3. Publishable key is used for client-side operations

### 2. Webhook Security

The platform secures webhook endpoints:

1. Webhook signature verification
2. HTTPS-only communication
3. Idempotency handling to prevent duplicate processing

### 3. PCI Compliance

The platform maintains PCI compliance:

1. No card data is stored on the platform
2. All payment processing is handled by Stripe
3. Stripe Elements or Checkout is used for card collection

## Testing

### 1. Test Mode

The platform supports Stripe test mode:

1. Separate API keys for test and production
2. Test cards for simulating payments
3. Test webhooks for simulating events

### 2. Webhook Testing

The platform supports webhook testing:

1. Stripe CLI for local webhook testing
2. Test events for simulating webhooks
3. Event logging for debugging

## Error Handling

### 1. API Error Handling

The platform handles Stripe API errors:

1. Try-catch blocks around all Stripe API calls
2. Error logging with detailed information
3. User-friendly error messages
4. Retry logic for transient errors

### 2. Webhook Error Handling

The platform handles webhook errors:

1. Signature verification errors
2. Event processing errors
3. Database transaction errors
4. Idempotency to prevent duplicate processing

## Reporting and Analytics

### 1. Revenue Reporting

The platform provides revenue reporting:

1. Total revenue by period
2. Revenue by agent
3. Revenue by creator
4. Subscription growth and churn

### 2. Payment Analytics

The platform provides payment analytics:

1. Payment success rate
2. Failed payment analysis
3. Subscription lifetime value
4. Churn analysis

## Stripe Dashboard Integration

The platform integrates with the Stripe Dashboard:

1. Links to Stripe Dashboard for detailed reporting
2. Consistent metadata for easy filtering
3. Clear product and price naming conventions
4. Organized customer data

## Future Enhancements

Planned enhancements to the Stripe integration:

1. Support for additional payment methods
2. Tax calculation and collection
3. Enhanced subscription management
4. Improved analytics and reporting
5. Stripe Connect for direct creator payouts
