import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { useSubscription } from '@/context/subscription-context';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { formatNumber } from '@/lib/utils';
import {
  Check,
  Clock,
  Zap,
  MoreVertical,
  ChevronDown,
  ChevronUp,
  Star,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ProBadge } from './pro-badge';
import { PreviewBadge } from './preview-badge';
import { PaymentBadge } from './payment-badge';
import { ShimmerButton } from './ui/shimmer-button';
import { Button } from './ui/button';
import dynamic from 'next/dynamic';
import { format } from 'date-fns';
import { allChatModels } from '@/lib/ai/models';
import { getModelWithFallback } from '@/lib/ai/model-registry';
import SubscriptionCancelModal from './subscription-cancel-modal';

// Dynamically import the SubscriptionModal to avoid circular dependencies
const SubscriptionModal = dynamic(() => import('./subscription-modal'), {
  ssr: false,
});

interface AgentProfile {
  id: string;
  agentId: string;
  rating: number;
  totalChats: number;
  datasets: number;
  model: string;
  features: string[];
  createdAt: string;
  updatedAt: string;
}

interface GreetingProps {
  agent?: any; // Agent data if available
  chatId?: string; // Chat ID if available
}

export const Greeting = ({ agent, chatId }: GreetingProps) => {
  const [showSubscribeModal, setShowSubscribeModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [agentProfile, setAgentProfile] = useState<AgentProfile | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const session = useSession();
  const currentUserId = session?.data?.user?.id;
  const {
    checkSubscription,
    isProForAgent,
    isLoading,
    isCanceled,
    getCancelDate,
  } = useSubscription();

  useEffect(() => {
    if (agent?.id && !isChecking) {
      setIsChecking(true);
      // Use the centralized subscription context
      checkSubscription(agent.id).then(() => {
        setIsChecking(false);
      });
    }
  }, [agent?.id, checkSubscription, isChecking]);

  // Fetch agent profile data
  useEffect(() => {
    const fetchAgentProfile = async () => {
      if (!agent?.id) return;

      setIsLoadingProfile(true);
      try {
        const response = await fetch(`/api/agents/profile?agentId=${agent.id}`);
        if (response.ok) {
          const data = await response.json();

          // Create a modified profile with the correct model from the agent object
          const profile = data.profile || data;

          // If the agent has a model and the profile has a different model, use the agent's model
          // This ensures consistency between what's in the database and what's displayed
          if (agent.model && profile.model !== agent.model) {
            profile.model = agent.model;
          }

          setAgentProfile(profile);
        }
      } catch (error) {
        // Error handling without logging
      } finally {
        setIsLoadingProfile(false);
      }
    };

    // Fetch only once on mount
    fetchAgentProfile();
  }, [agent?.id]);

  // Fallback greeting when no agent is provided
  if (!agent) {
    return (
      <div className="flex mx-auto px-4 w-full md:max-w-3xl py-4">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-2 w-full"
        >
          <h2 className="text-2xl font-medium">Hello there!</h2>
          <p className="text-muted-foreground">How can I help you today?</p>
        </motion.div>
      </div>
    );
  }

  // Get logo URL from agent data
  const logoUrl = agent.logo || null;

  // Determine if agent is paid and extract price price
  const isPaid =
    agent.accessLevel === 'subscription' || agent.accessLevel === 'lifetime';
  const price = agent.price || 0;
  const isLifetime = agent.accessLevel === 'lifetime';
  const monthlyPrice = isLifetime ? (price / 12).toFixed(2) : price;

  // Extract name from email (part before @)
  const creatorName = agent.email
    ? agent.email.includes('@')
      ? agent.email.split('@')[0]
      : agent.email
    : null;

  const handleUpgrade = () => {
    setShowSubscribeModal(true);
  };

  // Handle opening the cancellation modal
  const handleCancelSubscription = () => {
    if (!agent?.id) return;
    setShowCancelModal(true);
  };

  // Handle successful cancellation
  const handleCancellationSuccess = () => {
    // Force UI update after cancellation
    if (agent?.id) {
      // Manually update the UI without triggering a re-fetch
      // This prevents the maximum update depth exceeded error
      setTimeout(() => {
        // Use a timeout to ensure the modal is fully closed first
        checkSubscription(agent.id);
      }, 100);
    }
  };

  // Calculate the break-even point for lifetime vs monthly subscription
  const breakEvenMonths = isLifetime ? Math.ceil(price / (price / 12)) : 12;

  // Use profile data or fallback to defaults if still loading or not available
  const rating = agentProfile?.rating || 0;
  // Use stable values for SSR to avoid hydration errors
  const totalChats = agentProfile?.totalChats || 0;
  const datasets = agentProfile?.datasets || 0;

  // Use model from database directly, with fallbacks
  const modelId = agentProfile?.model || agent.model || 'gpt-4';

  // Get the display name from the model definitions
  const getModelDisplayName = (modelId: string) => {
    // First try to find the model in allChatModels
    let modelDefinition = allChatModels.find((m) => m.id === modelId);

    // If not found, try to get it with fallback
    if (!modelDefinition) {
      const fallbackModel = getModelWithFallback(modelId);
      if (fallbackModel) {
        modelDefinition = allChatModels.find((m) => m.id === fallbackModel.id);
      }
    }

    if (modelDefinition) {
      // If the model is gpt-4o, always display "GPT-4o"
      if (modelDefinition.id === 'gpt-4o') {
        return 'GPT-4o';
      }
      return modelDefinition.name;
    }

    // Special case for chat-model
    if (modelId === 'chat-model') {
      return 'GPT-4o';
    }

    // Fallback to formatted model ID
    return modelId
      .toLowerCase()
      .replace('gpt-', 'GPT-')
      .replace('-turbo', ' Turbo')
      .replace('gpt4', 'GPT-4')
      .replace('gpt35', 'GPT-3.5')
      .replace('claude-', 'Claude ')
      .replace('gemini-', 'Gemini ')
      .replace('grok-', 'Grok ')
      .replace(/-/g, ' ')
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Only show stats that have meaningful values
  const hasRating = rating > 0;
  const hasChats = totalChats > 0;
  const hasDatasets = datasets > 0;
  const hasModel = !!modelId;
  const hasAnyStats = hasRating || hasChats || hasDatasets || hasModel;

  // Format creation date if available
  const createdAt = agent.createdAt
    ? format(new Date(agent.createdAt), 'MMM d, yyyy')
    : format(new Date(), 'MMM d, yyyy');

  // Format updated date
  const updatedAt = agent.updatedAt
    ? format(new Date(agent.updatedAt), 'MMM d, yyyy')
    : format(new Date(), 'MMM d, yyyy');

  const getStatsGridCols = () => {
    let count = 0;
    if (rating && rating !== 0) count++;
    if (totalChats > 0) count++;
    if (datasets > 0) count++;
    if (modelId) count++;
    return `grid-cols-${count}`;
  };

  return (
    <div className="flex mx-auto px-4 w-full md:max-w-3xl py-4">
      <motion.div
        className="flex flex-col w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        {/* Agent Info */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4 w-full"
        >
          {/* Agent Header */}
          {isLoadingProfile ? (
            <div className="flex items-start gap-4 mb-4">
              {/* Skeleton for agent logo */}
              <div className="h-16 w-16 rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse flex-shrink-0" />

              <div className="flex-1">
                {/* Skeleton for agent name */}
                <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
                {/* Skeleton for agent description */}
                <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mt-1" />
              </div>
            </div>
          ) : (
            <div className="flex items-start gap-3 md:gap-4 mb-4">
              {logoUrl ? (
                <div className="h-12 w-12 md:h-16 md:w-16 rounded-lg flex-shrink-0 flex items-center justify-center">
                  <Image
                    src={logoUrl}
                    alt={agent.agentName}
                    width={56}
                    height={56}
                    className="h-10 w-10 md:h-14 md:w-14 rounded-md object-cover"
                  />
                </div>
              ) : (
                <div className="h-12 w-12 md:h-16 md:w-16 bg-gradient-to-r from-orange-400 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-lg md:text-xl font-medium text-white">
                    {agent.agentName?.charAt(0)?.toUpperCase() || '?'}
                  </span>
                </div>
              )}
              <div className="flex-1">
                <div className="flex items-center">
                  <h1 className="text-lg md:text-xl font-semibold">
                    {agent.agentName}
                  </h1>
                </div>
                <p className="text-sm text-muted-foreground">
                  {creatorName && `by ${creatorName}`}
                </p>
                {agent.description && (
                  <div className="mt-1">
                    <div
                      className={`text-sm whitespace-pre-wrap ${!showFullDescription ? 'line-clamp-3' : ''}`}
                    >
                      {agent.description}
                    </div>
                    {agent.description.length > 180 && (
                      <button
                        type="button"
                        onClick={() =>
                          setShowFullDescription(!showFullDescription)
                        }
                        className="text-xs text-orange-500 hover:text-orange-600 mt-1 font-medium"
                      >
                        {showFullDescription ? 'Show less' : 'Read more'}
                      </button>
                    )}
                  </div>
                )}
              </div>

              {/* Add Shimmer Button for subscription */}
              {isPaid && !isProForAgent(agent.id) && !isLoading(agent.id) && (
                <div className="flex-shrink-0 self-center">
                  <ShimmerButton
                    shimmerColor="rgba(255, 255, 255, 0.5)"
                    shimmerSize="0.1em"
                    shimmerDuration="2s"
                    background="linear-gradient(90deg, #f97316, #ec4899)"
                    borderRadius="9999px"
                    className="text-xs md:text-sm h-8 md:h-9 font-semibold text-white px-3 md:px-4 w-auto"
                    onClick={handleUpgrade}
                  >
                    {isLifetime ? `$${price}` : `$${monthlyPrice}/mo`}
                  </ShimmerButton>
                </div>
              )}

              {/* Show Preview Badge for creators */}
              {!isProForAgent(agent.id) &&
                agent.userId === currentUserId &&
                !isLoading(agent.id) && (
                  <div className="flex-shrink-0 self-center">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-2">
                        <PreviewBadge
                          size="sm"
                          className="h-8 md:h-9 px-3 py-0 cursor-default pointer-events-none text-xs md:text-sm font-semibold"
                        />
                      </div>
                    </div>
                  </div>
                )}

              {/* Show Pro Badge when user has Pro access but is not the creator */}
              {isPaid && isProForAgent(agent.id) && !isLoading(agent.id) && (
                <div className="flex-shrink-0 self-center">
                  {/* Top row: Pro/Preview badge and 3 dots menu in a single row */}
                  <div className="flex items-center gap-2">
                    {/* Check if the agent was created by the current user */}
                    <ProBadge size="lg" className="px-4 py-1.5" />

                    {/* Show cancellation menu if not canceled and not the creator */}
                    {!isCanceled(agent.id) && (
                      /* Three-dot menu for Pro users - positioned on the same line as badge */
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 rounded-full ml-1"
                          >
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">Menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={handleCancelSubscription}
                            className="text-destructive focus:text-destructive"
                          >
                            Cancel Subscription
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>

                  {/* Bottom row: Cancellation status if canceled */}
                  {isCanceled(agent.id) && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                      <Clock className="h-3 w-3" />
                      <span>
                        Cancels{' '}
                        {getCancelDate(agent.id)
                          ? new Date(
                              getCancelDate(agent.id) as string,
                            ).toLocaleDateString()
                          : 'at period end'}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </motion.div>

        {/* Agent Stats */}
        {isLoadingProfile ? (
          <div className="border border-zinc-100 dark:border-zinc-800 rounded-lg p-4 mb-4">
            <div className="flex justify-between">
              {/* Skeleton for stats */}
              {[1, 2, 3, 4].map((i) => (
                <div
                  key={`skeleton-${i}`}
                  className="flex flex-col items-center justify-center"
                >
                  <div className="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
                  <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        ) : hasAnyStats ? (
          <div className="flex flex-row justify-between border border-zinc-100 dark:border-zinc-800 rounded-lg p-4 mb-4">
            {/* Only show rating if it's not zero */}
            {hasRating && (
              <div className="flex flex-col items-center justify-center">
                <div className="flex items-center gap-1">
                  <div className="text-lg font-semibold">{rating}</div>
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                </div>
                <div className="text-xs text-muted-foreground mt-1">Rating</div>
              </div>
            )}

            {hasChats && (
              <div className="flex flex-col items-center justify-center">
                <div className="text-lg font-semibold">
                  {formatNumber(totalChats)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  <div>Chats</div>
                </div>
              </div>
            )}

            {hasDatasets && (
              <div className="flex flex-col items-center justify-center">
                <div className="text-lg font-semibold">
                  {formatNumber(datasets)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Datasets
                </div>
              </div>
            )}

            {hasModel && (
              <div className="flex flex-col items-center justify-center">
                <div className="flex items-center gap-1 text-lg font-semibold">
                  {modelId.toLowerCase().includes('gpt') && (
                    <Image
                      src="/images/chatgpt.svg"
                      alt="ChatGPT"
                      width={16}
                      height={16}
                    />
                  )}
                  {modelId.toLowerCase().includes('claude') && (
                    <Image
                      src="/images/claude.svg"
                      alt="Claude"
                      width={16}
                      height={16}
                    />
                  )}
                  {modelId.toLowerCase().includes('gemini') && (
                    <Image
                      src="/images/gemini.png"
                      alt="Gemini"
                      width={16}
                      height={16}
                    />
                  )}
                  {modelId.toLowerCase().includes('grok') && (
                    <Image
                      src="/images/grok.png"
                      alt="Grok"
                      width={16}
                      height={16}
                    />
                  )}
                  {modelId.toLowerCase().includes('deepseek') && (
                    <Image
                      src="/images/deepseek.png"
                      alt="DeepSeek"
                      width={16}
                      height={16}
                    />
                  )}
                  {!modelId.toLowerCase().includes('gpt') &&
                    !modelId.toLowerCase().includes('claude') &&
                    !modelId.toLowerCase().includes('gemini') &&
                    !modelId.toLowerCase().includes('grok') &&
                    !modelId.toLowerCase().includes('deepseek') && (
                      <div className="w-4 h-4 rounded-full bg-blue-500 mr-1 flex items-center justify-center text-white text-[8px] font-bold">
                        AI
                      </div>
                    )}
                  {getModelDisplayName(modelId)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  <div>Model</div>
                </div>
              </div>
            )}
          </div>
        ) : null}

        {/* Features Section - Show for all agents, including paid ones that the user has access to */}
        {((isPaid && isProForAgent(agent.id)) || !isPaid) && (
          <div className="border border-zinc-100 dark:border-zinc-800 rounded-lg p-4 mb-4">
            <h2 className="text-base md:text-lg font-semibold mb-3">
              Features
            </h2>

            {/* Agent Features from Database */}
            <ul className="space-y-2">
              {isLoadingProfile ? (
                // Show skeleton loader while loading
                <>
                  {[1, 2, 3].map((i) => (
                    <li key={i} className="flex items-start gap-2">
                      <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse" />
                    </li>
                  ))}
                </>
              ) : agentProfile?.features && agentProfile.features.length > 0 ? (
                // Show actual features from database
                agentProfile.features.map((feature, index) => (
                  <li
                    key={index}
                    className={`flex items-start gap-2 ${index >= 2 && !showAllFeatures ? 'hidden md:flex' : ''}`}
                  >
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">{feature}</span>
                  </li>
                ))
              ) : (
                // Fallback if no features are available after loading completes
                <>
                  <li className="flex items-start gap-2">
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">
                      Specialized {agent.agentName} expertise
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">
                      Access to premium content and insights
                    </span>
                  </li>
                  <li className="flex items-start gap-2 hidden md:flex">
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">
                      Regular knowledge updates
                    </span>
                  </li>
                </>
              )}
              {agentProfile?.features && agentProfile.features.length > 2 && (
                <li className="flex items-start gap-2 mt-2 md:hidden">
                  <button
                    type="button"
                    onClick={() => setShowAllFeatures(!showAllFeatures)}
                    className="text-xs text-orange-500 hover:text-orange-600 font-medium flex items-center gap-1"
                  >
                    {showAllFeatures ? (
                      <>
                        Show less <ChevronUp className="h-3 w-3" />
                      </>
                    ) : (
                      <>
                        View more features <ChevronDown className="h-3 w-3" />
                      </>
                    )}
                  </button>
                </li>
              )}
            </ul>
          </div>
        )}

        {/* Subscription Section - Only for paid agents when user is not pro */}
        {isPaid && !isProForAgent(agent.id) && !isLoading(agent.id) && (
          <div className="border border-zinc-100 dark:border-zinc-800 rounded-lg p-4 mb-4">
            <h2 className="text-base md:text-lg font-semibold mb-3">
              Why should you use it?
            </h2>

            {/* Agent Features from Database */}
            <ul className="space-y-2">
              {isLoadingProfile ? (
                // Show skeleton loader while loading
                <>
                  {[1, 2, 3].map((i) => (
                    <li key={i} className="flex items-start gap-2">
                      <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse" />
                    </li>
                  ))}
                </>
              ) : agentProfile?.features && agentProfile.features.length > 0 ? (
                // Show actual features from database
                agentProfile.features.map((feature, index) => (
                  <li
                    key={index}
                    className={`flex items-start gap-2 ${index >= 2 && !showAllFeatures ? 'hidden md:flex' : ''}`}
                  >
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">{feature}</span>
                  </li>
                ))
              ) : (
                // Fallback if no features are available after loading completes
                <>
                  <li className="flex items-start gap-2">
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">
                      Unlimited conversations with specialized {agent.agentName}{' '}
                      expertise
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">
                      Access to premium features and exclusive content
                    </span>
                  </li>
                  <li className="flex items-start gap-2 hidden md:flex">
                    <div className="mt-0.5 flex-shrink-0 h-4 w-4 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm md:text-base">
                      Priority support and regular knowledge updates
                    </span>
                  </li>
                </>
              )}
              {agentProfile?.features && agentProfile.features.length > 2 && (
                <li className="flex items-start gap-2 mt-2 md:hidden">
                  <button
                    type="button"
                    onClick={() => setShowAllFeatures(!showAllFeatures)}
                    className="text-xs text-orange-500 hover:text-orange-600 font-medium flex items-center gap-1"
                  >
                    {showAllFeatures ? (
                      <>
                        Show less <ChevronUp className="h-3 w-3" />
                      </>
                    ) : (
                      <>
                        View more features <ChevronDown className="h-3 w-3" />
                      </>
                    )}
                  </button>
                </li>
              )}
            </ul>

            <div className="mt-4">
              <p className="text-sm text-muted-foreground flex items-center gap-2">
                <span className="inline-flex items-center">
                  <Clock className="h-3.5 w-3.5 mr-1" />
                  <span>Created {createdAt}</span>
                </span>
              </p>
            </div>
          </div>
        )}
      </motion.div>

      {/* Subscription Modal */}
      {showSubscribeModal && (
        <SubscriptionModal
          agentId={agent.id}
          agentName={agent.agentName}
          chatId={chatId} // Pass chatId if available
          isOpen={showSubscribeModal}
          onClose={() => setShowSubscribeModal(false)}
        />
      )}

      {/* Cancellation Confirmation Modal */}
      {showCancelModal && (
        <SubscriptionCancelModal
          isOpen={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          agentId={agent.id}
          agentName={agent.agentName}
          onSuccess={handleCancellationSuccess}
        />
      )}
    </div>
  );
};
