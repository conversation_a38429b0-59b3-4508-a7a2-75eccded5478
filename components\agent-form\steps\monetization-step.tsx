'use client';

import React, { useEffect, useState } from 'react';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import type { UseFormReturn } from 'react-hook-form';
import type { FormValues } from '../types';
import { Input } from '@/components/ui/input';
import { DollarSign, Sparkles, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface MonetizationStepProps {
  form: UseFormReturn<FormValues, any, FormValues>;
  updateValidity?: (valid: boolean) => void;
  isEditMode?: boolean;
  isActiveAgent?: boolean;
}

export function MonetizationStep({
  form,
  updateValidity,
  isEditMode = false,
  isActiveAgent = false,
}: MonetizationStepProps) {
  const [isValid, setIsValid] = useState(true);

  // Watch for changes in the form values
  const isPaid = form.watch('isPaid');
  const price = form.watch('price') as string | undefined;

  // Validate the form when values change
  useEffect(() => {
    let valid = true;

    // If paid, validate price is a positive number
    if (isPaid) {
      const priceValue = price || '0';
      if (
        Number.isNaN(Number.parseFloat(priceValue)) ||
        Number.parseFloat(priceValue) <= 0
      ) {
        valid = false;
      }
    }

    setIsValid(valid);

    // Notify parent component about validity
    if (updateValidity) {
      updateValidity(valid);
    }
  }, [isPaid, price, updateValidity]);

  // Handle pricing type change
  const handleIsPaidChange = (checked: boolean) => {
    form.setValue('isPaid', checked);
    if (!checked) {
      form.setValue('price', '0');
      form.setValue('accessLevel', 'free');
    } else {
      // Always set to subscription since we're removing lifetime option
      form.setValue('pricingType', 'subscription');
      form.setValue('accessLevel', 'subscription');
    }
  };

  return (
    <div className="space-y-8">
      <div className="space-y-6">
        {/* <h3 className="text-base font-medium">Agent Monetization</h3> */}
        {/* <p className="text-sm text-muted-foreground">
          Choose whether your agent will be free or paid
        </p> */}

        <Alert className="bg-primary/5 border border-primary/20">
          <Info className="h-4 w-4 text-primary" />
          <AlertDescription className="text-sm">
            <span className="font-medium">Important:</span> {isEditMode && isActiveAgent 
              ? "Pricing settings cannot be changed for active agents to ensure consistency for existing users."
              : "Once your agent is active, pricing settings cannot be changed. Make sure you're satisfied with your choice before launching your agent."
            }
          </AlertDescription>
        </Alert>

        <div className="space-y-6 mt-4">
          {/* Pricing Selection */}
          <FormField
            control={form.control}
            name="isPaid"
            render={({ field }) => (
              <FormItem>
                <div className="flex flex-col space-y-2">
                  <FormLabel className="text-sm font-medium">
                    Pricing
                  </FormLabel>

                  <div
                    className={cn(
                      'flex rounded-md overflow-hidden border border-input',
                    )}
                  >
                    <button
                      type="button"
                      className={cn(
                        'flex-1 py-3 px-4 flex items-center justify-center gap-2 transition-all duration-200',
                        !field.value
                          ? 'bg-primary/10 border-r border-input'
                          : 'hover:bg-muted',
                        (isEditMode && isActiveAgent) && 'opacity-70 cursor-not-allowed',
                      )}
                      onClick={() => !(isEditMode && isActiveAgent) && handleIsPaidChange(false)}
                      aria-pressed={!field.value}
                      disabled={isEditMode && isActiveAgent}
                    >
                      <Sparkles className="h-4 w-4" />
                      <span className="text-sm font-medium">Free</span>
                    </button>
                    <button
                      type="button"
                      className={cn(
                        'flex-1 py-3 px-4 flex items-center justify-center gap-2 transition-all duration-200',
                        field.value ? 'bg-primary/10' : 'hover:bg-muted',
                        (isEditMode && isActiveAgent) && 'opacity-70 cursor-not-allowed',
                      )}
                      onClick={() => !(isEditMode && isActiveAgent) && handleIsPaidChange(true)}
                      aria-pressed={field.value}
                      disabled={isEditMode && isActiveAgent}
                    >
                      <DollarSign className="h-4 w-4" />
                      <span className="text-sm font-medium">Paid</span>
                    </button>
                  </div>
                </div>
              </FormItem>
            )}
          />

          {/* Only show pricing options if isPaid is true */}
          {isPaid && (
            <div className="space-y-4">
              {/* Hidden field to maintain subscription type */}
              <input
                type="hidden"
                {...form.register('pricingType')}
                value="subscription"
              />

              {/* Price Input */}
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-col space-y-2">
                      <FormLabel className="text-sm font-medium">
                        Subscription Price
                      </FormLabel>
                      <FormControl>
                        <div className="relative flex items-center">
                          <span className="absolute left-3 text-gray-500 text-sm">
                            USD
                          </span>
                          <Input
                            type="number"
                            min="0.99"
                            step="0.01"
                            placeholder="0.00"
                            className="pl-12 pr-20"
                            disabled={isEditMode && isActiveAgent}
                            {...field}
                          />
                          <span className="absolute right-3 text-gray-500 text-sm">
                            /month
                          </span>
                        </div>
                      </FormControl>
                      {/* <FormDescription>
                        Monthly subscription price in USD. Minimum price is
                        $0.99/month.
                      </FormDescription> */}
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
