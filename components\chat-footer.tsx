'use client';

import { memo } from 'react';
import { useSubscription } from '@/context/subscription-context';
import { ShimmerButton } from './ui/shimmer-button';
import type { Agent } from '@/lib/db/schema';

interface ChatFooterProps {
  agent?: Agent;
  remainingMessages?: number;
  onUpgrade?: () => void;
  isLoading?: boolean;
}

function PureChatFooter({
  agent,
  remainingMessages,
  onUpgrade,
  isLoading = false,
}: ChatFooterProps) {
  const { isProForAgent } = useSubscription();

  // Only show remaining messages if:
  // 1. We have an agent
  // 2. Remaining messages is defined and not infinite
  // 3. User doesn't have pro access for this agent
  // 4. Agent is not a free agent (accessLevel !== 'free')
  const showRemainingMessages =
    agent?.id &&
    remainingMessages !== undefined &&
    remainingMessages !== Number.POSITIVE_INFINITY &&
    !isProForAgent(agent.id) &&
    agent.accessLevel !== 'free';
  
  // For logged-out users or during initial load, we'll use a default value of 0
  const displayRemainingMessages = remainingMessages ?? 0;

  return (
    <footer className="flex items-center justify-between px-2 py-0.5 text-[10px] text-muted-foreground">
      <div className="flex items-center">
        <span className="hidden sm:inline mr-0.5">Powered by</span>
        <a
          href="https://buildthatidea.com"
          target="_blank"
          rel="noopener noreferrer"
          className="bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text text-transparent font-bold tracking-tight hover:opacity-90 transition-opacity"
        >
          {/* Show shorter text on mobile */}
          <span className="sm:hidden">BuildThatIdea</span>
          <span className="hidden sm:inline">BuildThatIdea.com</span>
        </a>
      </div>

      {showRemainingMessages && (
        <div className="flex items-center gap-1 whitespace-nowrap">
          <span className="hidden xs:inline">
            {displayRemainingMessages} free{' '}
            {displayRemainingMessages === 1 ? 'message' : 'messages'}
          </span>
          <span className="xs:hidden">{displayRemainingMessages} free messages</span>
          {onUpgrade && (
            <ShimmerButton
              type="button"
              onClick={onUpgrade}
              disabled={isLoading}
              className="h-5 text-[10px] px-1.5 text-white whitespace-nowrap"
            >
              Upgrade
            </ShimmerButton>
          )}
        </div>
      )}
    </footer>
  );
}

export const ChatFooter = memo(PureChatFooter);
