import { Index } from '@upstash/vector';
import { getAgentById } from '@/lib/db/queries';
import { decrypt } from '@/lib/utils';

// Define type for the metadata stored with each vector
type ChunkMetadata = {
  agentId: string;
  content: string;
  fileId?: string;
  chunkIndex?: string;
};

interface VectorResult {
  id: string;
  score: number;
  metadata: ChunkMetadata;
}

// Cache for vector indices to avoid creating new instances for the same agent
const vectorIndexCache = new Map<string, Index<ChunkMetadata>>();

/**
 * Get or create a vector index for a specific agent
 */
async function getVectorIndex(agentId: string): Promise<Index<ChunkMetadata>> {
  // Check if we already have this index in the cache
  if (vectorIndexCache.has(agentId)) {
    const cachedIndex = vectorIndexCache.get(agentId);
    if (cachedIndex) {
      return cachedIndex;
    }
  }

  // Get agent information from the database
  const agent = await getAgentById({ agentId });

  if (!agent || !agent.endpoint || !agent.token) {
    throw new Error(`Agent ${agentId} has no vector index configured`);
  }

  // Decrypt the token
  const decryptedToken = decrypt(agent.token);

  // Ensure the endpoint has the proper URL format with protocol
  let formattedEndpoint = agent.endpoint;
  if (formattedEndpoint && !formattedEndpoint.startsWith('http')) {
    formattedEndpoint = `https://${formattedEndpoint}`;
  }

  console.log(
    `Creating vector index for agent ${agentId} with endpoint ${formattedEndpoint}`,
  );

  // Create a new vector index instance
  const vectorIndex = new Index<ChunkMetadata>({
    url: formattedEndpoint,
    token: decryptedToken,
  });

  // Cache the index for future use
  vectorIndexCache.set(agentId, vectorIndex);

  return vectorIndex;
}

/**
 * Retrieve relevant content from the agent's vector index
 */
export async function retrieveRelevantContent(
  query: string,
  agentId: string,
  limit = 5,
  similarityThreshold = 0.5,
): Promise<string[]> {
  try {
    console.log('=== VECTOR RETRIEVAL STARTED ===');
    console.log(
      `Query: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`,
    );
    console.log(`Agent ID: ${agentId}`);
    console.log(
      `Limit: ${limit}, Similarity threshold: ${similarityThreshold}`,
    );

    // Get the vector index for this agent
    const vectorIndex = await getVectorIndex(agentId);
    console.log('Vector index retrieved successfully');

    // Query Upstash Vector using the query text directly
    // Let Upstash handle the embedding generation
    console.log('Querying vector index...');
    const results = await vectorIndex.query({
      data: query, // Use 'data' field for Upstash to generate the embedding
      topK: limit,
      includeVectors: false,
      includeMetadata: true,
    });
    console.log(`Raw results received: ${results.length}`);

    // Filter by similarity threshold and sort
    const filteredResults = (results as VectorResult[])
      .filter((result) => result.score >= similarityThreshold)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    console.log(
      `Found ${filteredResults.length} relevant chunks after filtering`,
    );

    // Log similarity scores
    filteredResults.forEach((result, index) => {
      console.log(`Result ${index + 1} - Score: ${result.score.toFixed(4)}`);
    });

    console.log('=== VECTOR RETRIEVAL COMPLETED ===');

    // Extract content with type safety
    return filteredResults.map(
      (result) => result.metadata?.content ?? 'Content not available',
    );
  } catch (error) {
    console.error('=== VECTOR RETRIEVAL ERROR ===');
    console.error(
      `Query: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`,
    );
    console.error(`Agent ID: ${agentId}`);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    console.error('==============================');
    throw new Error('Failed to retrieve relevant content');
  }
}
