'use client';

import { useEffect } from 'react';
import { useSubscription } from '@/context/subscription-context';

interface SubscriptionDataUpdaterProps {
  bootstrapData?: {
    subscription?: {
      isPaid: boolean;
      isCanceled?: boolean;
      cancelDate?: string;
      expiryDate?: string;
      remainingMessages: number;
    };
    agent?: { id: string };
  };
}

/**
 * A component that syncs bootstrap data with the global subscription context
 * This allows us to hydrate the subscription context with server-fetched data
 * without modifying the global SubscriptionProvider
 */
export function SubscriptionDataUpdater({
  bootstrapData,
}: SubscriptionDataUpdaterProps) {
  const { checkSubscription } = useSubscription();

  // When bootstrap data is available, trigger a check with this data
  // The checkSubscription method is patched to use initialData when available
  useEffect(() => {
    if (bootstrapData?.subscription && bootstrapData?.agent?.id) {
      const agentId = bootstrapData.agent.id;

      // Trigger checkSubscription which will use the bootstrap data
      // thanks to our modifications in subscription-context.tsx
      checkSubscription(agentId);
    }
  }, [bootstrapData, checkSubscription]);

  // This component doesn't render anything
  return null;
}
