-- Create enum type for subscription_type
CREATE TYPE subscription_type AS ENUM ('trial', 'monthly', 'yearly');

-- Create CreatorSubscriptions table
CREATE TABLE IF NOT EXISTS "CreatorSubscriptions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL REFERENCES "User"("id") ON DELETE CASCADE,
    "planId" UUID NOT NULL REFERENCES "CreatorPlans"("id") ON DELETE CASCADE,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "subscription_type" subscription_type NOT NULL DEFAULT 'monthly',
    "expiry_date" DATE,
    "amount_paid" FLOAT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
