/**
 * ConvertKit API utilities for managing subscribers and tags
 */

/**
 * Add a tag to a subscriber in ConvertKit
 * @param email - The subscriber's email address
 * @param tagId - The ConvertKit tag ID to apply
 * @returns Promise with the result of the operation
 */
export async function addTagToSubscriber(email: string, tagId: string): Promise<{ success: boolean; message: string }> {
  try {
    // Skip actual API call in non-production environments
    if (!isProduction) {
      console.log(`[ConvertKit Mock] Would add tag ${tagId} to subscriber ${email} in production`);
      return { 
        success: true, 
        message: 'Development mode - no actual API call made' 
      };
    }

    const CONVERTKIT_API_SECRET = process.env.CONVERTKIT_API_SECRET;
    
    if (!CONVERTKIT_API_SECRET) {
      console.error('ConvertKit API secret is not configured');
      return { 
        success: false, 
        message: 'Email service not fully configured' 
      };
    }
    
    // Add the tag to the subscriber
    const response = await fetch(`https://api.convertkit.com/v3/tags/${tagId}/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_secret: CONVERTKIT_API_SECRET,
        email: email
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Failed to add tag ${tagId} to subscriber ${email}. Status: ${response.status}`, errorText);
      return { 
        success: false, 
        message: `Failed to add tag: ${errorText}` 
      };
    }
    
    const data = await response.json();
    return { 
      success: true, 
      message: 'Tag added successfully' 
    };
  } catch (error) {
    console.error('Error adding tag to subscriber:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Remove a tag from a subscriber in ConvertKit
 * @param email - The subscriber's email address
 * @param tagId - The ConvertKit tag ID to remove
 * @returns Promise with the result of the operation
 */
export async function removeTagFromSubscriber(email: string, tagId: string): Promise<{ success: boolean; message: string }> {
  try {
    // Skip actual API call in non-production environments
    if (!isProduction) {
      console.log(`[ConvertKit Mock] Would remove tag ${tagId} from subscriber ${email} in production`);
      return { 
        success: true, 
        message: 'Development mode - no actual API call made' 
      };
    }

    const CONVERTKIT_API_SECRET = process.env.CONVERTKIT_API_SECRET;
    
    if (!CONVERTKIT_API_SECRET) {
      console.error('ConvertKit API secret is not configured');
      return { 
        success: false, 
        message: 'Email service not fully configured' 
      };
    }
    
    // First, find the subscriber ID
    const subscriberResponse = await fetch(
      `https://api.convertkit.com/v3/subscribers?api_secret=${CONVERTKIT_API_SECRET}&email_address=${encodeURIComponent(email)}`
    );
    
    if (!subscriberResponse.ok) {
      const errorText = await subscriberResponse.text();
      console.error(`Failed to find subscriber ${email}. Status: ${subscriberResponse.status}`, errorText);
      return { 
        success: false, 
        message: `Failed to find subscriber: ${errorText}` 
      };
    }
    
    const subscriberData = await subscriberResponse.json();
    
    if (!subscriberData.subscribers || subscriberData.subscribers.length === 0) {
      return { 
        success: false, 
        message: 'Subscriber not found' 
      };
    }
    
    const subscriberId = subscriberData.subscribers[0].id;
    
    // Remove the tag from the subscriber
    const response = await fetch(`https://api.convertkit.com/v3/subscribers/${subscriberId}/tags/${tagId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_secret: CONVERTKIT_API_SECRET
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Failed to remove tag ${tagId} from subscriber ${email}. Status: ${response.status}`, errorText);
      return { 
        success: false, 
        message: `Failed to remove tag: ${errorText}` 
      };
    }
    
    return { 
      success: true, 
      message: 'Tag removed successfully' 
    };
  } catch (error) {
    console.error('Error removing tag from subscriber:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Create or update a subscriber in ConvertKit
 * @param email - The subscriber's email address
 * @param firstName - Optional first name
 * @param fields - Optional custom fields
 * @returns Promise with the result of the operation
 */
export async function createOrUpdateSubscriber(
  email: string, 
  firstName?: string,
  fields?: Record<string, string>
): Promise<{ success: boolean; message: string; subscriberId?: string }> {
  try {
    const CONVERTKIT_API_SECRET = process.env.CONVERTKIT_API_SECRET;
    
    if (!CONVERTKIT_API_SECRET) {
      console.error('ConvertKit API secret is not configured');
      return { 
        success: false, 
        message: 'Email service not fully configured' 
      };
    }
    
    // Check if subscriber exists
    const subscriberResponse = await fetch(
      `https://api.convertkit.com/v3/subscribers?api_secret=${CONVERTKIT_API_SECRET}&email_address=${encodeURIComponent(email)}`
    );
    
    const subscriberData = await subscriberResponse.json();
    
    if (!subscriberData.subscribers || subscriberData.subscribers.length === 0) {
      // Create new subscriber
      const createResponse = await fetch('https://api.convertkit.com/v3/subscribers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          api_secret: CONVERTKIT_API_SECRET,
          email: email,
          first_name: firstName || email.split('@')[0],
          fields: fields || {}
        })
      });
      
      if (!createResponse.ok) {
        const errorText = await createResponse.text();
        console.error(`Failed to create subscriber ${email}. Status: ${createResponse.status}`, errorText);
        return { 
          success: false, 
          message: `Failed to create subscriber: ${errorText}` 
        };
      }
      
      const createData = await createResponse.json();
      return { 
        success: true, 
        message: 'Subscriber created successfully',
        subscriberId: createData.subscriber.id
      };
    } else {
      // Update existing subscriber
      const subscriberId = subscriberData.subscribers[0].id;
      
      if (firstName || fields) {
        const updateResponse = await fetch(`https://api.convertkit.com/v3/subscribers/${subscriberId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            api_secret: CONVERTKIT_API_SECRET,
            first_name: firstName,
            fields: fields || {}
          })
        });
        
        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error(`Failed to update subscriber ${email}. Status: ${updateResponse.status}`, errorText);
          return { 
            success: false, 
            message: `Failed to update subscriber: ${errorText}`,
            subscriberId
          };
        }
      }
      
      return { 
        success: true, 
        message: 'Subscriber already exists',
        subscriberId
      };
    }
  } catch (error) {
    console.error('Error creating or updating subscriber:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Tag constants for different user journey points
 * These match the actual tag IDs from ConvertKit
 */
export const CONVERTKIT_TAGS = {
  // User status tags
  USER: process.env.CONVERTKIT_TAG_USER,
  TRIAL: process.env.CONVERTKIT_TAG_TRIAL,
  HOBBY: process.env.CONVERTKIT_TAG_HOBBY,
  PRO: process.env.CONVERTKIT_TAG_PRO,
  CANCELLED: process.env.CONVERTKIT_TAG_CANCELLED,
  
  // Agent status tags
  HAS_DRAFT_AGENT: process.env.CONVERTKIT_TAG_HAS_DRAFT_AGENT,
};

// Debug log to check if the tags are loaded correctly
console.log('[ConvertKit Debug] Tag constants loaded:', {
  USER: CONVERTKIT_TAGS.USER,
  TRIAL: CONVERTKIT_TAGS.TRIAL,
  HOBBY: CONVERTKIT_TAGS.HOBBY,
  PRO: CONVERTKIT_TAGS.PRO,
  CANCELLED: CONVERTKIT_TAGS.CANCELLED,
  HAS_DRAFT_AGENT: CONVERTKIT_TAGS.HAS_DRAFT_AGENT,
});

// Check if we're in production environment
const isProduction = process.env.NEXT_PUBLIC_VERCEL_ENV === 'production' || 
                    process.env.NODE_ENV === 'production' ||
                    process.env.VERCEL_ENV === 'production' ||
                    process.env.NEXT_PUBLIC_SITE_URL?.includes('app.buildthatidea.com');

console.log(`[ConvertKit] Running in ${isProduction ? 'PRODUCTION' : 'DEVELOPMENT'} mode. ConvertKit operations will ${isProduction ? '' : 'NOT '}be performed.`);
