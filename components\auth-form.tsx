import React, { useEffect, useState } from 'react';

import { Input } from './ui/input';
import { Label } from './ui/label';

export function AuthForm({
  action,
  children,
  defaultEmail = '',
  className = '',
}: {
  action: NonNullable<
    string | ((formData: FormData) => void | Promise<void>) | undefined
  >;
  children: React.ReactNode;
  defaultEmail?: string;
  className?: string;
}) {
  // Use client-side only rendering for the form to avoid hydration errors with password managers
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  if (!isClient) {
    return (
      <div className={`flex flex-col gap-4 px-4 sm:px-16 ${className}`}>
        <div className="flex flex-col gap-2 min-h-[76px]">
          <div className="h-5 animate-pulse bg-muted rounded w-24 mb-2"></div>
          <div className="h-10 animate-pulse bg-muted rounded"></div>
        </div>
        <div className="flex flex-col gap-2 min-h-[76px]">
          <div className="h-5 animate-pulse bg-muted rounded w-20 mb-2"></div>
          <div className="h-10 animate-pulse bg-muted rounded"></div>
        </div>
        <div className="h-10 animate-pulse bg-primary/20 rounded mt-2"></div>
      </div>
    );
  }

  return (
    <form action={action} className={`flex flex-col gap-4 px-4 sm:px-16 ${className}`}>
      <div className="flex flex-col gap-2">
        <Label
          htmlFor="email"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          Email Address
        </Label>

        <Input
          id="email"
          name="email"
          className="bg-muted text-md md:text-sm"
          type="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          required
          autoFocus
          defaultValue={defaultEmail}
        />
      </div>

      <div className="flex flex-col gap-2">
        <Label
          htmlFor="password"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          Password
        </Label>

        <Input
          id="password"
          name="password"
          className="bg-muted text-md md:text-sm"
          type="password"
          required
          minLength={6}
        />
        <p className="text-xs text-muted-foreground mt-1">
          Password must be at least 6 characters long
        </p>
      </div>

      {children}
      
      <div className="flex justify-center mt-2">
        <a 
          href="/forgot-password" 
          className="text-sm text-primary hover:underline"
        >
          Forgot password?
        </a>
      </div>
    </form>
  );
}
