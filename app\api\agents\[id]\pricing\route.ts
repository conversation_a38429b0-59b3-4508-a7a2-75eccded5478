import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { agents } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const agentId = params.id;
    
    // Fetch agent from database
    const [agent] = await db
      .select()
      .from(agents)
      .where(eq(agents.id, agentId));
    
    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }
    
    // Extract pricing from direct price field
    let monthlyPrice = 4.99; // Default price
    
    // If agent has a price, use it
    if (agent.price !== null && agent.price !== undefined) {
      monthlyPrice = agent.price;
    }
    
    // Calculate yearly price (20% discount)
    const yearlyPrice = (monthlyPrice * 12 * 0.8).toFixed(2);
    
    return NextResponse.json({
      monthly: monthlyPrice.toFixed(2),
      yearly: yearlyPrice,
      accessLevel: agent.accessLevel,
      price: agent.price || 0 // Include the raw price from the database
    });
  } catch (error) {
    console.error('Error fetching agent pricing:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pricing information' },
      { status: 500 }
    );
  }
}
