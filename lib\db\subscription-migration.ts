import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  console.error('❌ POSTGRES_URL is not defined in environment variables');
  process.exit(1);
}

// Create database client after environment variables are loaded and
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

async function runSubscriptionMigration() {
  try {
    console.log('⏳ Starting migration: Adding fields to UserSubscriptions table');
    // biome-ignore lint: Forbidden non-null assertion.
    console.log(`Using database URL: ${process.env.POSTGRES_URL!.split('@')[1]}`); // Log only the host part for security
    
    // Add is_paid field to UserSubscriptions table
    await db.execute(sql`ALTER TABLE "UserSubscriptions" ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT false`);
    console.log('✅ Added is_paid column to UserSubscriptions table');

    // Add created_at field to UserSubscriptions table
    await db.execute(sql`ALTER TABLE "UserSubscriptions" ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT now()`);
    console.log('✅ Added created_at column to UserSubscriptions table');

    // Add updated_at field to UserSubscriptions table
    await db.execute(sql`ALTER TABLE "UserSubscriptions" ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT now()`);
    console.log('✅ Added updated_at column to UserSubscriptions table');

    console.log('🎉 Migration completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
    process.exit(0);
  }
}

// Run the migration function
runSubscriptionMigration().then(() => {
  console.log('✅ Subscription migration process completed');
});
