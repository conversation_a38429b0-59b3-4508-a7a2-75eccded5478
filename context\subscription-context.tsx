'use client';

import React, {
  createContext,
  useContext,
  useState,
  type ReactNode,
  useRef,
  useCallback,
  useEffect,
} from 'react';

import { useSession } from 'next-auth/react';
import { registerContextReset } from '@/lib/reset-app-state';

interface SubscriptionStatus {
  isPro: boolean;
  isLoading: boolean;
  remainingMessages: number;
  expiryDate?: string;
  isCanceled?: boolean;
  cancelDate?: string;
}

interface SubscriptionContextType {
  checkSubscription: (agentId: string) => Promise<boolean>;
  isProForAgent: (agentId: string) => boolean;
  isLoading: (agentId: string) => boolean;
  decrementMessages: (agentId: string) => void;
  getRemainingMessages: (agentId: string) => number;
  isCanceled: (agentId: string) => boolean;
  getCancelDate: (agentId: string) => string | undefined;
  resetSubscriptionState: () => void;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(
  undefined,
);

interface SubscriptionProviderProps {
  children: ReactNode;
  initialData?: {
    subscription?: {
      isPaid: boolean;
      isCanceled?: boolean;
      cancelDate?: string;
      expiryDate?: string;
      remainingMessages: number;
    };
    agent?: { id: string };
  };
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({
  children,
  initialData,
}) => {
  const { data: session } = useSession();
  const [subscriptionStatus, setSubscriptionStatus] = useState<
    Record<string, SubscriptionStatus>
  >(() => {
    // Initialize with initialData if available
    if (initialData?.subscription && initialData?.agent?.id) {
      const agentId = initialData.agent.id;
      return {
        [agentId]: {
          isPro: initialData.subscription.isPaid,
          isLoading: false,
          remainingMessages: initialData.subscription.remainingMessages,
          expiryDate: initialData.subscription.expiryDate,
          isCanceled: initialData.subscription.isCanceled,
          cancelDate: initialData.subscription.cancelDate,
        }
      };
    }
    return {};
  });
  const checkedAgentsRef = useRef<Set<string>>(new Set());
  const checkingInProgressRef = useRef<Set<string>>(new Set());
  
  // Function to reset subscription state
  const resetSubscriptionState = useCallback(() => {
    // Reset state variables
    setSubscriptionStatus({});
    checkedAgentsRef.current.clear();
    checkingInProgressRef.current.clear();
    
    // Clear subscription data from sessionStorage
    if (typeof window !== 'undefined' && window.sessionStorage) {
      // Log what's in sessionStorage before clearing
      console.log('SessionStorage before subscription reset:', Object.keys(sessionStorage));
      
      // Find and remove all subscription-related items
      // Use a broader pattern to catch all possible subscription data
      const keysToRemove = [];
      
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (
          key.startsWith('subscription_') || 
          key.includes('subscription') || 
          key.includes('remaining') || 
          key.includes('messages')
        )) {
          keysToRemove.push(key);
        }
      }
      
      // Remove the identified keys
      keysToRemove.forEach(key => {
        console.log(`Removing subscription-related sessionStorage item: ${key}`);
        sessionStorage.removeItem(key);
      });
      
      console.log('SessionStorage after subscription reset:', Object.keys(sessionStorage));
    }
    
    console.log('Subscription context state reset');
  }, []);
  
  // Register the reset function
  useEffect(() => {
    registerContextReset(resetSubscriptionState);
  }, [resetSubscriptionState]);

  const checkSubscription = async (agentId: string): Promise<boolean> => {
    if (!agentId || !session?.user) {
      return false;
    }

    // If we already have subscription data from initialData and it matches the requested agentId
    if (initialData?.subscription && initialData?.agent?.id === agentId) {
      console.log('[SubscriptionContext] Using initialData for agent:', agentId);
      // Mark this agent as checked to prevent refetching
      checkedAgentsRef.current.add(agentId);
      return initialData.subscription.isPaid;
    }

    // Check if we have data in sessionStorage first
    const storageKey = `subscription_${agentId}`;
    const storedData =
      typeof window !== 'undefined' ? sessionStorage.getItem(storageKey) : null;

    // If we have stored data with cancellation info, use it immediately
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        // Only update if we don't already have this info to prevent infinite loops
        if (
          parsedData.isCanceled &&
          (!subscriptionStatus[agentId] ||
            !subscriptionStatus[agentId].isCanceled ||
            subscriptionStatus[agentId].cancelDate !== parsedData.cancelDate)
        ) {
          // Update the subscription status with the cancellation info
          setSubscriptionStatus((prev) => {
            // If we already have data for this agent, just update the cancellation info
            if (prev[agentId]) {
              return {
                ...prev,
                [agentId]: {
                  ...prev[agentId],
                  isCanceled: true,
                  cancelDate: parsedData.cancelDate,
                },
              };
            }

            // Otherwise create a new entry
            return {
              ...prev,
              [agentId]: {
                isPro: parsedData.isPro || false,
                isLoading: false,
                remainingMessages: parsedData.remainingMessages || 0,
                isCanceled: true,
                cancelDate: parsedData.cancelDate,
              },
            };
          });
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    // If we've already checked this agent in this session and it's not loading, return the cached result
    if (
      checkedAgentsRef.current.has(agentId) &&
      subscriptionStatus[agentId] &&
      !subscriptionStatus[agentId].isLoading
    ) {
      return subscriptionStatus[agentId].isPro;
    }

    // If we're already in the process of checking this agent, don't start another check
    if (checkingInProgressRef.current.has(agentId)) {
      return subscriptionStatus[agentId]?.isPro || false;
    }

    // Mark this agent as being checked
    checkingInProgressRef.current.add(agentId);

    try {
      // Skip the API call if we already have the data from initialData
      if (initialData?.subscription && initialData?.agent?.id === agentId) {
        console.log('[SubscriptionContext] Using initialData subscription data');
        const data = initialData.subscription;
        const isPro = !!data.isPaid;
        const remainingMessages = data.remainingMessages || 0;
        const isCanceled = data.isCanceled || false;
        const cancelDate = data.cancelDate;
        const expiryDate = data.expiryDate;

        // Update the subscription status in state
        setSubscriptionStatus((prev) => ({
          ...prev,
          [agentId]: {
            isPro,
            isLoading: false,
            remainingMessages,
            expiryDate,
            isCanceled,
            cancelDate,
          },
        }));

        // Also store in sessionStorage for future reference
        const storageData = {
          isPro,
          remainingMessages,
          isCanceled,
          cancelDate,
        };
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(
            `subscription_${agentId}`,
            JSON.stringify(storageData),
          );
        }

        // Mark this agent as checked for this session
        checkedAgentsRef.current.add(agentId);

        // Remove from in-progress set
        checkingInProgressRef.current.delete(agentId);

        return isPro;
      }

      // If no initialData or it doesn't match the agentId, make the API call
      console.log('[SubscriptionContext] Fetching bootstrap data for agent:', agentId);
      const response = await fetch(`/api/agents/${agentId}/bootstrap`);
      if (response.ok) {
        const bootstrapData = await response.json();
        const data = bootstrapData.subscription;

        const currentDate = new Date().toISOString();
        // If user has paid and either there's no expiry date or it hasn't expired yet
        const isPro =
          data.isPaid &&
          (!data.expiryDate || new Date(data.expiryDate) > new Date());

        // Check if subscription is canceled but not yet expired
        const isCanceled = data.isCanceled || false;
        const cancelDate = data.cancelDate;

        // Update the subscription status for this agent
        // Store subscription data in the context
        setSubscriptionStatus((prev) => ({
          ...prev,
          [agentId]: {
            isPro,
            isLoading: false,
            expiryDate: data.expiryDate,
            remainingMessages: data.remainingMessages || 0,
            isCanceled: data.isCanceled || false,
            cancelDate: data.cancelDate || undefined,
          },
        }));

        // Also store in sessionStorage for faster access in future
        const storageData = {
          isPro,
          remainingMessages: data.remainingMessages || 0,
          isCanceled: data.isCanceled || false,
          cancelDate: data.cancelDate || undefined,
        };

        sessionStorage.setItem(
          `subscription_${agentId}`,
          JSON.stringify(storageData),
        );

        // Mark this agent as checked for this session
        checkedAgentsRef.current.add(agentId);

        // Remove from in-progress set
        checkingInProgressRef.current.delete(agentId);

        return isPro;
      }

      // If response is not ok, handle as error
      throw new Error('Failed to fetch subscription status');
    } catch (error) {
      // Update loading state in case of error and remove from in-progress set
      setSubscriptionStatus((prev) => ({
        ...prev,
        [agentId]: {
          isPro: false,
          isLoading: false,
          remainingMessages: 0,
        },
      }));
      checkingInProgressRef.current.delete(agentId);

      return false;
    }
  };

  const isProForAgent = (agentId: string): boolean => {
    const status = subscriptionStatus[agentId];
    if (!status) return false;

    // Check if subscription has expired
    if (status.expiryDate) {
      const expiryDate = new Date(status.expiryDate);
      if (expiryDate <= new Date()) {
        return false;
      }
    }

    return status.isPro;
  };

  const isLoading = (agentId: string): boolean => {
    return subscriptionStatus[agentId]?.isLoading || false;
  };

  const decrementMessages = (agentId: string): void => {
    const status = subscriptionStatus[agentId];
    if (!status || status.isLoading) return;

    // Don't decrement if user is Pro and subscription hasn't expired
    if (isProForAgent(agentId)) return;

    const newRemainingMessages = Math.max(0, status.remainingMessages - 1);
    setSubscriptionStatus((prev) => ({
      ...prev,
      [agentId]: {
        ...prev[agentId],
        remainingMessages: newRemainingMessages,
      },
    }));

    // Update sessionStorage
    const storageData = JSON.parse(
      sessionStorage.getItem(`subscription_${agentId}`) || '{}',
    );
    storageData.remainingMessages = newRemainingMessages;
    sessionStorage.setItem(
      `subscription_${agentId}`,
      JSON.stringify(storageData),
    );
  };

  const getRemainingMessages = (agentId: string): number => {
    return subscriptionStatus[agentId]?.remainingMessages ?? 0;
  };

  const isCanceled = (agentId: string): boolean => {
    // First check the context state
    const canceled = subscriptionStatus[agentId]?.isCanceled ?? false;
    if (canceled) return true;

    // If not found in context, check sessionStorage
    if (typeof window !== 'undefined') {
      try {
        const storageKey = `subscription_${agentId}`;
        const storedData = sessionStorage.getItem(storageKey);
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          return parsedData.isCanceled === true;
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    return false;
  };

  const getCancelDate = (agentId: string): string | undefined => {
    // First check the context state
    const cancelDate = subscriptionStatus[agentId]?.cancelDate;
    if (cancelDate) return cancelDate;

    // If not found in context, check sessionStorage
    if (typeof window !== 'undefined') {
      try {
        const storageKey = `subscription_${agentId}`;
        const storedData = sessionStorage.getItem(storageKey);
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          return parsedData.cancelDate;
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    return undefined;
  };

  return (
    <SubscriptionContext.Provider
      value={{
        checkSubscription,
        isProForAgent,
        isLoading,
        decrementMessages,
        getRemainingMessages,
        isCanceled,
        getCancelDate,
        resetSubscriptionState,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = (): SubscriptionContextType => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error(
      'useSubscription must be used within a SubscriptionProvider',
    );
  }
  return context;
};
