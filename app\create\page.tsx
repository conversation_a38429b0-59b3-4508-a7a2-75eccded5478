'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { formSchema, type FormValues } from '@/components/agent-form/types';
import { useToast } from '@/components/ui/use-toast';
import { MultiStepForm } from '@/components/agent-form/multi-step-form';
import { BasicInfoStep } from '@/components/agent-form/steps/basic-info-step';
import { ConfigurationStep } from '@/components/agent-form/steps/configuration-step';
import { CapabilitiesStep } from '@/components/agent-form/steps/capabilities-step';
import { MonetizationStep } from '@/components/agent-form/steps/monetization-step';
import { User, Bot, Settings2, DollarSign } from 'lucide-react';
import { debounce } from 'lodash';

// Define the steps for our multi-step form
const formSteps = [
  {
    id: 'basic-info',
    title: 'Basic Information',
    description: 'Provide your agent details',
    icon: <User className="size-4" />,
  },
  {
    id: 'capabilities',
    title: 'Capabilities',
    description: 'Define what your agent can do',
    icon: <Bot className="size-4" />,
  },
  {
    id: 'configuration',
    title: 'Configuration',
    description: 'Configure model and behavior',
    icon: <Settings2 className="size-4" />,
  },
  {
    id: 'monetization',
    title: 'Monetization',
    description: 'Set pricing for your agent',
    icon: <DollarSign className="size-4" />,
  },
];

export default function CreatePage() {
  // State for step validity
  const [stepValidity, setStepValidity] = useState<boolean[]>([
    false,
    false,
    false,
    false,
  ]);
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();

  // Form state
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      agentName: '',
      slug: '',
      instruction: '',
      description: '',
      model: 'gpt-4o',
      isPublic: true,
      isPaid: false,
      pricingType: 'subscription',
      price: '0',
      accessLevel: 'free',
      quickMessages: [],
      visibility: 'public',
      files: [],
      websiteUrl: '',
      selectedLinks: [],
    },
  });

  // Slug validation state
  const [slugExists, setSlugExists] = useState(false);
  const [isOwnDraft, setIsOwnDraft] = useState(false);

  // Logo preview
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingDraft, setIsSavingDraft] = useState(false);

  // New loading states for button disable conditions
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [isImprovingInstruction, setIsImprovingInstruction] = useState(false);
  const [isUploadingLogoState, setIsUploadingLogoState] = useState(false);
  const [isUploadingFiles, setIsUploadingFiles] = useState<boolean>(false);

  // Draft agent state
  const [draftAgentId, setDraftAgentId] = useState<string | null>(null);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  // Plan limits state
  const [planLimits, setPlanLimits] = useState<{
    canCreate: boolean;
    message: string;
    agentsLeft: number;
    totalAgents: number;
    currentAgentCount: number;
    plan?: any;
  } | null>(null);

  // Clear any previous draft data when creating a new agent
  useEffect(() => {
    localStorage.removeItem('draftAgentId');
    setDraftAgentId(null);
    setLogoUrl(null);
  }, []);

  // Ensure form validation is properly connected
  useEffect(() => {
    // Set up form validation mode for real-time feedback
    form.clearErrors();
  }, [form]);

  // Check if slug is available
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const checkSlugAvailability = useCallback(
    debounce(async (slug: string) => {
      try {
        if (!session?.user?.email || !slug) {
          setSlugExists(false);
          setIsCheckingSlug(false);
          setIsOwnDraft(false);
          return true;
        }
        setIsCheckingSlug(true);
        const response = await fetch(
          `/api/agents/check-slug?slug=${slug}&userEmail=${session.user.email}`,
        );
        const data = await response.json();
        setIsOwnDraft(data.isOwnDraft || false);
        if (data.isOwnDraft && data.draftAgentId) {
          setDraftAgentId(data.draftAgentId);
        }
        const isAvailable = !data.exists && !data.isOwnDraft;
        setSlugExists(!isAvailable);
        setIsCheckingSlug(false);
        return isAvailable;
      } catch (error) {
        console.error('Error checking slug availability:', error);
        setSlugExists(false);
        setIsCheckingSlug(false);
        return true;
      }
    }, 500),
    [session, setIsCheckingSlug, setSlugExists, setIsOwnDraft, setDraftAgentId],
  );

  // Function to create or update draft agent
  const createOrUpdateDraftAgent = useCallback(async () => {
    if (!session?.user?.email) return null;

    // Check if required fields are filled
    const formValues = form.getValues();
    if (
      !formValues.agentName ||
      !formValues.slug ||
      formValues.agentName.length < 4 ||
      formValues.slug.length < 1
    ) {
      toast({
        title: 'Validation Error',
        description: 'Agent name and URL path are required',
        variant: 'destructive',
      });
      return null;
    }

    setIsSavingDraft(true);

    try {
      // Process files to ensure they're in the correct format
      let formFiles = formValues.files;
      if (typeof formFiles === 'string') {
        try {
          formFiles = JSON.parse(formFiles);
        } catch (e) {
          console.error('Error parsing files string in form values:', e);
          formFiles = [];
        }
      } else if (!Array.isArray(formFiles)) {
        formFiles = [];
      }

      const processedFiles = formFiles
        ? formFiles
          .filter((file) => file.status === 'completed')
          .map((file) => ({
            url: file.url,
            file_name: file.name,
            file_size: file.size ? file.size.toString() : undefined,
          }))
        : [];
      // Ensure slug is alphanumeric only
      const cleanedSlug = formValues.slug
        ? formValues.slug.replace(/[^a-z0-9]/g, '')
        : formValues.slug;

      const data = {
        ...formValues,
        slug: cleanedSlug, // Use the cleaned alphanumeric-only slug
        id: draftAgentId || undefined,
        quickMessages: formValues.quickMessages || [], // Use form's quickMessages
        isDraft: true,
        logo: logoUrl || formValues.logo, // Use logoUrl, fallback to logo field
        files: processedFiles,
        selectedLinks: formValues.selectedLinks,
        websiteUrl: formValues.websiteUrl,
      };
      let response;
      if (!draftAgentId) {
        response = await fetch('/api/agents', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
      } else {
        response = await fetch(`/api/agents?id=${draftAgentId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
      }

      if (!response.ok) {
        // Try to get the error details from the response
        let errorDetails;
        try {
          errorDetails = await response.json();
        } catch (e) {
          errorDetails = await response.text();
        }

        console.error('❌ API Response Error:', {
          status: response.status,
          statusText: response.statusText,
          operation: draftAgentId ? 'UPDATE' : 'CREATE',
          errorDetails: errorDetails,
          validationErrors: errorDetails?.errors || [],
          errorMessage: errorDetails?.message || 'Unknown error',
          requestUrl: draftAgentId
            ? `/api/agents?id=${draftAgentId}`
            : '/api/agents',
          requestMethod: draftAgentId ? 'PUT' : 'POST',
          timestamp: new Date().toISOString(),
        });

        throw new Error(
          `Failed to save draft agent: ${errorDetails?.message || response.statusText}`,
        );
      }

      const responseData = await response.json();

      // If this is a new agent, store the ID
      if (!draftAgentId && responseData.agent?.id) {
        setDraftAgentId(responseData.agent.id);
        localStorage.setItem('draftAgentId', responseData.agent.id);
      }

      toast({
        title: 'Draft saved successfully',
        description: 'Your progress has been saved.',
      });

      return responseData.agent;
    } catch (error) {
      console.error('Error saving draft:', error);
      toast({
        title: 'Error saving draft',
        description:
          error instanceof Error ? error.message : 'Failed to save draft',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsSavingDraft(false);
    }
  }, [session?.user?.email, draftAgentId, form, logoUrl, setIsSavingDraft]);

  // Check plan limits
  const checkPlanLimits = useCallback(async () => {
    if (!session?.user?.email) return;
    try {
      const response = await fetch('/api/agents/check-plan-limits');
      const data = await response.json();
      setPlanLimits(data);
    } catch (error) {
      console.error('Error checking plan limits:', error);
    }
  }, [session?.user?.email]);

  // Handle form submission
  const handleSubmit = async () => {
    if (!session?.user?.email) return;

    // Check the current button text to determine the action
    const buttonText = getSubmitButtonText();

    // Handle different upgrade scenarios
    if (buttonText === 'Start your free trial') {
      initiateFreeTrial();
      return;
    } else if (buttonText === 'Upgrade to enterprise plan') {
      // Auto-save draft before redirecting
      const formValues = form.getValues();
      if (formValues.agentName && formValues.agentName.trim() !== '') {
        try {
          await createOrUpdateDraftAgent();
        } catch (error) {
          console.error('Error auto-saving draft before redirect:', error);
        }
      }
      window.open('https://buildthatidea.typeform.com/to/dX0HyMyI', '_blank');
      return;
    } else if (buttonText === 'Upgrade to pro plan') {
      // Auto-save draft before redirecting
      const formValues = form.getValues();
      if (formValues.agentName && formValues.agentName.trim() !== '') {
        try {
          await createOrUpdateDraftAgent();
        } catch (error) {
          console.error('Error auto-saving draft before redirect:', error);
        }
      }
      router.push('/billing');
      return;
    }

    // Check if files are still uploading
    const files = form.getValues('files') || [];
    const fileUploadInProgress =
      (Array.isArray(files) &&
        files.some((file) => file.status === 'uploading')) ||
      isUploadingFiles;

    if (fileUploadInProgress) {
      toast({
        title: 'Files uploading',
        description:
          'Please wait for all files to finish uploading before launching the agent.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const data = form.getValues();

      // First ensure data files are an array
      let formFiles = data.files;
      if (typeof formFiles === 'string') {
        try {
          formFiles = JSON.parse(formFiles);
        } catch (e) {
          console.error(
            '[SUBMIT] Error parsing files string in handleSubmit:',
            e,
          );
          formFiles = [];
        }
      } else if (!Array.isArray(formFiles)) {
        formFiles = [];
      }

      // Process files to ensure they're in the correct format
      const processedFiles = formFiles
        ? formFiles
          .filter((file) => file.status === 'completed')
          .map((file) => ({
            url: file.url,
            file_name: file.name,
            file_size: file.size ? file.size.toString() : undefined,
          }))
        : [];

      // Ensure price is always a valid string to prevent NaN
      let validatedData = { ...data };

      // Handle price field specifically to prevent NaN
      if (validatedData.isPaid) {
        // Make sure price is a string and has a valid value
        validatedData.price = validatedData.price
          ? validatedData.price.toString()
          : '0';
        // If price is empty or not a valid number, set it to "0"
        if (!validatedData.price.trim() || isNaN(Number(validatedData.price))) {
          validatedData.price = '0';
        }
      } else {
        // If agent is not paid, ensure price is "0"
        validatedData.price = '0';
      }

      // Get vector configuration if needed (files or website)
      let vectorConfig = {};
      const hasFiles = validatedData.files && validatedData.files.length > 0;
      const hasWebsite =
        validatedData.websiteUrl && validatedData.websiteUrl.trim();

      if (hasFiles || hasWebsite) {
        try {
          // Use existing draft agent ID for get-index call
          const tempAgentId = draftAgentId;

          if (!tempAgentId) {
            console.warn('No draft agent ID available for vector config');
          } else {
            const indexResponse = await fetch('/api/agents/get-index', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                agentId: tempAgentId,
              }),
            });

            if (indexResponse.ok) {
              const indexData = await indexResponse.json();
              if (indexData.endpoint && indexData.token) {
                vectorConfig = {
                  endpoint: indexData.endpoint,
                  token: indexData.token,
                };
              }
            }
          }
        } catch (error) {
          console.error('Error getting vector config:', error);
          // Continue without vector config
        }
      }

      const finalSubmitData = {
        ...validatedData,
        ...vectorConfig, // Include endpoint and token if available
        id: draftAgentId, // Use the existing draftAgentId
        quickMessages: validatedData.quickMessages || [], // Use form's quickMessages
        isDraft: false,
        status: 'active', // Explicitly set status to active
        logo: logoUrl || validatedData.logo, // Use logoUrl, fallback to logo field
        // Always ensure files is a proper JSON array
        files: processedFiles,
        // Keep websiteUrl and selectedLinks for scraping process
        websiteUrl: validatedData.websiteUrl,
        selectedLinks: validatedData.selectedLinks,
      };

      // Submit the agent for creation - use PUT method instead of POST
      // This ensures we do a true update instead of potentially creating a new record
      let response;
      if (!finalSubmitData.id) {
        response = await fetch('/api/agents', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(finalSubmitData),
        });
      } else {
        response = await fetch(`/api/agents?id=${finalSubmitData.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(finalSubmitData),
        });
      }
      if (!response.ok) {
        const errorData = await response.json();

        if (response.status === 403) {
          toast({
            title: 'Subscription required',
            description: errorData.message,
            variant: 'destructive',
          });
          setIsSubmitting(false);
          return;
        }

        throw new Error(errorData.message || 'Failed to create agent');
      }

      const responseData = await response.json();

      // Store the created agent data for the success page
      localStorage.setItem(
        'createdAgentData',
        JSON.stringify({
          id: responseData.agent.id,
          name: responseData.agent.name,
          slug: responseData.agent.slug,
        }),
      );

      // Clear the draft agent ID
      localStorage.removeItem('draftAgentId');

      // Create a free subscription with 25 messages for the creator if it's a paid agent
      // if (validatedData.isPaid) {
      //   try {
      //     // Use the getOrCreateUserSubscription function with isCreator=true
      //     const response = await fetch(
      //       '/api/billing/user-subscription/get-or-create',
      //       {
      //         method: 'POST',
      //         headers: {
      //           'Content-Type': 'application/json',
      //         },
      //         body: JSON.stringify({
      //           agentId: responseData.agent.id,
      //           isCreator: true
      //         }),
      //       },
      //     );

      //     if (!response.ok) {
      //       console.error(
      //         'Failed to create creator subscription:',
      //         await response.text(),
      //       );
      //     }
      //   } catch (subscriptionError) {
      //     console.error('Error creating creator subscription:', subscriptionError);
      //     // Continue even if subscription creation fails
      //   }
      // }

      // Handle website scraping and file embeddings
      if (hasWebsite || hasFiles) {
        try {
          // Handle website scraping if URL exists
          if (hasWebsite) {
            // Extract URLs with 'selected' status from selectedLinks
            const selectedUrls: string[] = [];
            if (
              validatedData.selectedLinks &&
              Array.isArray(validatedData.selectedLinks)
            ) {
              validatedData.selectedLinks.forEach((website) => {
                if (website.links && Array.isArray(website.links)) {
                  website.links.forEach(
                    (link: { status: string; url: string }) => {
                      if (link.status === 'selected') {
                        selectedUrls.push(link.url);
                      }
                    },
                  );
                }
              });
            }

            await fetch('/api/fire-crawl/batch-scrape', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                url: validatedData.websiteUrl,
                agentId: responseData.agent.id,
                urls: selectedUrls.length > 0 ? selectedUrls : undefined,
                limit: 100,
              }),
            });
          }

          // Handle file embeddings if files exist
          if (hasFiles) {
            fetch('/api/agents/initiate-embeddings', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                agentId: responseData.agent.id,
              }),
            });
          }
        } catch (error) {
          console.error(
            '❌ Error in website scraping/embedding process:',
            error,
          );
        }
      }
      // Redirect to success page
      router.push(`/create/success?agent=${responseData.agent.slug}`);
    } catch (error) {
      console.error('Error creating agent:', error);
      toast({
        title: 'Error creating agent',
        description:
          error instanceof Error ? error.message : 'Failed to create agent',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load plan limits on component mount
  useEffect(() => {
    if (session?.user?.email) {
      checkPlanLimits();
    }
  }, [session?.user?.email, checkPlanLimits]);

  // Check for trial success parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const trialStatus = params.get('trial');

    if (trialStatus === 'success') {
      toast({
        title: 'Free trial activated!',
        description:
          'Your 2-week free trial of the Hobby Plan has been activated. You can now create and launch your agent.',
      });

      // Remove the trial parameter from URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);

      // Refresh plan limits
      checkPlanLimits();
    } else if (trialStatus === 'canceled') {
      toast({
        title: 'Trial canceled',
        description:
          "Trial signup was canceled. You can try again when you're ready.",
        variant: 'destructive',
      });

      // Remove the payment parameter from URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [checkPlanLimits]);

  // Handle slug change
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'slug' && value.slug) {
        checkSlugAvailability(value.slug as string);
      }

      // Auto-generate slug from agent name if slug is empty
      if (name === 'agentName' && value.agentName && !form.getValues('slug')) {
        const generatedSlug = (value.agentName as string)
          .toLowerCase()
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-');

        form.setValue('slug', generatedSlug, {
          shouldValidate: true,
          shouldDirty: true,
        });
      }
    });

    return () => subscription.unsubscribe();
  }, [form, checkSlugAvailability]);

  // Function to update step validity
  const updateStepValidity = useCallback(
    (stepIndex: number, valid: boolean) => {
      setStepValidity((prev) => {
        const newValid = [...prev];
        if (newValid[stepIndex] !== valid) {
          newValid[stepIndex] = valid;
          return newValid;
        }
        return prev;
      });
    },
    [],
  );

  const getSubmitButtonText = () => {
    if (!planLimits) return 'Create Agent';

    if (planLimits.currentAgentCount === 0 && !planLimits.plan) {
      return 'Start your free trial';
    } else if (!planLimits.canCreate) {
      if ((planLimits.currentAgentCount ?? 0) >= 3) {
        return 'Upgrade to enterprise plan';
      } else {
        return 'Upgrade to pro plan';
      }
    }

    return 'Create Agent';
  };

  // Function to initiate the free trial checkout
  const initiateFreeTrial = async () => {
    try {
      const formValues = form.getValues();
      const draftId = draftAgentId || null;

      // Auto-save draft before redirecting if agent name exists
      if (formValues.agentName && formValues.agentName.trim() !== '') {
        toast({
          title: 'Saving draft',
          description: 'Saving your draft before proceeding...',
        });

        try {
          await createOrUpdateDraftAgent();
        } catch (saveError) {
          console.error('Error auto-saving draft before checkout:', saveError);
          toast({
            title: 'Warning',
            description:
              'Could not save draft completely. Proceeding with checkout.',
            variant: 'destructive',
          });
        }
      }

      const response = await fetch('/api/agents/free-trial-hobby', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId: draftId,
          agentSlug: formValues.slug || 'new-agent',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create free trial checkout');
      }

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        toast({
          title: 'Error',
          description: 'Unable to start free trial. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error initiating free trial:', error);
      toast({
        title: 'Error',
        description: 'Failed to start free trial. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleUpgradeToPro = async () => {
    // Auto-save draft before redirecting
    const formValues = form.getValues();
    if (formValues.agentName && formValues.agentName.trim() !== '') {
      try {
        await createOrUpdateDraftAgent();
      } catch (error) {
        console.error('Error auto-saving draft before redirect:', error);
      }
    }

    // Redirect to billing page
    router.push('/billing');
  };

  // Handler for upgrading to Enterprise
  const handleUpgradeToEnterprise = () => {
    // Open TypeForm in new tab
    window.open('https://buildthatidea.typeform.com/to/dX0HyMyI', '_blank');
  };

  return (
    <div className="bg-background min-h-screen flex items-center justify-center lg:p-6">
      <div className="w-full max-w-7xl lg:px-4 xl:px-6 h-full">
        <Form {...form}>
          <form>
            <div className="bg-card dark:bg-card lg:rounded-lg shadow-sm border-0 lg:border border-border overflow-hidden min-h-screen lg:min-h-[calc(100vh-3rem)] lg:h-[calc(100vh-3rem)]">
              <MultiStepForm
                steps={formSteps}
                onComplete={handleSubmit}
                onSaveStep={createOrUpdateDraftAgent}
                isSubmitting={isSubmitting}
                isSavingDraft={isSavingDraft}
                submitButtonText={getSubmitButtonText()}
                showBackToLogin={true}
                logoPreview={logoPreview}
                draftAgentId={draftAgentId}
                // New loading states
                isUploadingLogo={isUploadingLogoState}
                isCheckingSlug={isCheckingSlug}
                isUploadingFiles={isUploadingFiles}
                isImprovingInstruction={isImprovingInstruction}
                // Plan limits and handlers
                planLimits={planLimits}
                onStartFreeTrial={initiateFreeTrial}
                onUpgradeToEnterprise={handleUpgradeToEnterprise}
                onUpgradeToPro={handleUpgradeToPro}
                onImprovingInstructionChange={setIsImprovingInstruction}
                onUploadingLogoChange={setIsUploadingLogoState}
              >
                <BasicInfoStep
                  form={form}
                  updateValidity={(valid) => updateStepValidity(0, valid)}
                  isCheckingSlug={isCheckingSlug}
                  slugExists={slugExists}
                  isOwnDraft={isOwnDraft}
                  logoPreview={logoPreview}
                  setLogoPreview={setLogoPreview}
                  setLogoUrl={setLogoUrl}
                  draftAgentId={draftAgentId}
                />
                <CapabilitiesStep
                  form={form}
                  updateValidity={(valid) => updateStepValidity(1, valid)}
                  draftAgentId={draftAgentId}
                  setIsUploadingFiles={setIsUploadingFiles}
                  planLimits={planLimits}
                />
                <ConfigurationStep
                  form={form}
                  updateValidity={(valid) => updateStepValidity(2, valid)}
                />
                <MonetizationStep
                  form={form}
                  updateValidity={(valid) => updateStepValidity(3, valid)}
                />
              </MultiStepForm>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}