/**
 * Logger configuration for different environments
 */

import type { LoggerOptions } from './logger';

// Default configuration for all environments
const defaultConfig: LoggerOptions = {
  enableConsole: process.env.NODE_ENV !== 'production',
  minLevel: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
};

// Environment-specific configurations
const environmentConfigs: Record<string, Partial<LoggerOptions>> = {
  development: {
    enableConsole: true,
    minLevel: 'debug',
  },
  test: {
    enableConsole: false,
    minLevel: 'warn',
  },
  production: {
    enableConsole: false,
    minLevel: 'info',
  },
};

// Get the current environment
const currentEnv = process.env.NODE_ENV || 'development';

// Export the configuration for the current environment
export const loggerConfig: LoggerOptions = {
  ...defaultConfig,
  ...(environmentConfigs[currentEnv] || {}),
};

// Export a function to get service-specific configuration
export function getServiceConfig(service: string): LoggerOptions {
  // You could add service-specific overrides here
  // For example, you might want some services to log at different levels
  const serviceOverrides: Record<string, Partial<LoggerOptions>> = {
    // Example: Make authentication logs more verbose in development
    'AuthService': {
      minLevel: currentEnv === 'development' ? 'debug' : 'info',
    },
    // Example: Always log payment-related events, even in production
    'PaymentService': {
      enableConsole: true,
    },
  };

  return {
    ...loggerConfig,
    service,
    ...(serviceOverrides[service] || {}),
  };
}

export default loggerConfig;
