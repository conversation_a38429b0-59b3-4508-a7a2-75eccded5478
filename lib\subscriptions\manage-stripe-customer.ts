import { db } from '@/lib/db';
import { stripeCustomers, stripeUsers } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function findCustomerId(userId: string): Promise<string | null> {
  try {
    const customer = await db.query.stripeCustomers.findFirst({
      where: eq(stripeCustomers.userId, userId)
    });

    return customer?.stripeCustomerId || null;
  } catch (error) {
    console.error('Error finding stripe customer:', error);
    throw error;
  }
}


export async function findStripeUserId(userId: string): Promise<string | null> {
  try {
    const customer = await db.query.stripeUsers.findFirst({
      where: eq(stripeUsers.userId, userId)
    });

    console.log('Found stripe user:', userId);
    return customer?.stripeCustomerId || null;
  } catch (error) {
    console.error('Error finding stripe customer:', error);
    throw error;
  }
}


export async function saveStripeCustomerInfo(userId: string, stripeCustomerId: string) {
  try {
    // Check if customer already exists
    const existingCustomer = await db.query.stripeCustomers.findFirst({
      where: eq(stripeCustomers.stripeCustomerId, stripeCustomerId)
    });

    if (!existingCustomer) {
      // Insert new customer record
      await db.insert(stripeCustomers).values({
        userId,
        stripeCustomerId
      });
      console.log('Created new stripe customer record:', { userId, stripeCustomerId });
    } else {
      console.log('Stripe customer record already exists:', { userId, stripeCustomerId });
    }

    return true;
  } catch (error) {
    console.error('Error managing stripe customer info:', error);
    throw error;
  }
}


export async function saveStripeUserInfo(userId: string, stripeCustomerId: string) {
  try {
    // Check if customer already exists
    const existingCustomer = await db.query.stripeUsers.findFirst({
      where: eq(stripeUsers.stripeCustomerId, stripeCustomerId)
    });

    if (!existingCustomer) {
      // Insert new customer record
      await db.insert(stripeUsers).values({
        userId,
        stripeCustomerId
      });
      console.log('Created new stripe customer record:', { userId, stripeCustomerId });
    } else {
      console.log('Stripe customer record already exists:', { userId, stripeCustomerId });
    }

    return true;
  } catch (error) {
    console.error('Error managing stripe customer info:', error);
    throw error;
  }
}
