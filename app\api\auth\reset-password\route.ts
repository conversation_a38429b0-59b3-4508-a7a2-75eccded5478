import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { user } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { hash } from 'bcrypt';
import { trackForgotPassword } from '@/lib/user-journey';
import { addTagToSubscriber, CONVERTKIT_TAGS } from '@/lib/convertkit';
import { Resend } from 'resend';

// Function to generate a random password
function generateRandomPassword(length = 10) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

// Function to send an email with the new password
async function sendPasswordResetEmail(email: string, password: string) {
  try {
    // Always log the password reset information for development/debugging
    console.log('=============== PASSWORD RESET EMAIL ===============');
    console.log(`To: ${email}`);
    console.log(`Subject: Your New Password for BuildThatIdea`);
    console.log(`New Password: ${password}`);
    console.log('===================================================');
    
    // Use the direct API call to ensure we see all details
    const CONVERTKIT_API_SECRET = process.env.CONVERTKIT_API_SECRET;
    if (!CONVERTKIT_API_SECRET) {
      throw new Error('ConvertKit API secret is not configured');
    }
    
    // Step 1: Add the FORGET_PASSWORD tag to trigger the sequence
    console.log(`Adding FORGET_PASSWORD tag to subscriber ${email}...`);
    
    // Get the tag ID from environment variables or use the default
    const tagId = CONVERTKIT_TAGS.FORGET_PASSWORD || '7993407';
    console.log(`Using tag ID: ${tagId}`);
    
    // Add the tag to the subscriber
    const tagResponse = await fetch(`https://api.convertkit.com/v3/tags/${tagId}/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_secret: CONVERTKIT_API_SECRET,
        email: email
      })
    });
    
    const tagResponseData = await tagResponse.json();
    console.log(`Tag response:`, JSON.stringify(tagResponseData, null, 2));
    
    if (!tagResponse.ok) {
      throw new Error(`Failed to add tag: ${JSON.stringify(tagResponseData)}`);
    }
    
    console.log('Successfully added FORGET_PASSWORD tag to subscriber');
    
    // Step 2: Send the password reset email using Resend
    console.log('Sending password reset email with Resend...');
    
    // Get the base URL for the app
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://buildthatidea.com';
    const logoUrl = `https://app.buildthatidea.com/images/logo.png`;
    
    // Format the email content with the password
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your New Password</title>
      </head>
      <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f9fafb; margin: 0; padding: 0;">
        <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f9fafb;">
          <tr>
            <td style="padding: 20px 0;">
              <table align="center" width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); margin: 0 auto;">
                <!-- Header with Logo -->
                <tr>
                  <td style="padding: 30px 30px 20px; text-align: center; border-bottom: 1px solid #eaeaea;">
                    <img src="${logoUrl}" alt="BuildThatIdea Logo" style="width: 180px; height: auto; max-width: 100%;">
                  </td>
                </tr>
                
                <!-- Email Content -->
                <tr>
                  <td style="padding: 30px;">
                    <h1 style="color: #f97316; font-size: 24px; margin-top: 0; margin-bottom: 20px; font-weight: 600;">Password Reset</h1>
                    
                    <p style="margin-bottom: 16px; color: #4b5563; font-size: 16px;">Hello,</p>
                    
                    <p style="margin-bottom: 16px; color: #4b5563; font-size: 16px;">You recently requested to reset your password for your BuildThatIdea account. We've generated a new password for you:</p>
                    
                    <div style="background-color: #f3f4f6; border-radius: 6px; padding: 16px; margin: 24px 0; text-align: center;">
                      <p style="font-family: monospace; font-size: 18px; font-weight: bold; margin: 0; color: #1f2937;">${password}</p>
                    </div>
                    
                    <p style="margin-bottom: 16px; color: #4b5563; font-size: 16px;">Use this password to log in to your account.</p>
                    
                    <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 30px 0;">
                      <tr>
                        <td align="center">
                          <a href="${baseUrl}/login" style="display: inline-block; background-color: #f97316; color: #ffffff; font-weight: 600; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-size: 16px;">Login to Your Account</a>
                        </td>
                      </tr>
                    </table>
                    
                    <p style="margin-bottom: 16px; color: #4b5563; font-size: 16px;">If you didn't request a password reset, please contact our support team immediately.</p>
                    
                    <p style="margin-bottom: 0; color: #4b5563; font-size: 16px;">Best regards,<br>The BuildThatIdea Team</p>
                  </td>
                </tr>
                
                <!-- Footer -->
                <tr>
                  <td style="padding: 20px 30px; text-align: center; color: #6b7280; font-size: 14px; background-color: #f3f4f6; border-top: 1px solid #eaeaea; border-radius: 0 0 8px 8px;">
                    <p style="margin: 0 0 8px;">© ${new Date().getFullYear()} BuildThatIdea. All rights reserved.</p>
                    <p style="margin: 0;">If you have any questions, please contact our support team.</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;
    
    // Always log the password in development for testing
    console.log('Running in development mode - showing email content:');
    console.log(emailHtml);
    
    // Get the Resend API key
    const RESEND_API_KEY = process.env.RESEND_API_KEY;
    
    if (!RESEND_API_KEY) {
      console.error('Resend API key is not configured');
      console.log('Password reset email not sent. Check logs for password.');
    } else {
      try {
        // Initialize Resend
        const resend = new Resend(RESEND_API_KEY);
        
        // Send the email
        const result = await resend.emails.send({
          from: process.env.RESEND_FROM_EMAIL || 'BuildThatIdea <<EMAIL>>',
          to: email,
          subject: 'Your New Password for BuildThatIdea',
          html: emailHtml,
        });
        
        console.log('Resend email result:', JSON.stringify(result, null, 2));
        
        if (result.error) {
          throw new Error(`Failed to send email: ${JSON.stringify(result.error)}`);
        }
        
        console.log('Successfully sent password reset email via Resend');
      } catch (emailError) {
        console.error('Error sending email with Resend:', emailError);
        console.log('Password reset email not sent. Check logs for password.');
      }
    }
    
    return { 
      success: true, 
      message: 'Password reset email triggered via ConvertKit tag'
    };
  } catch (error) {
    console.error('Error sending email:', error);
    
    // Always log the password for development/debugging
    console.log('=============== PASSWORD RESET EMAIL ===============');
    console.log(`To: ${email}`);
    console.log(`Subject: Your New Password for BuildThatIdea`);
    console.log(`New Password: ${password}`);
    console.log('===================================================');
    
    // Return a success response since we've logged the password
    return { success: true, message: 'Password reset processed. Check logs for password.' };
  }
}

export async function POST(request: Request) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    // Check if user exists
    const userRecord = await db.query.user.findFirst({
      where: eq(user.email, email),
    });
    
    if (!userRecord) {
      // For security reasons, don't reveal that the user doesn't exist
      // Instead, pretend we sent an email
      return NextResponse.json({ success: true });
    }
    
    // Generate a new random password
    const password = generateRandomPassword();
    
    // Hash the new password
    const hashedPassword = await hash(password, 10);
    
    // Update the user's password in the database
    await db
      .update(user)
      .set({ password: hashedPassword })
      .where(eq(user.id, userRecord.id));
    
    // Send the new password to the user's email and add the FORGET_PASSWORD tag
    await sendPasswordResetEmail(email, password);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error resetting password:', error);
    return NextResponse.json(
      { error: 'Failed to process your request' },
      { status: 500 }
    );
  }
}
