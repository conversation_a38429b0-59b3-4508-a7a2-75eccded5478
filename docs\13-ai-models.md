# AI Models and Integration

## Overview

The platform integrates with various AI models to power its agent capabilities. This document details the AI model integration, including the technology stack, implementation details, and best practices for working with AI models in the platform.

## Technology Stack

- **Vercel AI SDK**: Core library for AI model integration
- **AI SDK Providers**: Integration with various AI providers
  - **@ai-sdk/anthropic**: For Claude models
  - **@ai-sdk/openai**: For OpenAI models
  - **@ai-sdk/xai**: For additional AI capabilities
- **Next.js API Routes**: Backend endpoints for AI interactions
- **Streaming Responses**: Real-time streaming of AI responses

## AI Models

The platform supports multiple AI models:

### 1. Anthropic Claude Models

- **Claude 3 Haiku**: Fast, cost-effective model for most use cases
- **Claude 3 Sonnet**: Balanced model for more complex tasks
- **Claude 3 Opus**: Most capable model for advanced reasoning

### 2. OpenAI Models

- **GPT-4o**: Latest OpenAI model with strong capabilities
- **GPT-4 Turbo**: Powerful model for complex tasks
- **GPT-3.5 Turbo**: Cost-effective model for simpler tasks

## Implementation Details

### 1. AI SDK Integration

The AI SDK is integrated into the platform for model interactions:

```typescript
import { AI } from 'ai';
import { getKnowledge } from '@/lib/ai/tools/get-knowledge';

// Create AI instance with the appropriate model
const ai = new AI({
  model: agent.model || 'claude-3-haiku',
  tools: [getKnowledge],
});

// Process the message with streaming
const result = await ai.run({
  messages,
  tools: [
    {
      type: 'function',
      name: 'getKnowledge',
      parameters: {
        query: messages[messages.length - 1].content,
        agentId: agent.id,
        limit: 5,
      },
    },
  ],
});
```

### 2. Streaming Responses

The platform uses streaming responses for a better user experience:

```typescript
import { StreamingTextResponse } from 'ai';

// Return streaming response
return new StreamingTextResponse(result.text);
```

### 3. Custom Tools

The platform implements custom tools for AI models:

```typescript
import { tool } from 'ai';
import { z } from 'zod';
import { retrieveRelevantContent } from '@/lib/embeddings/retriever';

/**
 * Tool for retrieving relevant content from the agent's knowledge base
 */
export const getKnowledge = tool({
  description:
    "REQUIRED: You MUST call this tool for EVERY question before responding. This tool retrieves relevant information from the agent's knowledge base that you should use in your answer.",
  parameters: z.object({
    query: z.string().describe('The search query to find relevant information'),
    agentId: z.string().describe('The ID of the agent whose knowledge base to search'),
    limit: z.number().optional().describe('Maximum number of results to return (default: 5)'),
  }),
  execute: async ({ query, agentId, limit = 5 }) => {
    try {
      // Use the retrieveRelevantContent function to get relevant content
      const relevantContent = await retrieveRelevantContent(
        query,
        agentId,
        limit,
        0.5, // Default similarity threshold
      );

      // Process and return the content
      // ...
    } catch (error) {
      // Handle errors
      // ...
    }
  },
});
```

### 4. Message Processing

The platform processes messages and manages conversation context:

```typescript
// Simplified message processing
export async function processMessage(message: string, chatId: string) {
  // Get chat and agent information
  const chat = await getChat(chatId);
  const agent = await getAgentById({ agentId: chat.agentId });
  
  // Get previous messages
  const previousMessages = await getMessages(chatId);
  
  // Format messages for the AI
  const formattedMessages = formatMessages(previousMessages, message);
  
  // Process with AI
  const ai = new AI({
    model: agent.model || 'claude-3-haiku',
    tools: [getKnowledge],
  });
  
  const result = await ai.run({
    messages: formattedMessages,
    tools: [
      {
        type: 'function',
        name: 'getKnowledge',
        parameters: {
          query: message,
          agentId: agent.id,
          limit: 5,
        },
      },
    ],
  });
  
  // Store the response
  await storeMessage({
    chatId,
    role: 'assistant',
    content: result.text,
  });
  
  return result;
}
```

## AI API Routes

The platform implements several API routes for AI interactions:

### 1. Chat API Route

```typescript
// POST /api/chat-agent/:agentId
export async function POST(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    const { messages } = await request.json();
    const agentId = params.agentId;
    const session = await auth();
    
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get agent details
    const agent = await getAgentById({ agentId });
    
    if (!agent) {
      return Response.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Check access permissions
    const hasAccess = await checkAgentAccess(session.user.id, agentId);
    
    if (!hasAccess) {
      return Response.json({ error: 'Access denied' }, { status: 403 });
    }
    
    // Create AI instance with the appropriate model
    const ai = new AI({
      model: agent.model || 'claude-3-haiku',
      tools: [getKnowledge],
    });
    
    // Process the message with streaming
    const result = await ai.run({
      messages,
      tools: [
        {
          type: 'function',
          name: 'getKnowledge',
          parameters: {
            query: messages[messages.length - 1].content,
            agentId,
            limit: 5,
          },
        },
      ],
    });
    
    // Return streaming response
    return new StreamingTextResponse(result.text);
  } catch (error) {
    console.error('Error in chat-agent API:', error);
    return Response.json(
      { error: 'Failed to process message' },
      { status: 500 }
    );
  }
}
```

### 2. Message API Route

```typescript
// POST /api/chat/:id/messages
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { content } = await request.json();
    const chatId = params.id;
    const session = await auth();
    
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Store user message
    const userMessage = await storeMessage({
      chatId,
      role: 'user',
      content,
    });
    
    // Get chat and agent information
    const chat = await getChat(chatId);
    const agent = await getAgentById({ agentId: chat.agentId });
    
    // Create AI instance with the appropriate model
    const ai = new AI({
      model: agent.model || 'claude-3-haiku',
      tools: [getKnowledge],
    });
    
    // Get previous messages
    const previousMessages = await getMessages(chatId);
    
    // Format messages for the AI
    const formattedMessages = formatMessages(previousMessages);
    
    // Process with AI and stream the response
    const result = ai.run({
      messages: formattedMessages,
      tools: [
        {
          type: 'function',
          name: 'getKnowledge',
          parameters: {
            query: content,
            agentId: chat.agentId,
            limit: 5,
          },
        },
      ],
    });
    
    // Store the assistant message after streaming
    result.text.then(async (text) => {
      await storeMessage({
        chatId,
        role: 'assistant',
        content: text,
      });
    });
    
    // Return streaming response
    return new StreamingTextResponse(result.text);
  } catch (error) {
    console.error('Error in messages API:', error);
    return Response.json(
      { error: 'Failed to process message' },
      { status: 500 }
    );
  }
}
```

## Model Configuration

### 1. Model Selection

The platform allows creators to select models for their agents:

```typescript
// Model selection options
const modelOptions = [
  { value: 'claude-3-haiku', label: 'Claude 3 Haiku (Fast)' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet (Balanced)' },
  { value: 'claude-3-opus', label: 'Claude 3 Opus (Powerful)' },
  { value: 'gpt-4o', label: 'GPT-4o' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
];
```

### 2. Model Parameters

The platform configures model parameters for optimal performance:

```typescript
// Example model parameters
const modelParams = {
  'claude-3-haiku': {
    temperature: 0.7,
    maxTokens: 4096,
  },
  'claude-3-sonnet': {
    temperature: 0.7,
    maxTokens: 8192,
  },
  'claude-3-opus': {
    temperature: 0.7,
    maxTokens: 16384,
  },
  'gpt-4o': {
    temperature: 0.7,
    maxTokens: 8192,
  },
  'gpt-4-turbo': {
    temperature: 0.7,
    maxTokens: 4096,
  },
  'gpt-3.5-turbo': {
    temperature: 0.7,
    maxTokens: 4096,
  },
};
```

## Token Management

### 1. Token Counting

The platform counts tokens for billing and rate limiting:

```typescript
import { encode } from 'gpt-tokenizer';

// Count tokens in a message
function countTokens(text: string): number {
  return encode(text).length;
}

// Count tokens in a conversation
function countConversationTokens(messages: any[]): number {
  return messages.reduce((total, message) => {
    return total + countTokens(message.content);
  }, 0);
}
```

### 2. Token Limits

The platform enforces token limits to prevent excessive usage:

```typescript
// Check if a conversation exceeds token limits
function checkTokenLimits(messages: any[], model: string): boolean {
  const totalTokens = countConversationTokens(messages);
  const modelParams = getModelParams(model);
  
  return totalTokens <= modelParams.maxTokens;
}
```

## Error Handling

The platform includes comprehensive error handling for AI interactions:

### 1. Model Errors

```typescript
try {
  const result = await ai.run({
    messages,
    tools: [/* ... */],
  });
  
  return new StreamingTextResponse(result.text);
} catch (error) {
  console.error('AI model error:', error);
  
  // Check for specific error types
  if (error.name === 'AbortError') {
    return Response.json({ error: 'Request timed out' }, { status: 408 });
  }
  
  if (error.status === 429) {
    return Response.json({ error: 'Rate limit exceeded' }, { status: 429 });
  }
  
  return Response.json(
    { error: 'Failed to generate response' },
    { status: 500 }
  );
}
```

### 2. Token Limit Errors

```typescript
// Check token limits before processing
if (!checkTokenLimits(messages, model)) {
  return Response.json(
    { error: 'Conversation exceeds token limit' },
    { status: 400 }
  );
}
```

## Performance Considerations

The platform includes several performance optimizations for AI interactions:

### 1. Streaming Responses

Using streaming responses for a better user experience:

```typescript
// Return streaming response
return new StreamingTextResponse(result.text);
```

### 2. Caching

Caching common responses to reduce API calls:

```typescript
// Simplified caching example
const cache = new Map();

function getCachedResponse(key: string): string | null {
  return cache.get(key) || null;
}

function setCachedResponse(key: string, response: string): void {
  cache.set(key, response);
}
```

### 3. Parallel Processing

Processing multiple operations in parallel when possible:

```typescript
// Process multiple operations in parallel
const [agent, previousMessages] = await Promise.all([
  getAgentById({ agentId }),
  getMessages(chatId),
]);
```

## Security Considerations

The platform includes several security measures for AI interactions:

### 1. Input Validation

Validating all inputs to prevent injection attacks:

```typescript
// Validate user input
function validateInput(input: string): boolean {
  // Check for malicious content
  if (input.length > 10000) {
    return false;
  }
  
  // Check for other validation rules
  // ...
  
  return true;
}
```

### 2. Output Filtering

Filtering AI outputs to prevent harmful content:

```typescript
// Filter AI output
function filterOutput(output: string): string {
  // Filter harmful content
  // ...
  
  return output;
}
```

### 3. Rate Limiting

Implementing rate limiting to prevent abuse:

```typescript
// Simplified rate limiting
const rateLimits = new Map();

function checkRateLimit(userId: string): boolean {
  const now = Date.now();
  const userRequests = rateLimits.get(userId) || [];
  
  // Remove requests older than 1 minute
  const recentRequests = userRequests.filter(
    (time: number) => now - time < 60000
  );
  
  // Check if user has exceeded rate limit
  if (recentRequests.length >= 10) {
    return false;
  }
  
  // Update rate limit
  recentRequests.push(now);
  rateLimits.set(userId, recentRequests);
  
  return true;
}
```

## Best Practices

### 1. Prompt Engineering

The platform implements best practices for prompt engineering:

1. **Clear Instructions**: Providing clear instructions to the AI
2. **Context Management**: Managing conversation context effectively
3. **Knowledge Integration**: Integrating knowledge base results effectively

### 2. Model Selection

Guidelines for selecting the appropriate model:

1. **Task Complexity**: Matching model capabilities to task complexity
2. **Cost Considerations**: Balancing performance and cost
3. **Response Time**: Considering model response time for user experience

### 3. Error Handling

Best practices for error handling:

1. **Graceful Degradation**: Providing fallback responses when models fail
2. **User Communication**: Communicating errors to users effectively
3. **Retry Logic**: Implementing retry logic for transient errors

## Future Enhancements

Planned enhancements to the AI integration:

### 1. Model Improvements

1. **Additional Models**: Support for more AI models
2. **Model Switching**: Dynamic model switching based on task complexity
3. **Fine-tuning**: Support for fine-tuned models

### 2. Feature Enhancements

1. **Multi-modal Support**: Support for image and audio inputs
2. **Advanced Tools**: More sophisticated tools for AI models
3. **Improved Context Management**: Better management of conversation context

### 3. Performance Improvements

1. **Optimized Tokenization**: More efficient token counting
2. **Improved Caching**: More sophisticated caching strategies
3. **Parallel Processing**: More efficient parallel processing
