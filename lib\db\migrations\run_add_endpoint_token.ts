import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL is not defined');
}

// Create database client after environment variables are loaded
const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

async function runMigration() {
  try {
    console.log('🔄 Starting migration: Adding endpoint and token columns to agents table');
    
    // Add the new columns
    await db.execute(sql`
      ALTER TABLE "agents" 
      ADD COLUMN IF NOT EXISTS "endpoint" TEXT DEFAULT NULL,
      ADD COLUMN IF NOT EXISTS "token" TEXT DEFAULT NULL;
    `);
    
    console.log('✅ Successfully added endpoint and token columns');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    // Close the database connection
    await client.end();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  runMigration()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
