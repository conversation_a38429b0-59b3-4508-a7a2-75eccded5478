'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  Database,
  DollarSign,
  Zap,
  ArrowUpRight,
  Calendar,
  BarChart3,
  Download
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  RevenueCard,
  TokensCard,
  KnowledgeBaseCard,
  AgentsCard,
  formatTokens,
  formatMB
} from '@/components/dashboard/stat-card';
import { useSubscriptionBalance } from '@/hooks/use-subscription-balance';

// Add import for charts
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

// Sample data for the analytics
interface TokenDataPoint {
  name: string;
  tokens: number;
}

interface RevenueDataPoint {
  name: string;
  revenue: number;
}

interface TokenUsageDataPoint {
  day: string;
  tokens: number;
  date: Date;
}

const tokenData: TokenDataPoint[] = [];
const tokenUsageData: TokenUsageDataPoint[] = [];
const revenueData: RevenueDataPoint[] = [];

// Interface for CreatorPlan from the database
interface CreatorPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  tokensAllowed: number;
  knowledgeBaseSize: string;
  agentsAllowed: number;
  embeddableWidget: boolean;
  monetizationIncluded: boolean;
  cryptoAndFiatPayments: boolean;
  analytics: string;
  supportType: string;
  otherFeatures: string[];
}

export default function AnalyticsPage() {
  // State for loading and data
  const [revenueData, setRevenueData] = useState<RevenueDataPoint[]>([]);
  const [revenue, setRevenue] = useState(0);
  const [isLoadingRevenue, setIsLoadingRevenue] = useState(true);
  const [tokenUsageData, setTokenUsageData] = useState<TokenUsageDataPoint[]>([]);
  const [totalTokens, setTotalTokens] = useState(0);
  const [avgTokens, setAvgTokens] = useState(0);
  const [isLoadingTokens, setIsLoadingTokens] = useState(true);
  
  // Use our new subscription balance hook
  const { 
    isLoading: isLoadingBalance, 
    balanceData, 
    hasPlan, 
    planName 
  } = useSubscriptionBalance();

  // Format tokens for display
  const formatTokensLocal = formatTokens;

  // Convert bytes to MB
  const bytesToMBLocal = formatMB;

  // Fetch token usage data
  useEffect(() => {
    const fetchTokenUsage = async () => {
      setIsLoadingTokens(true);
      try {
        const response = await fetch('/api/dashboard/token-usage');
        if (response.ok) {
          const data = await response.json();
          
          if (data.dailyTokens && Array.isArray(data.dailyTokens) && data.dailyTokens.length > 0) {
            // Process the data
            const formattedData = data.dailyTokens
              .map((item: { created_at: string; tokens: number | string }) => {
                // Ensure created_at is a valid date string
                let dateObj;
                try {
                  dateObj = new Date(item.created_at);
                  if (isNaN(dateObj.getTime())) {
                    return null; // Skip invalid dates
                  }
                } catch (e) {
                  return null; // Skip invalid dates
                }
                
                // Format the date for display
                const day = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
                
                // Ensure tokens is a number
                const tokens = typeof item.tokens === 'number' 
                  ? item.tokens 
                  : parseInt(item.tokens as string, 10) || 0;
                
                return {
                  day,
                  tokens,
                  date: dateObj
                };
              })
              .filter(Boolean) // Remove null entries
              .sort((a, b) => a!.date.getTime() - b!.date.getTime()); // Sort by date
            
            setTokenUsageData(formattedData as TokenUsageDataPoint[]);
            
            // Calculate total and average tokens
            const total = formattedData.reduce((sum, item) => sum + (item?.tokens || 0), 0);
            setTotalTokens(total);
            setAvgTokens(Math.round(total / formattedData.length));
          }
        }
      } catch (error) {
        console.error('Error fetching token usage:', error);
      } finally {
        setIsLoadingTokens(false);
      }
    };
    
    fetchTokenUsage();
  }, []);

  // Fetch revenue data
  const fetchRevenue = async () => {
    setIsLoadingRevenue(true);
    try {
      const response = await fetch('/api/revenue/daily');
      if (response.ok) {
        const data = await response.json();
        if (data && data.dailyRevenue) {
          interface DailyRevenueItem {
            date: Date;
            name: string;
            revenue: number;
          }

          const formattedData = data.dailyRevenue
            .map((item: { created_at: string; amount: number }) => ({
              date: new Date(item.created_at),
              name: new Date(item.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
              revenue: item.amount
            }))
            .sort((a: DailyRevenueItem, b: DailyRevenueItem) => a.date.getTime() - b.date.getTime())
            .map(({ name, revenue }: DailyRevenueItem) => ({ name, revenue }));
          setRevenueData(formattedData);
          // Calculate total revenue from daily data
          const totalRevenue = formattedData.reduce((sum: number, item: RevenueDataPoint) => sum + item.revenue, 0);
          setRevenue(totalRevenue);
        } else {
          setRevenueData([]);
          setRevenue(0);
        }
      }
    } catch (error) {
      console.error('Error fetching revenue:', error);
      setRevenueData([]);
      setRevenue(0);
    } finally {
      setIsLoadingRevenue(false);
    }
  };

  // Fetch revenue data on component mount
  useEffect(() => {
    fetchRevenue();
  }, []);

  // Set document title
  useEffect(() => {
    document.title = 'Analytics | BuildThatIdea';
  }, []);

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Analytics</h1>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <TokensCard 
          tokensUsed={balanceData.tokensUsed}
          tokensTotal={balanceData.tokensTotal}
          tokensPercentage={balanceData.tokensPercentage}
          isLoading={isLoadingBalance}
        />
        <AgentsCard 
          agentsUsed={balanceData.agentsUsed}
          agentsTotal={balanceData.agentsTotal}
          agentsPercentage={balanceData.agentsPercentage}
          isLoading={isLoadingBalance}
        />
        <KnowledgeBaseCard 
          knowledgeBaseUsed={balanceData.knowledgeBaseUsed}
          knowledgeBaseTotal={balanceData.knowledgeBaseTotal}
          knowledgeBasePercentage={balanceData.knowledgeBasePercentage}
          isLoading={isLoadingBalance}
        />
        <RevenueCard 
          revenue={revenue}
          isLoading={isLoadingRevenue}
        />
      </div>

      {/* Token Usage Chart */}
      <Card className="border shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <CardTitle className="text-lg">Daily Token Usage</CardTitle>
              <CardDescription>Token consumption over time</CardDescription>
            </div>
            {!isLoadingTokens && (
              <div className="text-right">
                <p className="text-sm font-medium">Total Usage</p>
                <p className="text-xl font-bold text-purple-600">
                  {totalTokens.toLocaleString()} tokens
                </p>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] sm:h-[350px] w-full mt-2">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={tokenUsageData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                barSize={40}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" vertical={false} />
                <XAxis
                  dataKey="day"
                  tick={{ fontSize: 12, fill: '#64748b' }}
                  tickLine={false}
                  axisLine={false}
                  dy={10}
                />
                <YAxis
                  tick={{ fontSize: 12, fill: '#64748b' }}
                  tickFormatter={(value) => value >= 1000 ? `${value / 1000}k` : value}
                  tickLine={false}
                  axisLine={false}
                  dx={-10}
                  width={60}
                />
                <Tooltip
                  formatter={(value) => [`${value.toLocaleString()} tokens`, 'Usage']}
                  contentStyle={{
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    padding: '8px 12px',
                    fontSize: '12px'
                  }}
                  cursor={{ fill: 'rgba(136, 132, 216, 0.1)' }}
                  labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                />
                <Bar
                  dataKey="tokens"
                  fill="#8884d8"
                  radius={[4, 4, 0, 0]}
                  name="Token Usage"
                  animationDuration={800}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-between items-center mt-4 text-sm text-muted-foreground border-t border-border pt-3">
            {isLoadingTokens ? (
              <>
                <div><span className="block h-4 w-24 bg-muted animate-pulse rounded"></span></div>
                <div><span className="block h-4 w-24 bg-muted animate-pulse rounded"></span></div>
              </>
            ) : (
              <>
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                  <span>Daily Token Usage</span>
                </div>
                <div>Daily Avg: {avgTokens.toLocaleString()} tokens</div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Revenue Chart */}
      <Card className="border shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <CardTitle className="text-lg">Daily Revenue</CardTitle>
              <CardDescription>Revenue trends over time</CardDescription>
            </div>
            {!isLoadingRevenue && (
              <div className="text-right">
                <p className="text-sm font-medium">Total Revenue</p>
                <p className="text-xl font-bold text-emerald-600">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(revenue)}
                </p>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] sm:h-[350px] w-full mt-2">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={revenueData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" vertical={false} />
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12, fill: '#64748b' }}
                  tickLine={false}
                  axisLine={false}
                  dy={10}
                />
                <YAxis
                  tick={{ fontSize: 12, fill: '#64748b' }}
                  tickFormatter={(value) => new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(value)}
                  tickLine={false}
                  axisLine={false}
                  dx={-10}
                  width={80}
                />
                <Tooltip
                  formatter={(value: number) => [
                    new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD',
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(value),
                    'Revenue'
                  ]}
                  contentStyle={{
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    padding: '8px 12px',
                    fontSize: '12px'
                  }}
                  cursor={{ stroke: 'rgba(16, 185, 129, 0.3)', strokeWidth: 2 }}
                  labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#10b981"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#10b981', strokeWidth: 2, stroke: '#fff' }}
                  activeDot={{ r: 6, fill: '#10b981', stroke: '#fff', strokeWidth: 2 }}
                  name="Daily Revenue"
                  animationDuration={1000}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-between items-center mt-4 text-sm text-muted-foreground border-t border-border pt-3">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
              <span>Daily Revenue</span>
            </div>
            <div>Daily Avg: {
              new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(revenueData.length > 0 ? revenue / revenueData.length : 0)
            }</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
