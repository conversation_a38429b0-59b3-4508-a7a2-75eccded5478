import {
  createDataStreamResponse,
  streamText,
  smoothStream,
  type UIMessage,
  appendResponseMessages,
} from 'ai';
import { auth } from '@/auth';
import { db } from '@/lib/db/client';
import {
  saveChat,
  getChatById,
  saveMessages,
  deleteChatById,
  updateMessageTokens,
} from '@/lib/db/queries';
import { generateTitleFromUserMessage } from '@/app/chat/actions';
import { systemPrompt } from '@/lib/ai/prompts';
import { myProvider } from '@/lib/ai/providers';
import {
  generateUUID,
  getMostRecentUserMessage,
  getTrailingMessageId,
  decrypt,
} from '@/lib/utils';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { eq } from 'drizzle-orm';
import { agents, chat, message } from '@/lib/db/schema';
import {
  countConversationTokens,
  convertUIMessageForTokenCounting,
} from '@/lib/ai/tokens';
import { DEFAULT_CHAT_MODEL, allChatModels } from '@/lib/ai/models';
import {
  getOrCreateUserSubscription,
  decrementFreeMessages,
} from '@/lib/db/subscription-queries';
import { Index } from '@upstash/vector';
import { OpenAI } from 'openai';

import logger from '@/lib/logger';

export const maxDuration = 60;

// extra Quality of interaction instructions
const EXTRA_INSTRUCTION = `Dynamically adapt your communication style to match the persona established in the system prompt:
      
      1. TONE ANALYSIS: During initial exchanges, identify key communication patterns from the system prompt:
         - Extract characteristic vocabulary, sentence structures, and rhetorical devices
         - Identify formality level, technical depth, and communication values (brevity vs. elaboration)
         - Note distinctive metaphors, analogies, or reference frameworks
      
      2. PERSONA CONSISTENCY: Maintain consistent characterization throughout the conversation:
         - Preserve established expertise domains and knowledge boundaries
         - Apply consistent decision-making frameworks and reasoning patterns
         - Maintain any character-specific viewpoints or philosophical stances
      
      3. ADAPTIVE CALIBRATION: Continuously refine the persona expression based on:
         - User feedback and engagement signals
         - Conversation context and complexity requirements
         - Need for clarity vs. stylistic authenticity
      
      4. PURPOSEFUL INTERACTION: While maintaining authentic persona expression:
         - Identify the user's underlying goals beyond stated questions
         - Provide substantive value appropriate to the persona's expertise
         - When conversation continuation is beneficial, offer insights or questions aligned with the persona's communication style
      
      Balance authentic persona representation with delivering meaningful value aligned with the user's objectives. Avoid superficial imitation in favor of capturing the essence of the established character while maintaining natural conversation flow.`;

// Initialize OpenAI client for embeddings
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Helper function to perform similarity search
async function performSimilaritySearch({
  query,
  endpoint,
  token,
  agentId,
  topK = 5,
}: {
  query: string;
  endpoint: string;
  token: string;
  agentId: string;
  topK?: number;
}) {
  try {
    logger.info('[SIMILARITY SEARCH] Starting search', {
      query: `${query.substring(0, 50)}...`,
      agentId,
      topK,
    });

    // Create embedding from the query
    const embeddingResponse = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: query,
    });

    const queryEmbedding = embeddingResponse.data[0].embedding;
    logger.info('[SIMILARITY SEARCH] Created embedding', {
      dimension: queryEmbedding.length,
    });

    // Decrypt token and initialize Upstash Vector client
    const decryptedToken = decrypt(token);
    const formattedEndpoint = endpoint.startsWith('http')
      ? endpoint
      : `https://${endpoint}`;

    const vectorIndex = new Index({
      url: formattedEndpoint,
      token: decryptedToken,
    });

    // First, try searching in the agent's namespace
    logger.info('[SIMILARITY SEARCH] Searching in namespace', { agentId });
    let results = await vectorIndex.namespace(agentId).query({
      vector: queryEmbedding,
      topK,
      includeMetadata: true,
    });

    // If no results found in the namespace, fall back to default namespace with filter
    if (results.length === 0) {
      logger.info(
        '[SIMILARITY SEARCH] No results in namespace; falling back to default namespace',
      );
      results = await vectorIndex.query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
        filter: `agentId = '${agentId}'`,
      });

      if (results.length > 0) {
        logger.info('[SIMILARITY SEARCH] Found results in default namespace');
      } else {
        logger.info(
          '[SIMILARITY SEARCH] No results found in default namespace either',
        );
      }
    } else {
      logger.info('[SIMILARITY SEARCH] Found results in namespace', {
        count: results.length,
      });
    }

    logger.info('[SIMILARITY SEARCH] Total results found', {
      count: results.length,
      results,
    });

    return results;
  } catch (error) {
    logger.error('[SIMILARITY SEARCH] Error', {
      error: error instanceof Error ? error.message : error,
    });
    return [];
  }
}

// Define the type for the response object from the Vercel AI SDK
interface AIResponse {
  messages: any[];
  body?: unknown;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
}

// Helper function to log benchmarking information
function logBenchmark(
  label: string,
  startTime: number,
  additionalInfo: Record<string, any> = {},
) {
  const currentTime = performance.now();
  const elapsed = currentTime - startTime;
  logger.info(`[BENCHMARK] ${label}`, {
    elapsed: `${elapsed.toFixed(2)} ms`,
    ...additionalInfo,
  });
  return currentTime;
}

export async function POST(request: Request) {
  const startTime = performance.now();
  logger.info('[API:POST] Request received');

  try {
    // 1. Parse request body
    const parseStart = performance.now();
    const {
      id,
      messages,
      selectedChatModel: requestedModel,
      agentId,
      agentData,
      chatData,
      subscriptionData,
    }: {
      id: string;
      messages: Array<UIMessage & { warning?: any }>;
      selectedChatModel: string;
      agentId?: string;
      agentData?: {
        accessLevel: string;
        instruction?: string;
        endpoint?: string;
        token?: string;
        selectedLinks?: boolean;
      };
      chatData?: {
        userId: string;
        title: string;
        exists: boolean;
      };
      subscriptionData?: {
        isPaid: boolean;
        remainingMessages: number;
        expiryDate?: string;
        isCanceled?: boolean;
      };
    } = await request.json();
    logBenchmark('Parse request body', parseStart);

    // 2. Validate model
    let modelToUse = requestedModel;
    const isValidModel = allChatModels.some(
      (model) => model.id === requestedModel,
    );
    if (!isValidModel) {
      modelToUse = DEFAULT_CHAT_MODEL;
      logger.info('[MODEL] Invalid model requested; using default', {
        requestedModel,
        fallback: DEFAULT_CHAT_MODEL,
      });
    }

    // 3. Authenticate
    const authStart = performance.now();
    const session = await auth();
    logBenchmark('Authentication', authStart);

    if (!session || !session.user || !session.user.id) {
      logger.info('[API:POST] Unauthorized');
      return new Response('Unauthorized', { status: 401 });
    }

    // 4. Extract most recent user message
    const userMessage = getMostRecentUserMessage(messages);
    if (!userMessage) {
      logger.info('[API:POST] No user message found');
      return new Response('No user message found', { status: 400 });
    }

    // 5. Resolve agent & subscription
    let agent = null;
    let subscription: any = null;
    let isFreeAgent = true;
    if (agentId) {
      if (agentData) {
        logger.info('[API:POST] Using provided agent data', { agentId });
        agent = {
          id: agentId,
          accessLevel: agentData.accessLevel,
          instruction: agentData.instruction,
          endpoint: agentData.endpoint,
          token: agentData.token,
          selectedLinks: agentData.selectedLinks,
        };
      } else {
        const fetchAgentStart = performance.now();
        [agent] = await db.select().from(agents).where(eq(agents.id, agentId));
        logBenchmark('Fetch agent', fetchAgentStart);

        if (!agent) {
          logger.info('[API:POST] Agent not found', { agentId });
          return new Response('Agent not found', { status: 404 });
        }
      }

      isFreeAgent = agent.accessLevel === 'free';
      if (!isFreeAgent) {
        if (subscriptionData) {
          logger.info('[API:POST] Using provided subscription data', {
            agentId,
          });
          subscription = {
            isPaid: subscriptionData.isPaid,
            remainingMessages: subscriptionData.remainingMessages,
            expiryDate: subscriptionData.expiryDate,
            isCanceled: subscriptionData.isCanceled,
          };
        } else {
          const checkSubStart = performance.now();
          subscription = await getOrCreateUserSubscription({
            userId: session.user.id,
            agentId,
          });
          logBenchmark('Check subscription', checkSubStart);
        }

        const subData = Array.isArray(subscription)
          ? subscription[0]
          : subscription;

        let remainingMessages = 0;
        if (typeof subData === 'object' && subData !== null) {
          if (
            'remainingMessages' in subData &&
            typeof subData.remainingMessages === 'number'
          ) {
            remainingMessages = subData.remainingMessages;
          } else if (
            'allowedFreeMessages' in subData &&
            typeof (subData as any).allowedFreeMessages === 'number'
          ) {
            remainingMessages = (subData as any).allowedFreeMessages;
          }
        }

        // Check if user has exactly 0 messages remaining (not <= 0)
        if (remainingMessages === 0) {
          const responseHeaders = new Headers();
          responseHeaders.append('X-Remaining-Messages', '0');
          responseHeaders.append('X-Messages-Exhausted', 'true');
          responseHeaders.append(
            'X-Exhausted-Message',
            'You have reached your free message limit. Please subscribe to continue using this agent.',
          );

          logger.info('[API:POST] Free messages exhausted for user', {
            userId: session.user.id,
            agentId,
          });
          return new Response(
            JSON.stringify({
              id: 'exhausted',
              role: 'assistant',
              content:
                'You have reached your free message limit. Please subscribe to continue using this agent.',
            }),
            {
              status: 200,
              headers: responseHeaders,
            },
          );
        }
      }
    }

    // 6. Resolve existing chat or mark as new
    let existingChat: {
      userId: string;
      title: string;
      agentId: string;
    } | null = null;

    if (chatData) {
      logger.info('[API:POST] Using provided chat data', { chatId: id });
      existingChat = chatData.exists
        ? {
            userId: chatData.userId,
            title: chatData.title,
            agentId: (chatData as any).agentId || agentId || '',
          }
        : null;
    } else {
      const chatFetchStart = performance.now();
      const fetchedChat = await getChatById({ id });
      if (fetchedChat) {
        existingChat = {
          userId: fetchedChat.userId || '',
          title: fetchedChat.title || '',
          agentId: fetchedChat.agentId || '',
        };
      } else {
        existingChat = null;
      }
      logBenchmark('Fetch existing chat', chatFetchStart);
    }

    let isNewChat = false;
    let chatDataForSaving: {
      id: string;
      userId: string;
      title: string;
      agentId: string;
    };

    if (!existingChat) {
      isNewChat = true;
      if (!session.user?.id) {
        logger.error('[CHAT] Missing user ID in session', { session });
        throw new Error('User ID is required to create a chat');
      }
      if (!agentId) {
        logger.error('[CHAT] Missing agent ID', { agentId });
        throw new Error('Agent ID is required to create a chat');
      }
      const placeholderTitle = 'New Chat';
      logger.info('[CHAT] Will create new chat with placeholder title', {
        chatId: id,
        title: placeholderTitle,
        isNewChat,
      });
      chatDataForSaving = {
        id,
        userId: session.user.id,
        title: placeholderTitle,
        agentId,
      };
    } else {
      isNewChat = false;
      logger.info('[CHAT] Using existing chat', {
        chatId: id,
        isNewChat,
      });
      chatDataForSaving = {
        id,
        userId: existingChat.userId || session.user?.id || '',
        title: existingChat.title || `Chat ${new Date().toLocaleString()}`,
        agentId: existingChat.agentId || agentId || '',
      };
    }

    // 7. Prepare user message payload (not yet saved to DB)
    const messageToSave = userMessage
      ? {
          chatId: id,
          id: userMessage.id,
          role: 'user',
          parts: userMessage.parts,
          attachments: userMessage.experimental_attachments ?? [],
          createdAt: new Date(),
          tokens: 0, // we’ll update this later
        }
      : null;

    // 8. (No pre‐stream DB writes here! We want to start streaming immediately.)

    // 9. Start token counting in the background (optional)
    const countTokensStart = performance.now();
    logger.info('[TOKENS] Kicking off asynchronous token counting');
    Promise.resolve().then(async () => {
      try {
        const msgsForCounting = messages.map(convertUIMessageForTokenCounting);
        const { promptTokens } = countConversationTokens(
          msgsForCounting,
          modelToUse,
        );
        if (promptTokens > 0 && userMessage) {
          // Only update user message tokens *after* message is inserted (in onFinish)
          // We’ll update based on the real message row ID.
          logger.info(
            '[TOKENS] Computed prompt tokens (to be saved later):',
            promptTokens,
          );
        }
        const tokenDuration = performance.now() - countTokensStart;
        logger.info('[TOKENS] Finished computing tokens in background', {
          durationMs: tokenDuration,
        });
      } catch (error) {
        logger.error('[TOKENS] Error computing tokens', {
          error: error instanceof Error ? error.message : error,
        });
      }
    });
    logBenchmark('Initiated async token counting', countTokensStart);

    // 10. Perform similarity search if applicable (before streaming)
    let similaritySearchResults: any[] = [];
    const hasVectorIndex = agent?.endpoint && agent.token;
    const hasSelectedLinks = agent?.selectedLinks;

    if (hasVectorIndex && hasSelectedLinks) {
      const similaritySearchStart = performance.now();
      similaritySearchResults = await performSimilaritySearch({
        query: userMessage.content,
        endpoint: agent.endpoint!,
        token: agent.token!,
        agentId: agent.id!,
        topK: 5,
      });
      logBenchmark('Similarity search', similaritySearchStart);
      logger.info('[SIMILARITY SEARCH] Completed search', {
        totalResults: similaritySearchResults.length,
      });
    } else {
      logger.info(
        '[SIMILARITY SEARCH] Skipped—missing endpoint/token or no selected links',
      );
    }

    // 11. Create data stream response (stream immediately, no pre-stream DB writes)
    const dataStream = new TransformStream();
    const responseHeaders = new Headers();

    const preProcessingTime = logBenchmark(
      'Pre-processing time (before createDataStreamResponse)',
      startTime,
    );

    return createDataStreamResponse({
      execute: (dataStream) => {
        //
        // 11.a. Build system prompt (include context if present)
        //
        const baseSystemPrompt =
          agent?.instruction || systemPrompt({ selectedChatModel: modelToUse });
        let systemMsg = `${baseSystemPrompt}\n\n${EXTRA_INSTRUCTION}`;
        let contextAdded = false;

        if (similaritySearchResults.length > 0) {
          systemMsg += `
      
      ## Context Usage Guidelines
      
      You have access to context from the knowledge base. Use it ONLY when appropriate:
      
      **DO NOT USE CONTEXT FOR:**
      - Simple greetings (Hi, Hello, How are you)
      - Casual conversation
      - General questions that don't require specific knowledge
      - Questions about yourself or your capabilities
      
      **USE CONTEXT WHEN:**
      - User asks specific questions about topics in the knowledge base
      - User requests detailed information
      - Questions clearly relate to the provided context
      
      **WHEN USING CONTEXT:**
      - Only cite information that directly answers the question
      - Be precise and relevant
      - Don't force context into responses where it's not needed
      
      ## Available Context:
      ${similaritySearchResults
        .map((result, index) => {
          const text = result.metadata?.text || 'No text available';
          return `[${index + 1}] ${text}`;
        })
        .join('\n\n')}
      
      Remember: Most conversations don't need context. Only use it when the user's question specifically requires information from the knowledge base.`;

          contextAdded = true;
          logger.info('[CONTEXT] Added search results to system prompt', {
            resultCount: similaritySearchResults.length,
            promptLength: systemMsg.length,
            snippet: systemMsg.substring(
              baseSystemPrompt.length,
              baseSystemPrompt.length + 200,
            ),
          });
        } else {
          logger.info('[CONTEXT] No context added', {
            reason:
              similaritySearchResults.length === 0
                ? 'No search results'
                : 'Skipped',
          });
        }

        logger.info('[AI MODEL] Preparing to send request', {
          model: modelToUse,
          contextIncluded: contextAdded,
          systemPromptType: contextAdded ? 'Base + Context' : 'Base only',
          userMessage:
            userMessage.content.substring(0, 100) +
            (userMessage.content.length > 100 ? '...' : ''),
        });

        let remainingMessagesCount = 0;
        if (!isFreeAgent && subscription && typeof subscription === 'object') {
          if (
            'remainingMessages' in subscription &&
            typeof subscription.remainingMessages === 'number'
          ) {
            remainingMessagesCount = subscription.remainingMessages;
          } else if (
            'allowedFreeMessages' in subscription &&
            typeof (subscription as any).allowedFreeMessages === 'number'
          ) {
            remainingMessagesCount = (subscription as any).allowedFreeMessages;
          }
        }

        if (!isFreeAgent && remainingMessagesCount > 0) {
          responseHeaders.append(
            'X-Remaining-Messages',
            remainingMessagesCount.toString(),
          );
        }

        const beforeModelCallTime = logBenchmark(
          'Time before model call',
          preProcessingTime,
        );
        logger.info('[AI MODEL] Final request to AI', {
          contextStatus: contextAdded ? 'INCLUDED' : 'NOT INCLUDED',
          resultCount: similaritySearchResults.length,
        });

        //
        // 11.b. Start the AI stream
        //
        const result = streamText({
          model: myProvider.languageModel(modelToUse),
          system: systemMsg,
          messages,
          maxSteps: 5,
          experimental_activeTools:
            modelToUse === 'chat-model-reasoning'
              ? []
              : [
                  'getWeather',
                  'createDocument',
                  'updateDocument',
                  'requestSuggestions',
                ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          tools: {
            getWeather,
            createDocument: createDocument({ session, dataStream }),
            updateDocument: updateDocument({ session, dataStream }),
            requestSuggestions: requestSuggestions({
              session,
              dataStream,
            }),
          },
          onFinish: async ({ response }: { response: AIResponse }) => {
            //
            // 11.c. Once streaming is done, perform all DB writes IN ORDER:
            //       c2 → c2 → c5 → c4 → c3
            //
            //   c2: save or update chat
            //   c2: save user message
            //   c5: save assistant reply
            //   c4: generate & update title
            //   c3: decrement free messages
            //
            try {
              // ─── c2.1: SAVE CHAT (if new) ─────────────────────────────────
              if (isNewChat) {
                await saveChat(chatDataForSaving);
                logger.info('[DB] saveChat completed', { chatId: id });
              }

              // ─── c2.2: SAVE USER MESSAGE ─────────────────────────────────
              if (messageToSave) {
                await saveMessages({ messages: [messageToSave] });
                logger.info('[DB] saveMessages(user) completed', {
                  messageId: messageToSave.id,
                });

                // Update the user message token count now that the row exists
                try {
                  const msgsForCounting = messages.map(
                    convertUIMessageForTokenCounting,
                  );
                  const { promptTokens } = countConversationTokens(
                    msgsForCounting,
                    modelToUse,
                  );
                  if (promptTokens > 0) {
                    await updateMessageTokens({
                      messageId: userMessage.id,
                      tokens: promptTokens,
                    });
                    logger.info('[TOKENS] Updated user message tokens', {
                      messageId: userMessage.id,
                      tokens: promptTokens,
                    });
                  }
                } catch (tokenError) {
                  logger.error('[TOKENS] Error updating user tokens', {
                    error:
                      tokenError instanceof Error
                        ? tokenError.message
                        : tokenError,
                  });
                }
              }

              // ─── c5: SAVE ASSISTANT REPLY ──────────────────────────────────
              try {
                // Short delay so that the final assistant message has an ID
                await new Promise((res) => setTimeout(res, 10));

                const assistantArray = response.messages.filter(
                  (m) => m.role === 'assistant',
                );
                logger.info('[CHAT] Assistant messages to save', {
                  count: assistantArray.length,
                  hasIds: assistantArray.map((m) => !!m.id),
                  messageIds: assistantArray.map((m) => m.id || 'missing'),
                });

                const assistantId = getTrailingMessageId({
                  messages: assistantArray,
                });
                if (!assistantId) {
                  throw new Error('No assistant message ID found');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [userMessage],
                  responseMessages: response.messages,
                });
                if (!assistantMessage) {
                  throw new Error('Failed to append assistant response');
                }

                let completionTokensCount = 0;
                if (response.usage?.totalTokens) {
                  completionTokensCount = response.usage.completionTokens || 0;
                  const rawPromptTokens = response.usage.promptTokens || 0;
                  if (rawPromptTokens > 0) {
                    await updateMessageTokens({
                      messageId: userMessage.id,
                      tokens: rawPromptTokens,
                    });
                    logger.info('[TOKENS] Updated user message tokens', {
                      messageId: userMessage.id,
                      tokens: rawPromptTokens,
                    });
                  }
                } else {
                  const msgsForCount = [
                    ...messages.map(convertUIMessageForTokenCounting),
                    convertUIMessageForTokenCounting(assistantMessage),
                  ];
                  completionTokensCount = countConversationTokens(
                    msgsForCount,
                    modelToUse,
                  ).completionTokens;
                }

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts || [],
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                      tokens: completionTokensCount,
                    },
                  ],
                });
                logger.info('[SAVE] Assistant message saved successfully', {
                  messageId: assistantId,
                  tokens: completionTokensCount,
                });
              } catch (err) {
                logger.error('[ASYNC] Failed to save assistant reply', {
                  error: err instanceof Error ? err.message : err,
                });
              }

              // ─── c4: GENERATE & UPDATE CHAT TITLE ─────────────────────────
              if (isNewChat && userMessage && session.user?.id) {
                try {
                  let newTitle = '';
                  const titleStart = performance.now();
                  logger.info('[CHAT] Starting title generation');

                  // First attempt
                  newTitle = await generateTitleFromUserMessage({
                    message: userMessage,
                  });
                  if (!newTitle) {
                    // Retry once
                    logger.info(
                      '[CHAT] Title generation returned empty, retrying',
                    );
                    newTitle = await generateTitleFromUserMessage({
                      message: userMessage,
                    });
                  }

                  if (!newTitle) {
                    newTitle = `Chat on ${new Date()
                      .toISOString()
                      .slice(0, 19)
                      .replace('T', ' ')}`;
                  }

                  await db
                    .update(chat)
                    .set({ title: newTitle })
                    .where(eq(chat.id, id));
                  const titleDuration = performance.now() - titleStart;
                  logger.info('[CHAT] Title updated in database', {
                    chatId: id,
                    title: newTitle,
                    durationMs: titleDuration,
                  });
                } catch (dbError) {
                  logger.error('[CHAT] Failed title generation/update', {
                    error: dbError instanceof Error ? dbError.message : dbError,
                    chatId: id,
                  });
                }
              }

              // ─── c3: DECREMENT FREE MESSAGES ───────────────────────────────
              if (!isFreeAgent && session.user?.id && agentId) {
                try {
                  await decrementFreeMessages({
                    userId: session.user.id,
                    agentId,
                  });
                  logger.info('[ASYNC] Free message count decremented', {
                    userId: session.user.id,
                    agentId,
                  });
                } catch (err) {
                  logger.error('[ASYNC] Failed to decrement free messages', {
                    error: err instanceof Error ? err.message : err,
                  });
                }
              }

              logger.info(
                '[ASYNC] onFinish DB writes completed in order c2→c2→c5→c4→c3',
              );
            } catch (errorInFinish) {
              logger.error('[onFinish] Unexpected error', {
                error:
                  errorInFinish instanceof Error
                    ? errorInFinish.message
                    : errorInFinish,
              });
            }
          },
        });

        // 11.d. Pipe the AI stream into our dataStream so the client sees it immediately
        result.mergeIntoDataStream(dataStream);
        logBenchmark('Stream initiated', beforeModelCallTime);

        // Returning from execute() immediately lets the streaming begin
      },
      headers: responseHeaders,
    });
  } catch (error) {
    logBenchmark('Error occurred', startTime, {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    logger.error('[API:POST] Error processing chat request', {
      error: error instanceof Error ? error.message : error,
    });
    return new Response('Error processing chat request', { status: 500 });
  }
}

export async function DELETE(request: Request) {
  const deleteStart = performance.now();
  logger.info('[API:DELETE] Request received');

  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    logger.info('[API:DELETE] No ID provided');
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();
  if (!session || !session.user) {
    logger.info('[API:DELETE] Unauthorized');
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chatRecord = await getChatById({ id });
    if (chatRecord.userId !== session.user.id) {
      logger.info('[API:DELETE] Unauthorized: chat user mismatch', {
        chatId: id,
        userId: session.user.id,
      });
      return new Response('Unauthorized', { status: 401 });
    }

    await deleteChatById({ id });
    logBenchmark('Delete chat', deleteStart);
    logger.info('[API:DELETE] Chat deleted', { chatId: id });
    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    logger.error('[API:DELETE] Error deleting chat', {
      error: error instanceof Error ? error.message : error,
    });
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
