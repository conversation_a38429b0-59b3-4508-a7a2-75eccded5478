// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // In production, reduce the trace sample rate for better performance
  // 0.1 means 10% of transactions will be captured
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  // Disable debug mode in production
  debug: process.env.NODE_ENV !== 'production',
  
  // Set environment explicitly
  environment: process.env.NODE_ENV || 'development',
  
  // Add release information for better error tracking
  release: process.env.VERCEL_GIT_COMMIT_SHA || process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || 'local-development',
  
  // Configure server-specific settings
  serverName: process.env.VERCEL_URL || 'local-server',
  
  // Capture IP addresses for better user tracking
  sendDefaultPii: true,
  
  // Set maximum breadcrumbs to keep memory usage reasonable
  maxBreadcrumbs: 50,
});
