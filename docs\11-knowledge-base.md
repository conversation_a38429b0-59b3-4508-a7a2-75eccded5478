# Knowledge Base Implementation

## Overview

The knowledge base system in the BuildThatIdea platform enables AI agents to access and utilize specific information provided by their creators. This document details the complete knowledge base implementation, including file processing, storage, and retrieval mechanisms.

## Technology Stack

- **AWS S3**: Cloud storage for uploaded knowledge base files
- **Upstash Vector**: Vector database for storing and querying embeddings
- **Next.js API Routes**: Backend endpoints for file processing
- **Various File Processors**: Libraries for extracting text from different file formats

## Knowledge Base Components

### 1. File Upload and Storage

The knowledge base system allows creators to upload various file types:

1. **Supported File Types**:
   - PDF (`.pdf`)
   - Word Documents (`.docx`)
   - Text Files (`.txt`)
   - CSV Files (`.csv`)
   - Markdown Files (`.md`)

2. **File Storage**:
   - Files are uploaded directly from the client browser to AWS S3 using presigned URLs
   - Metadata is stored in the PostgreSQL database
   - File references are associated with specific agents
   - Upload progress is tracked in real-time on the client side

### 2. Text Extraction

The system extracts text content from various file formats:

1. **PDF Processing**: Using `pdf2json` library
2. **DOCX Processing**: Using `mammoth` library
3. **CSV Processing**: Using `papaparse` library
4. **TXT/MD Processing**: Direct text extraction

### 3. Text Chunking

Extracted text is split into manageable chunks for embedding:

1. **Chunk Size**: Typically 1000-1500 characters
2. **Chunk Overlap**: Overlap between chunks to maintain context
3. **Metadata**: Each chunk maintains metadata about its source

### 4. Vector Embeddings

Text chunks are converted to vector embeddings:

1. **Embedding Generation**: Using AI models to generate vector representations
2. **Vector Storage**: Storing vectors in Upstash Vector
3. **Metadata Storage**: Storing metadata with each vector for retrieval

### 5. Retrieval System

The system retrieves relevant information based on user queries:

1. **Query Embedding**: Converting user queries to vector embeddings
2. **Similarity Search**: Finding similar vectors in the database
3. **Content Retrieval**: Retrieving and returning the most relevant content

## Database Schema

The knowledge base system uses several database tables:

### Agent Knowledge Files

```typescript
export const agentKnowledgeFiles = pgTable("agent_knowledge_files", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  agentId: uuid("agent_id").notNull().references(() => agents.id, { onDelete: 'cascade' }),
  fileName: text("file_name").notNull(),
  fileUrl: text("file_url").notNull(),
  fileSize: integer("file_size"),
  fileType: text("file_type"),
  status: text("status").default("pending"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  processingError: text("processing_error"),
  chunkCount: integer("chunk_count").default(0),
  vectorized: boolean("vectorized").default(false),
});
```

### Agent Vector Index Information

Vector index information is stored in the agents table:

```typescript
// Relevant fields in the agents table
endpoint: text('endpoint'),  // Vector database endpoint
token: text('token'),        // Encrypted vector database token
```

## Implementation Details

### 1. File Upload Process

The file upload process now uses client-side direct uploads to S3:

```typescript
// Client-side implementation in agent-appearance-form.tsx
const handleFileUpload = useCallback(async (files: File[]) => {
  try {
    setIsUploading(true);
    
    // Create temporary file entries with progress tracking
    const tempFileEntries = files.map(file => ({
      id: uuidv4(),
      name: file.name,
      file,
      status: 'uploading' as const,
      progress: 0,
    }));
    
    // Request presigned URLs from the API
    const presignedUrlResponse = await fetch('/api/agents/generate-presigned-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        agentId: draftAgentId,
      }),
    });
    
    const presignedData = await presignedUrlResponse.json();
    const { presignedUrl, fileUrl } = presignedData;
    
    // Upload directly to S3 with progress tracking
    const xhr = new XMLHttpRequest();
    xhr.upload.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentComplete = Math.round((event.loaded / event.total) * 100);
        setUploadProgress((prev) => ({
          ...prev,
          [fileEntry.id]: percentComplete,
        }));
      }
    };
    
    // After successful upload, register with the API
    fetch('/api/agents/upload-knowledgebase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        agentId: draftAgentId,
        files: [
          {
            name: fileEntry.name,
            url: fileUrl,
            type: fileEntry.file?.type,
            size: fileEntry.file?.size,
          },
        ],
      }),
    });
    
    // Rest of the implementation...
  } catch (error) {
    console.error('Error uploading files:', error);
  }
}, [draftAgentId]);
```

The server-side API endpoint now primarily handles file registration and vector index generation:

```typescript
// Server-side implementation in upload-knowledgebase/route.ts
export async function POST(request: Request) {
  try {
    // Authentication check
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse the JSON request for direct S3 uploads
    const body = await request.json();
    const { agentId, files } = body;
    
    // Validate input
    if (!agentId || files.length === 0) {
      return NextResponse.json({ error: 'Invalid request' }, { status: 400 });
    }
    
    // Get the agent to check ownership
    const agent = await db.query.agents.findFirst({
      where: and(
        eq(agents.id, agentId),
        eq(agents.userId, session.user.id)
      ),
    });
    
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Register the files in the database
    const fileRecords = await Promise.all(
      files.map(async (file) => {
        return db.insert(agentKnowledgeFiles).values({
          agentId,
          fileName: file.name,
          fileUrl: file.url,
          fileSize: parseInt(file.size),
          fileType: file.type,
        });
      })
    );
    
    // Generate or update vector index if needed
    const vectorIndexInfo = await generateVectorIndexIfNeeded(agent);
    
    if (vectorIndexInfo) {
      // Update agent with vector index information
      await db.update(agents)
        .set({
          endpoint: vectorIndexInfo.endpoint,
          token: vectorIndexInfo.token,
        })
        .where(eq(agents.id, agentId));
    }
    
    return NextResponse.json({ 
      success: true, 
      vectorIndexAvailable: !!vectorIndexInfo 
    });
  } catch (error) {
    console.error('Error in upload-knowledgebase:', error);
    return NextResponse.json(
      { error: 'Failed to process file' },
      { status: 500 }
    );
  }
}
```

### 2. File Processing

The file processing logic extracts text from different file types:

```typescript
async function processAndUploadFile(file: File, agentId: string): Promise<FileUploadResult> {
  const fileName = file.name;
  const fileType = file.type;
  const fileSize = file.size;
  
  try {
    // Generate a unique file key
    const fileKey = `${agentId}/${uuidv4()}-${fileName}`;
    
    // Upload file to S3
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME!,
      Key: fileKey,
      Body: Buffer.from(await file.arrayBuffer()),
      ContentType: fileType,
    });
    
    await s3Client.send(uploadCommand);
    
    // Generate file URL
    const fileUrl = `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileKey}`;
    
    // Create file record in database
    const [newFile] = await db.insert(agentKnowledgeFiles)
      .values({
        agentId,
        fileName,
        fileUrl,
        fileSize,
        fileType,
        status: 'pending',
      })
      .returning();
    
    return {
      name: fileName,
      url: fileUrl,
      status: 'completed',
    };
  } catch (error) {
    console.error(`Error processing file ${fileName}:`, error);
    return {
      name: fileName,
      status: 'error',
      error: 'Failed to process file',
    };
  }
}
```

### 3. Vector Index Generation

The vector index generation is handled by the `generateVectorIndexIfNeeded` function:

```typescript
const generateVectorIndexIfNeeded = async (agent: any) => {
  // Check if agent already has valid endpoint and token
  const hasEndpoint = agent.endpoint && agent.endpoint.trim() !== '';
  const hasToken = agent.token && agent.token.trim() !== '';
  
  // If agent already has both endpoint and token, return the existing values
  if (hasEndpoint && hasToken) {
    console.log('Agent already has vector index, using existing one');
    return {
      endpoint: agent.endpoint,
      token: agent.token,
    };
  }
  
  console.log('Generating vector index for agent:', agent.id);
  
  try {
    // Call the generate-index API
    const indexResponse = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/agents/generate-index`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: agent.id,
        }),
      },
    );
    
    if (!indexResponse.ok) {
      throw new Error('Failed to generate vector index');
    }
    
    const indexData = await indexResponse.json();
    
    // Encrypt the token
    const encryptedToken = encrypt(indexData.token);
    
    return {
      endpoint: indexData.endpoint,
      token: encryptedToken,
    };
  } catch (error) {
    console.error('Error generating vector index:', error);
    return null;
  }
};
```

### 4. Knowledge Retrieval

The knowledge retrieval is implemented as an AI tool:

```typescript
export const getKnowledge = tool({
  description:
    "REQUIRED: You MUST call this tool for EVERY question before responding. This tool retrieves relevant information from the agent's knowledge base that you should use in your answer.",
  parameters: z.object({
    query: z.string().describe('The search query to find relevant information'),
  }),
  async handler({ query }) {
    try {
      // Get the current agent from context
      const agent = getAgent();
      
      if (!agent || !agent.endpoint || !agent.token) {
        return 'No knowledge base available for this agent.';
      }
      
      // Decrypt the token
      const decryptedToken = decrypt(agent.token);
      
      // Create Upstash Vector client
      const vectorClient = new UpstashVector({
        url: agent.endpoint,
        token: decryptedToken,
      });
      
      // Query the vector index
      const results = await vectorClient.query({
        vector: await generateEmbedding(query),
        topK: 5,
        includeMetadata: true,
        includeVectors: false,
      });
      
      if (!results || results.length === 0) {
        return 'No relevant information found in the knowledge base.';
      }
      
      // Format the results
      const formattedResults = results
        .map((result) => {
          const metadata = result.metadata as { text: string; source: string };
          return `Source: ${metadata.source}\n${metadata.text}`;
        })
        .join('\n\n---\n\n');
      
      return formattedResults;
    } catch (error) {
      console.error('Error retrieving knowledge:', error);
      return 'Error retrieving information from the knowledge base.';
    }
  },
});
```

## Embedding Process

The embedding process for knowledge base files only begins when the user clicks the "Create Agent" or "Update Agent" button:

1. Files are uploaded directly from the client to S3 using presigned URLs
2. A vector index is conditionally created if one doesn't already exist
3. When the "Create Agent" or "Update Agent" button is clicked, the embedding process starts
4. The user is redirected to a success page while embeddings are processed in the background

## Limitations and Considerations

The knowledge base system has several limitations and considerations:

1. **File Size Limits**: Maximum file size constraints
2. **Supported File Types**: Limited to specific file formats
3. **Text Extraction Quality**: Varies by file format and content
4. **Vector Database Capacity**: Limited by Upstash Vector capacity
5. **Query Complexity**: Simple queries work better than complex ones

## Fixed Issues

A notable issue that was fixed in the system involved the vector index generation:

Previously, the edit page wouldn't create a vector index if no files were uploaded during agent creation. This was fixed by simplifying the vector index generation logic in the upload-knowledgebase route to always call `generateVectorIndexIfNeeded`, which creates a vector index if one doesn't exist, regardless of whether there are files or whether it's the create or edit page.
