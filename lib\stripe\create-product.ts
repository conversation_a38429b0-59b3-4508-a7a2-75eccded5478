import Stripe from 'stripe';
import { db } from "@/lib/db";
import { agentStripeInfo } from "@/lib/db/schema";
import { and, eq } from 'drizzle-orm/expressions';
import { SQL } from 'drizzle-orm';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
});

export async function createProductAndPriceForAgentOnStripe(agentSlug: string, price: number, agentId: string, status: 'active' | 'inactive' = 'active') {
  
  try {
    // Create a product
    const product = await stripe.products.create({
      name: agentSlug,
      active: true
    });

    // Create a price for the product
    const priceObj = await stripe.prices.create({
      product: product.id,
      unit_amount: price,
      currency: 'usd',
      recurring: {
        interval: 'month'
      }
    });

    // Check if entry already exists
    const existingInfo = await db
      .select()
      .from(agentStripeInfo)
      .where(eq(agentStripeInfo.agentId, agentId))
      .limit(1);

    let result;
    if (existingInfo && existingInfo.length > 0) {
      // Update existing entry
      console.log('Updating existing agent stripe info for agent:', agentId);
      result = await updateAgentStripeInfoStatus(agentId, status);
    } else {
      // Insert new entry
      console.log('Creating new agent stripe info for agent:', agentId);
      result = await db.insert(agentStripeInfo).values({
        agentId: agentId,
        productId: product.id,
        priceId: priceObj.id,
        status: status
      }).returning();
    }
    return result;
  } catch (error) {
    console.error('Error creating product and price:', error);
    throw error;
  }
}

export async function updateAgentStripeInfoStatus(agentId: string, status: 'active' | 'inactive') {
  try {
    const updatedInfo = await db
      .update(agentStripeInfo)
      .set({ 
        status
      })
      .where(eq(agentStripeInfo.agentId, agentId))
      .returning();

    if (!updatedInfo || updatedInfo.length === 0) {
      throw new Error(`No agent_stripe_info found for agent ID: ${agentId}`);
    }

    console.log(`Updated agent_stripe_info status to ${status} for agent ID: ${agentId}`);
    return updatedInfo[0];
  } catch (error) {
    console.error('Error updating agent_stripe_info status:', error);
    throw error;
  }
}
