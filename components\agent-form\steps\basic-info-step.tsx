'use client';

import React, { useEffect, useState, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import type { UseFormReturn } from 'react-hook-form';
import { Check, AlertCircle, Loader2 } from 'lucide-react';
import type { FormValues } from '../types';
import { LogoUploader } from '../logo-uploader';

interface BasicInfoStepProps {
  form: UseFormReturn<FormValues, any, FormValues>;
  updateValidity?: (valid: boolean) => void;
  isCheckingSlug?: boolean;
  slugExists?: boolean;
  isOwnDraft?: boolean;
  logoPreview?: string | null;
  setLogoPreview?: (preview: string | null) => void;
  setLogoFile?: (file: File | null) => void;
  setLogoUrl?: (url: string | null) => void;
  isEditMode?: boolean;
  onUploadingLogoChange?: (isUploading: boolean) => void;
  draftAgentId?: string | null;
  loadSpecificDraft?: (draftId?: string, agentSlug?: string) => Promise<any>;
  resolveDraftIdFromSlug?: (slug: string) => Promise<string | null>;
}

export function BasicInfoStep({
  form,
  updateValidity,
  isCheckingSlug = false,
  slugExists = false,
  isOwnDraft = false,
  logoPreview: externalLogoPreview,
  setLogoPreview: externalSetLogoPreview,
  setLogoFile: externalSetLogoFile,
  setLogoUrl: externalSetLogoUrl,
  isEditMode = false,
  onUploadingLogoChange,
  draftAgentId,
  loadSpecificDraft,
  resolveDraftIdFromSlug,
}: BasicInfoStepProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isValid, setIsValid] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(
    externalLogoPreview || null,
  );
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [hasManuallyEditedSlug, setHasManuallyEditedSlug] = useState(() => {
    // Check on initialization if slug was manually edited
    const currentAgentName = form.getValues('agentName');
    const currentSlug = form.getValues('slug');
    
    if (currentAgentName && currentSlug) {
      const wouldBeGenerated = currentAgentName
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '');
      return currentSlug !== wouldBeGenerated;
    }
    return false;
  });

  // Use external state handlers if provided
  const handleLogoPreviewChange = (preview: string | null) => {
    setLogoPreview(preview);
    if (externalSetLogoPreview) externalSetLogoPreview(preview);
  };

  const handleLogoFileChange = (file: File | null) => {
    setLogoFile(file);
    if (externalSetLogoFile) externalSetLogoFile(file);
  };

  const handleLogoUrlChange = (url: string | null) => {
    if (externalSetLogoUrl) externalSetLogoUrl(url);
    if (url) {
      form.setValue('logo', url); // Set the logo field
    }
  };

  // Watch for changes in the form values
  const agentName = form.watch('agentName');
  const slug = form.watch('slug');
  const description = form.watch('description');

  // Check if slug was manually edited by comparing with what would be auto-generated
  const getGeneratedSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters but keep spaces
      .replace(/\s+/g, ''); // Then remove spaces
  };

  // Generate slug from agent name whenever agent name changes
  useEffect(() => {
    if (agentName && !hasManuallyEditedSlug) {
      const generatedSlug = getGeneratedSlug(agentName);
      
      form.setValue('slug', generatedSlug, {
        shouldValidate: true,
        shouldDirty: false, // Don't mark as dirty for auto-generation
      });
    }
  }, [agentName, hasManuallyEditedSlug]);

  // Validate the form fields and update validity state
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (['agentName', 'slug', 'description'].includes(name)) {
        const hasRequiredFields =
          value.agentName?.length >= 4 && 
          value.slug?.length >= 4 && 
          value.description?.length >= 10 &&
          (!slugExists || isOwnDraft); // Allow if no conflict OR it's user's own draft

        setIsValid(hasRequiredFields);

        // Notify parent component about validity
        if (updateValidity) {
          updateValidity(hasRequiredFields);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, slugExists, isOwnDraft, updateValidity]);

  // Initial validation check on mount only
  useEffect(() => {
    const currentValues = form.getValues();
    const hasRequiredFields =
      currentValues.agentName?.length >= 4 && 
      currentValues.slug?.length >= 4 && 
      currentValues.description?.length >= 10 &&
      (!slugExists || isOwnDraft);

    setIsValid(hasRequiredFields);

    if (updateValidity) {
      updateValidity(hasRequiredFields);
    }
  }, []); // Only run on mount

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Logo Upload - Mobile optimized */}
      <div className="sm:max-w-md">
        <LogoUploader
          logoPreview={logoPreview}
          setLogoPreview={handleLogoPreviewChange}
          setLogoFile={handleLogoFileChange}
          setLogoUrl={handleLogoUrlChange}
          onUploadingStateChange={onUploadingLogoChange}
        />
      </div>

      <div className="space-y-6 mt-8">
        <FormField
          control={form.control}
          name="agentName"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <div className="flex justify-between items-center">
                <FormLabel
                  htmlFor="agent-name"
                  className="text-sm font-medium"
                >
                  Agent Name <span className="text-destructive">*</span>
                </FormLabel>
                <span className="text-sm text-muted-foreground font-medium">
                  {field.value?.length || 0}/50
                </span>
              </div>
              <FormControl>
                <Input
                  id="agent-name"
                  placeholder="Enter a name for your agent"
                  {...field}
                  className="w-full h-11 px-4 shadow-sm transition-all focus-visible:ring-1 focus-visible:ring-primary text-sm"
                  maxLength={50}
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem className="space-y-2.5 mt-6">
              <div className="flex justify-between items-center">
                <FormLabel
                  htmlFor="agent-slug"
                  className="text-sm font-medium"
                >
                  Agent URL <span className="text-destructive">*</span>
                </FormLabel>
                <span className="text-sm text-muted-foreground font-medium">
                  {field.value?.length || 0}/50
                </span>
              </div>
              <div className="relative">
                <FormControl>
                  <div className="flex items-center shadow-sm rounded-md overflow-hidden">
                    <div className="bg-muted/70 px-4 py-2.5 border-y border-l text-sm text-muted-foreground h-11 flex items-center font-medium">
                      /agents/
                    </div>
                    <Input
                      id="agent-slug"
                      placeholder="agent-name"
                      {...field}
                      className="rounded-l-none h-11 px-4 transition-all focus-visible:ring-1 focus-visible:ring-primary border-l-0 text-sm"
                      maxLength={50}
                      disabled={isEditMode}
                      onChange={(e) => {
                        // Only allow lowercase letters and numbers, no spaces or special characters
                        const sanitizedValue = e.target.value
                          .toLowerCase()
                          .replace(/[^a-z0-9]/g, '');

                        field.onChange(sanitizedValue);
                        
                        // Mark that user has manually edited the slug
                        setHasManuallyEditedSlug(true);
                      }}
                    />
                  </div>
                </FormControl>
                {isCheckingSlug && (
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  </div>
                )}
              </div>

              {!isCheckingSlug && !slugExists && slug && slug.length >= 4 && (
                <div className="flex items-center gap-2 mt-1.5">
                  <div className="flex items-center justify-center w-5 h-5 rounded-full bg-green-50 dark:bg-green-900/20">
                    <Check className="h-3.5 w-3.5 text-green-500" />
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                    Agent URL available
                  </p>
                </div>
              )}

              {!isCheckingSlug && slugExists && (
                <div className="flex items-start gap-2 mt-1.5">
                  {isOwnDraft ? (
                    <>
                      <div className="flex items-center justify-center w-5 h-5 rounded-full bg-orange-50 dark:bg-orange-900/20 mt-0.5">
                        <AlertCircle className="h-3.5 w-3.5 text-orange-500" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          <strong>You already have a draft with this agent URL.</strong> You can{' '}
                          <Button
                            variant="link"
                            type="button" // Prevent form submission
                            onClick={async (e) => {
                              e.preventDefault(); // Prevent form submission
                              e.stopPropagation(); // Stop event bubbling
                              try {
                                let resolvedDraftId = draftAgentId;
                                // If we don't have the draft ID, resolve it from the slug
                                if (!resolvedDraftId && resolveDraftIdFromSlug) {
                                  const currentSlug = form.getValues('slug');
                                  if (currentSlug) {
                                    resolvedDraftId = await resolveDraftIdFromSlug(currentSlug);
                                  }
                                }
                                if (resolvedDraftId) {
                                  router.push(`/edit?id=${resolvedDraftId}`);
                                } else {
                                  toast({
                                    title: "Unable to edit draft",
                                    description: "Could not find the draft ID. Please try again.",
                                    variant: "destructive",
                                  });
                                }
                              } catch (error) {
                                toast({
                                  title: "Error",
                                  description: "Failed to load draft for editing.",
                                  variant: "destructive",
                                });
                              }
                            }}
                            className="text-orange-600 hover:text-orange-700 p-0 h-auto font-semibold underline text-sm"
                          >
                            edit the existing draft
                          </Button>.
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center justify-center w-5 h-5 rounded-full bg-red-50 dark:bg-red-900/20">
                        <AlertCircle className="h-3.5 w-3.5 text-destructive" />
                      </div>
                      <p className="text-sm text-destructive font-medium">
                        This Agent URL is already taken
                      </p>
                    </>
                  )}
                </div>
              )}

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem className="space-y-2.5 mt-6">
              <div className="flex justify-between items-center">
                <FormLabel
                  htmlFor="agent-description"
                  className="text-sm font-medium"
                >
                  Description <span className="text-destructive">*</span>
                </FormLabel>
                <span className="text-sm text-muted-foreground font-medium">
                  {field.value?.length || 0}/500
                </span>
              </div>
              <FormControl>
                <textarea
                  id="agent-description"
                  placeholder="Briefly describe what your agent does and how it can help users"
                  className="w-full min-h-32 p-4 rounded-md border border-input bg-background resize-y text-sm shadow-sm transition-all focus-visible:ring-1 focus-visible:ring-primary focus-visible:outline-none"
                  {...field}
                  maxLength={500}
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
