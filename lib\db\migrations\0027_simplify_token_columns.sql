-- Create a temporary column to store tokens based on role
ALTER TABLE "Message_v2" ADD COLUMN IF NOT EXISTS "tokens" INTEGER;

-- Copy prompt tokens for user messages
UPDATE "Message_v2"
SET "tokens" = "prompt_tokens"
WHERE "role" = 'user';

-- Copy completion tokens for assistant messages
UPDATE "Message_v2"
SET "tokens" = "completion_tokens"
WHERE "role" = 'assistant';

-- Drop the old columns
ALTER TABLE "Message_v2" DROP COLUMN IF EXISTS "prompt_tokens";
ALTER TABLE "Message_v2" DROP COLUMN IF EXISTS "completion_tokens";
ALTER TABLE "Message_v2" DROP COLUMN IF EXISTS "total_tokens";
