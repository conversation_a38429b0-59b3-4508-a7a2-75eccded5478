import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  try {
    // Check if user is authenticated by getting the token with the secret
    const token = await getToken({
      req: request,
      secret: process.env.AUTH_SECRET,
      secureCookie: process.env.NODE_ENV === "production", // Important for HTTPS
    });
    
   // console.log("Cookie names:", Array.from(request.cookies.getAll()).map(c => c.name));
   // console.log("Auth result:", token ? "Authenticated" : "Not authenticated");
    
    // List of protected routes that require authentication
    const protectedRoutes = [
      '/dashboard',
      '/billing',
      '/analytics',
      '/agents',
      '/create',
    ];

    // Check if the current path starts with any protected route
    const isProtectedRoute = protectedRoutes.some((route) =>
      request.nextUrl.pathname.startsWith(route),
    );

    if (isProtectedRoute) {
      // If not authenticated, redirect to login
      if (!token) {
        console.log('Redirecting to login - user is not authenticated');
        return NextResponse.redirect(new URL('/login', request.url));
      }

      // Check if user is verified for all protected routes
      const verifiedCookie = request.cookies.get('dashboardVerified');

      // Only allow access if the cookie exists and has value 'true'
      if (!verifiedCookie || verifiedCookie.value !== 'true') {
        // Don't redirect if already on the verify page
        if (!request.nextUrl.pathname.startsWith('/verify')) {
          console.log('Redirecting to verify page - user not verified');
          return NextResponse.redirect(new URL('/verify', request.url));
        }
      }
    }

    // Special handling for the login page when user is already verified
    if (request.nextUrl.pathname === '/login') {
      const verifiedCookie = request.cookies.get('dashboardVerified');

      // If user is already verified, redirect to dashboard
      if (token && verifiedCookie && verifiedCookie.value === 'true') {
        console.log('Redirecting to dashboard - user already verified');
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }

    // For all other routes, continue normally
    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    // Fail open - allow the request to proceed
    return NextResponse.next();
  }
}

// Make sure the matcher includes all protected routes and login
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/billing/:path*',
    '/analytics/:path*',
    '/agents/:path*',
    '/create',
    '/create/new',
    '/create/edit/:path*',
    '/login',
  ],
};
