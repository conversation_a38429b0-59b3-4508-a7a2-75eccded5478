'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useSWRConfig } from 'swr';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from './sidebar-history';
import { useWindowSize } from 'usehooks-ts';

import { SidebarToggle } from '@/components/sidebar-toggle';
import { Button } from '@/components/ui/button';
import { PlusIcon } from './icons';
import { useSidebar } from './ui/sidebar';
import { memo, useEffect, useState } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { type VisibilityType, VisibilitySelector } from './visibility-selector';
import { Badge } from './ui/badge';
import { Clock, DollarSign, MessageSquare, MoreVertical } from 'lucide-react';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatNumber } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import { useSubscription } from '@/context/subscription-context';
import dynamic from 'next/dynamic';
import { ShimmerButton } from './ui/shimmer-button';
import { ProBadge } from './pro-badge';
import { PreviewBadge } from './preview-badge';
import SubscriptionCancelModal from './subscription-cancel-modal';
import { ModelSelector } from './model-selector';
import { useAgentData } from '@/lib/agent-data-context';

// Dynamically import the modals to avoid circular dependencies
const SubscriptionModal = dynamic(() => import('./subscription-modal'), {
  ssr: false,
});

const AuthModal = dynamic(
  () => import('./auth-modal').then((mod) => mod.AuthModal),
  {
    ssr: false,
  },
);

function PureChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  agentName,
  agentSlug,
  agent,
  onModelChange,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  agentName?: string;
  agentSlug?: string;
  agent?: any;
  onModelChange?: (modelId: string) => void;
}) {
  const router = useRouter();
  const { open } = useSidebar();
  const { mutate } = useSWRConfig();
  const { data: session } = useSession();
  const currentUserId = session?.user?.id;
  const [isChecking, setIsChecking] = useState(false);
  const [showSubscribeModal, setShowSubscribeModal] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const {
    checkSubscription,
    isProForAgent,
    isLoading,
    isCanceled,
    getCancelDate,
  } = useSubscription();
  const [showAgentDetails, setShowAgentDetails] = useState(false); // Default to not showing until we get messages
  const [showFullHeader, setShowFullHeader] = useState(false); // New state to control header visibility
  const [agentProfile, setAgentProfile] = useState<any>(null);

  const { width: windowWidth } = useWindowSize();

  // Listen for messages to determine when to show agent details and full header
  useEffect(() => {
    // Create a function to handle the custom event
    const handleMessagesChange = (event: CustomEvent) => {
      const messagesCount = event.detail?.messagesCount || 0;
      setShowAgentDetails(messagesCount > 0);
      setShowFullHeader(messagesCount > 0); // Show full header when messages are present
    };

    // Add event listener for our custom event
    window.addEventListener(
      'messagesChange' as any,
      handleMessagesChange as EventListener,
    );

    // Clean up
    return () => {
      window.removeEventListener(
        'messagesChange' as any,
        handleMessagesChange as EventListener,
      );
    };
  }, []);

  // Use our consolidated bootstrap API data for agent profile
  const { data: bootstrapData } = useAgentData();

  // Set agent profile from bootstrap data when available
  useEffect(() => {
    if (bootstrapData?.profile && agent?.id) {
      setAgentProfile(bootstrapData.profile);
    }
  }, [bootstrapData, agent?.id]);

  // Use the centralized subscription context
  useEffect(() => {
    if (agent?.id && !isChecking) {
      setIsChecking(true);
      checkSubscription(agent.id).then(() => {
        setIsChecking(false);
      });
    }
  }, [agent?.id, checkSubscription]);

  // Handle new chat button click
  const handleNewChat = () => {
    // Invalidate general chat history cache
    mutate(unstable_serialize(getChatHistoryPaginationKey));

    // If we have an agent, also invalidate the specific agent's cache key
    if (agent?.id) {
      mutate(
        unstable_serialize((pageIndex, prevPageData) =>
          getChatHistoryPaginationKey(pageIndex, prevPageData, agent.id),
        ),
      );
    }

    // Force a second round of cache invalidations with delay for better reliability
    setTimeout(() => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));

      if (agent?.id) {
        mutate(
          unstable_serialize((pageIndex, prevPageData) =>
            getChatHistoryPaginationKey(pageIndex, prevPageData, agent.id),
          ),
        );
      }
    }, 800);

    if (agentSlug) {
      router.push(`/agent/${agentSlug}`);
    } else {
      router.push('/');
    }
    router.refresh();
  };

  const handleUpgrade = () => {
    setShowSubscribeModal(true);
  };

  // Handle opening the cancellation modal
  const handleCancelSubscription = () => {
    if (!agent?.id || !session?.user) return;
    setShowCancelModal(true);
  };

  // Handle successful cancellation
  const handleCancellationSuccess = () => {
    // Force UI update after cancellation
    if (agent?.id) {
      // Manually update the UI without triggering a re-fetch
      // This prevents the maximum update depth exceeded error
      setTimeout(() => {
        // Use a timeout to ensure the modal is fully closed first
        checkSubscription(agent.id);
      }, 100);
    }
  };

  return (
    <>
      {/* Always show a minimal header with just the sidebar toggle when no messages */}
      {!showFullHeader && (
        <header className="sticky top-0 z-10 flex items-center w-full h-14 px-4 shrink-0 bg-background">
          <div className="flex items-center gap-2">
            <SidebarToggle className="md:hidden" />
            {agentName && <div className="font-medium">{agentName}</div>}
          </div>
        </header>
      )}

      {/* Full header when messages are present */}
      {showFullHeader && (
        <header className="sticky top-0 z-10 flex items-center justify-between w-full h-14 px-4 shrink-0 bg-background">
          {/* Left side - Agent image, name and chat count */}
          <div className="flex items-center gap-2">
            <SidebarToggle className="md:hidden" />
            {/* Agent details */}
            {agent && showAgentDetails && (
              <div className="flex items-center gap-2 overflow-hidden">
                {/* Agent image */}
                <div className="flex-shrink-0">
                  {agent.logo ? (
                    <Image
                      src={agent.logo}
                      alt={agent.agentName}
                      width={32}
                      height={32}
                      className="w-8 h-8 object-cover rounded-md shadow-sm"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-md shadow-sm bg-gradient-to-r from-orange-400 to-pink-500 flex items-center justify-center text-white text-sm font-medium">
                      {agent.agentName?.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>

                <div className="overflow-hidden flex-grow">
                  <div className="flex items-center gap-2">
                    {/* Agent name */}
                    <div className="font-medium truncate max-w-[150px] sm:max-w-[200px] md:max-w-full">
                      {agent.agentName}
                    </div>

                    {/* CASE 1: Agent owner - Always show preview badge */}
                    {!isProForAgent(agent.id) &&
                      agent.userId === currentUserId &&
                      !isLoading(agent.id) && (
                        <div className="flex items-center gap-1">
                          <PreviewBadge
                            size="sm"
                            className="h-6 px-2 py-0 cursor-default pointer-events-none text-xs font-semibold"
                          />
                        </div>
                      )}

                    {/* CASE 2: Not subscribed - Show subscribe button */}
                    {session?.user &&
                      !isProForAgent(agent.id) &&
                      agent?.price > 0 && (
                        <ShimmerButton
                          shimmerColor="rgba(255, 255, 255, 0.5)"
                          shimmerSize="0.1em"
                          shimmerDuration="2s"
                          background="linear-gradient(90deg, #f97316, #ec4899)"
                          borderRadius="9999px"
                          className="text-xs h-7 font-semibold text-white px-2 w-auto"
                          onClick={handleUpgrade}
                        >
                          {agent?.accessLevel === 'lifetime'
                            ? `Buy $${agent?.price || 0}`
                            : `$${agent?.price || ''}/mo`}
                        </ShimmerButton>
                      )}

                    {/* CASE 3: Subscribed - Show Pro badge for non-creators */}
                    {isProForAgent(agent.id) && (
                      <>
                        {/* CASE 1: Agent owner - Show preview badge with price tag */}
                        <ProBadge size="sm" />

                        {/* CASE 2: Subscribed but not canceled - Show three dots menu only if not the creator */}
                        {session?.user && !isCanceled(agent.id) && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 rounded-full ml-1"
                              >
                                <MoreVertical className="h-3.5 w-3.5" />
                                <span className="sr-only">Menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={handleCancelSubscription}
                                className="text-destructive focus:text-destructive"
                              >
                                Cancel Subscription
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}

                        {/* CASE 3: Subscribed and canceled - Show cancellation text */}
                        {isCanceled(agent.id) && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground ml-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              Cancels{' '}
                              {getCancelDate(agent.id)
                                ? new Date(
                                    getCancelDate(agent.id) as string,
                                  ).toLocaleDateString()
                                : 'at period end'}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  {/* Temporarily hidden chat count badge */}
                  {false && (
                    <div className="flex items-center gap-1 text-xs text-zinc-500">
                      {/* Only show the chat count badge if there are chats */}
                      {((agentProfile?.totalChats &&
                        agentProfile.totalChats > 0) ||
                        (agent.totalChats && agent.totalChats > 0)) && (
                        <Badge
                          variant="secondary"
                          className="text-xs px-1.5 py-0.5 flex items-center gap-0.5"
                        >
                          <MessageSquare className="h-2.5 w-2.5" />
                          <span className="whitespace-nowrap">
                            {agentProfile?.totalChats
                              ? formatNumber(agentProfile.totalChats)
                              : formatNumber(agent.totalChats)}
                          </span>
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {agentName && !agent && (
              <div className="font-medium">{agentName}</div>
            )}
          </div>

          {/* Right side - Visibility toggle and New chat button */}
          <div className="flex items-center gap-2">
            {!isReadonly && (
              <>
                {/* Model Selector - hidden on mobile */}
                <div className="hidden md:block">
                  <ModelSelector
                    selectedModelId={selectedModelId}
                    onModelChange={onModelChange}
                  />
                </div>
                {/* Visibility Selector - hidden on mobile */}
                <div className="hidden md:block">
                  <VisibilitySelector
                    chatId={chatId}
                    selectedVisibilityType={selectedVisibilityType}
                  />
                </div>
              </>
            )}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  data-testid="new-chat-button"
                  variant="outline"
                  size="icon"
                  className="rounded-full"
                  onClick={handleNewChat}
                >
                  <PlusIcon />
                  <span className="sr-only">New Chat</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>New Chat</TooltipContent>
            </Tooltip>
          </div>
        </header>
      )}

      {showSubscribeModal && agent && (
        <SubscriptionModal
          agentId={agent.id}
          agentName={agent.agentName}
          chatId={chatId} // Pass chatId to the modal
          isOpen={showSubscribeModal}
          onClose={() => setShowSubscribeModal(false)}
        />
      )}

      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      )}

      {showCancelModal && agent && (
        <SubscriptionCancelModal
          isOpen={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          agentId={agent.id}
          agentName={agent.agentName}
          onSuccess={handleCancellationSuccess}
        />
      )}
    </>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return (
    prevProps.selectedModelId === nextProps.selectedModelId &&
    prevProps.agentName === nextProps.agentName &&
    prevProps.agent?.id === nextProps.agent?.id
  );
});
