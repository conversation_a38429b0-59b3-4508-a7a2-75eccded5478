/**
 * Utility function to reset all application state during sign-out
 * This includes React contexts, browser storage, and cookies
 */

import { clearBrowserStorage } from './clear-browser-storage';

// Define a type for context reset functions
type ContextResetFunction = () => void;

// Global registry of context reset functions
const contextResetFunctions: ContextResetFunction[] = [];

/**
 * Register a context's reset function
 * Each context provider should call this during initialization
 * @param resetFn Function that resets the context state
 */
export function registerContextReset(resetFn: ContextResetFunction): void {
  if (typeof resetFn === 'function' && !contextResetFunctions.includes(resetFn)) {
    contextResetFunctions.push(resetFn);
  }
}

/**
 * Unregister a context's reset function
 * Should be called during context unmount if needed
 * @param resetFn The function to unregister
 */
export function unregisterContextReset(resetFn: ContextResetFunction): void {
  const index = contextResetFunctions.indexOf(resetFn);
  if (index !== -1) {
    contextResetFunctions.splice(index, 1);
  }
}

/**
 * Reset all application state
 * This includes:
 * 1. All registered React contexts
 * 2. Browser storage (sessionStorage, localStorage, IndexedDB)
 * 3. Cookies via API call
 */
export async function resetAppState(): Promise<void> {
  console.log(`Resetting application state: ${contextResetFunctions.length} contexts registered`);
  
  // 1. Reset all registered React contexts
  for (const resetFn of contextResetFunctions) {
    try {
      resetFn();
    } catch (error) {
      console.error('Error resetting context:', error);
    }
  }

  // 2. Clear browser storage - await the async function
  try {
    console.log('Starting browser storage clearing...');
    await clearBrowserStorage();
    console.log('Browser storage clearing completed');
    
    // Double-check sessionStorage is actually empty
    if (typeof window !== 'undefined' && window.sessionStorage) {
      const remainingKeys = Object.keys(sessionStorage);
      if (remainingKeys.length > 0) {
        console.warn('SessionStorage still has items after clearing:', remainingKeys);
        // Force direct removal of any remaining items
        remainingKeys.forEach(key => {
          console.log(`Forcing removal of sessionStorage item: ${key}`);
          sessionStorage.removeItem(key);
        });
      }
    }
  } catch (error) {
    console.error('Error clearing browser storage:', error);
  }

  // 3. Clear cookies via API
  try {
    const response = await fetch('/api/auth/clear-cookie');
    if (!response.ok) {
      throw new Error(`Failed to clear cookies: ${response.status} ${response.statusText}`);
    }
    console.log('Cookies cleared via API');
  } catch (error) {
    console.error('Error clearing cookies via API:', error);
  }
  
  console.log('Application state reset completed');
}
