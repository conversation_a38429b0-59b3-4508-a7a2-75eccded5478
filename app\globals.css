@import './launchpad-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 0, 0, 0;
        --background-end-rgb: 0, 0, 0;
    }
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: 20.5 90.2% 48.2%; /* Updated to match Launchpad orange */
        --primary-foreground: 60 9.1% 97.8%;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 25 5% 45%;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 20.5 90.2% 48.2%; /* Updated to match Launchpad orange */
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        /* Standard sidebar colors matching the rest of the application */
        --sidebar-background: 0 0% 100%; /* Pure white for cleaner look */
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: var(--primary); /* Use standard primary color */
        --sidebar-primary-foreground: var(--primary-foreground);
        --sidebar-accent: 210 40% 96.1%; /* Standard light accent color */
        --sidebar-accent-foreground: 222.2 47.4% 11.2%; /* Standard accent text */
        --sidebar-border: 214.3 31.8% 91.4%; /* Standard border color */
        --sidebar-ring: var(--ring); /* Use standard ring color */
    }
    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;
        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 25 5% 45%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        /* Standard dark mode sidebar colors */
        --sidebar-background: 240 10% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: var(--primary); /* Use standard primary color */
        --sidebar-primary-foreground: var(--primary-foreground);
        --sidebar-accent: 240 3.7% 15.9%; /* Standard dark accent color */
        --sidebar-accent-foreground: 0 0% 98%; /* Standard dark accent text */
        --sidebar-border: 240 3.7% 15.9%; /* Standard dark border color */
        --sidebar-ring: var(--ring); /* Use standard ring color */
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

.skeleton {
    * {
        pointer-events: none !important;
    }

    *[class^="text-"] {
        color: transparent;
        @apply rounded-md bg-foreground/20 select-none animate-pulse;
    }

    .skeleton-bg {
        @apply bg-foreground/10;
    }

    .skeleton-div {
        @apply bg-foreground/20 animate-pulse;
    }
}

.ProseMirror {
    outline: none;
}

.cm-editor,
.cm-gutters {
    @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
    @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
    @apply bg-transparent !important;
}

.cm-activeLine {
    @apply rounded-r-sm !important;
}

.cm-lineNumbers {
    @apply min-w-7;
}

.cm-foldGutter {
    @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
    @apply rounded-l-sm !important;
}

.suggestion-highlight {
    @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}

/* Safari input overflow fixes */
.safari-input-fix input,
.safari-input-fix textarea,
.safari-input-fix select {
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
}

/* Safari-specific webkit fixes */
@supports (-webkit-appearance: none) {
    .safari-input-fix input[type="text"],
    .safari-input-fix input[type="url"],
    .safari-input-fix input[type="email"],
    .safari-input-fix textarea {
        -webkit-appearance: none;
        width: 100% !important;
        max-width: 100% !important;
        min-width: 0 !important;
        overflow-x: hidden !important;
        word-wrap: break-word;
        word-break: break-word;
    }
}
