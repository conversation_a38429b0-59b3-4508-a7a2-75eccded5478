CREATE TABLE IF NOT EXISTS "CreatorPlans" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(64) NOT NULL,
	"monthly_price" integer NOT NULL,
	"yearly_price" integer NOT NULL,
	"tokens_allowed" integer NOT NULL,
	"knowledge_base_size" varchar(10) NOT NULL,
	"agents_allowed" integer NOT NULL,
	"top_up_tokens" integer,
	"top_up_knowledge_base" varchar(10),
	"top_up_token_price" integer,
	"top_up_knowledge_base_price" integer,
	"embeddable_widget" boolean,
	"monetization_included" boolean,
	"crypto_and_fiat_payments" boolean,
	"analytics" varchar,
	"support_type" varchar,
	"other_features" json NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "Message_v2" DROP CONSTRAINT "Message_v2_agent_id_agents_id_fk";
EXCEPTION
  WHEN undefined_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "Chat" ADD COLUMN "agentId" uuid NOT NULL;
EXCEPTION
  WHEN duplicate_column THEN null;
END $$;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Chat" ADD CONSTRAINT "Chat_agentId_agents_id_fk" FOREIGN KEY ("agentId") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "Message_v2" DROP COLUMN IF EXISTS "agent_id";