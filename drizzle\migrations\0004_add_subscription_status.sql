-- Custom SQL migration file
-- Migration: Add subscription status to CreatorSubscriptions
-- Description: Adds an enum type for subscription status and a new column to track subscription status

-- 1️⃣ Create the enum type (do this once)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'creator_subscription_status') THEN
        CREATE TYPE creator_subscription_status AS ENUM ('active', 'inactive');
    END IF;
END$$;

-- 2️⃣ Add the column with default and NOT NULL constraint
ALTER TABLE CreatorSubscriptions
ADD COLUMN status creator_subscription_status NOT NULL DEFAULT 'active';
