CREATE TABLE IF NOT EXISTS "agent_knowledge_files" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
    "agent_id" uuid NOT NULL,
    "filename" text NOT NULL,
    "url" text NOT NULL,
    "file_type" text NOT NULL,
    "file_size" text NOT NULL,
    "status" varchar NOT NULL DEFAULT 'pending' CHECK ("status" IN ('pending', 'processing', 'completed', 'failed')),
    "processing_error" text,
    "created_at" timestamp NOT NULL DEFAULT now(),
    "processed_at" timestamp,
    "total_chunks" text,
    "processed_chunks" text DEFAULT '0',
    CONSTRAINT "agent_knowledge_files_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "agents"("id") ON DELETE CASCADE
);