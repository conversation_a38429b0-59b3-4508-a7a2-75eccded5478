# Agent Creation Flow

## Overview

The agent creation flow in the BuildThatIdea platform allows users to create custom AI agents with specific knowledge bases, instructions, and monetization options. This document details the complete process from initial form submission to agent deployment.

## User Interface

The agent creation interface is implemented in `/app/create/page.tsx` and consists of a multi-step form with the following sections:

1. **Basic Information**: Agent name, description, and instructions
2. **Appearance**: Logo and visual elements
3. **Knowledge Base**: File uploads for the agent's knowledge
4. **Monetization**: Pricing and access level settings

## Creation Process

### 1. Basic Information

Users provide the following information:

- **Agent Name**: Display name for the agent
- **Slug**: URL-friendly identifier (auto-generated from name)
- **Description**: Public description of the agent's capabilities
- **Instructions**: System instructions for the agent's behavior
- **Model**: AI model to use (e.g., Claude, GPT)
- **Visibility**: Public or private

### 2. Knowledge Base Upload

Users can upload various file types to create the agent's knowledge base:

- **Supported Formats**: PDF, DOCX, TXT, CSV
- **File Processing**: Files are processed to extract text content
- **Chunking**: Text is split into manageable chunks for embedding
- **Vector Indexing**: Chunks are converted to vector embeddings for similarity search

**Important**: While files are uploaded at this stage, the actual embedding process does not start until the user clicks the "Create Agent" button at the end of the flow.

### 3. Monetization Options

Users can choose monetization options for their agent:

- **Access Level**: Free, subscription, or lifetime access
- **Pricing**: Monthly subscription price or one-time payment
- **Stripe Integration**: For payment processing

## Technical Implementation

### Form State Management

The form state is managed using React Hook Form:

```typescript
const methods = useForm<FormValues>({
  defaultValues: {
    agentName: '',
    slug: '',
    instruction: '',
    description: '',
    model: 'claude-3-haiku',
    isPublic: true,
    isPaid: false,
    pricingType: 'subscription',
    price: '',
    accessLevel: 'free',
    visibility: 'public',
    files: [],
  },
});
```

### Slug Generation

The slug is automatically generated from the agent name:

```typescript
const generateSlug = (name: string) => {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};
```

### File Upload Process

The file upload process involves several steps:

1. **Client-Side Handling**:
   - File selection via drag-and-drop or file picker
   - Initial validation (file type, size)
   - Direct upload to S3 using presigned URLs
   - Progress tracking with real-time updates

2. **Upload Implementation**:
   - Files are uploaded directly from the client browser to S3
   - The system first requests a presigned URL from `/api/agents/generate-presigned-url`
   - Files are then uploaded to S3 using XMLHttpRequest with progress tracking
   - After successful upload, file metadata is registered with the API

3. **Vector Index Creation**:
   - A vector index is conditionally created for the agent if one doesn't already exist
   - This happens regardless of whether files are uploaded or not
   - The actual embedding process only starts when the "Create Agent" button is clicked

### Vector Index Generation

The `generateVectorIndexIfNeeded` function in `/app/api/agents/upload-knowledgebase/route.ts` handles vector index creation:

```typescript
const generateVectorIndexIfNeeded = async (agent: any) => {
  // Check if agent already has valid endpoint and token
  const hasEndpoint = agent.endpoint && agent.endpoint.trim() !== '';
  const hasToken = agent.token && agent.token.trim() !== '';

  // If agent already has both endpoint and token, return the existing values
  if (hasEndpoint && hasToken) {
    console.log('Agent already has vector index, using existing one');
    return {
      endpoint: agent.endpoint,
      token: agent.token,
    };
  }

  console.log('Generating vector index for agent:', agent.id);

  try {
    // Call the generate-index API
    const indexResponse = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/agents/generate-index`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: agent.id,
        }),
      },
    );

    if (!indexResponse.ok) {
      const errorText = await indexResponse.text();
      console.error('Failed to generate vector index:', errorText);
      throw new Error('Failed to generate vector index');
    }

    const indexData = await indexResponse.json();

    // Encrypt the token
    const encryptedToken = encrypt(indexData.token);

    return {
      endpoint: indexData.endpoint,
      token: encryptedToken,
    };
  } catch (error) {
    console.error('Error generating vector index:', error);
    return null;
  }
};
```

### File Processing

The file processing logic in the upload-knowledgebase route handles different file types:

1. **PDF Processing**: Using pdf2json
2. **DOCX Processing**: Using mammoth
3. **CSV Processing**: Using papaparse
4. **TXT Processing**: Direct text extraction

### Database Operations

The agent creation process involves several database operations:

1. **Agent Record Creation**: Initial creation of the agent record
2. **File Record Creation**: Recording uploaded files in the database
3. **Vector Index Update**: Storing vector index information

## API Endpoints

The agent creation flow uses the following API endpoints:

1. **POST /api/agents**: Creates a new agent
2. **POST /api/agents/upload-knowledgebase**: Uploads and processes knowledge base files
3. **POST /api/agents/upload-logo**: Uploads the agent logo
4. **POST /api/agents/generate-index**: Generates a vector index for the agent
5. **POST /api/billing/user-subscription/create-checkout**: Creates a Stripe checkout session for paid agents

## Embedding Process

It's important to note that the actual embedding process for knowledge base files only begins when the user clicks the "Create Agent" button at the end of the flow. This is the same behavior in both the creation and editing flows:

1. Files are uploaded and stored in S3
2. A vector index is conditionally created if one doesn't already exist
3. When the "Create Agent" button is clicked, the embedding process starts
4. The user is redirected to a success page while embeddings are processed in the background

## Error Handling

The agent creation flow includes comprehensive error handling:

1. **Form Validation**: Client-side validation using Zod
2. **File Upload Errors**: Handling S3 upload failures
3. **Processing Errors**: Handling text extraction failures
4. **Vector Index Errors**: Handling Upstash Vector index creation failures
5. **Database Errors**: Handling database operation failures

## Success Flow

Upon successful agent creation:

1. **Success Page**: User is redirected to a success page
2. **Payment Flow**: For paid agents, user is redirected to Stripe checkout
3. **Dashboard**: Agent appears in the user's dashboard

## Fixed Issues

A notable issue that was fixed in the system involved the vector index generation:

Previously, the edit page wouldn't create a vector index if no files were uploaded during agent creation. This was fixed by simplifying the vector index generation logic in the upload-knowledgebase route to always call `generateVectorIndexIfNeeded`, which creates a vector index if one doesn't exist, regardless of whether there are files or whether it's the create or edit page.

## Security Considerations

The agent creation flow includes several security measures:

1. **Authentication**: Only authenticated users can create agents
2. **Authorization**: Users can only modify their own agents
3. **Input Validation**: All user inputs are validated
4. **File Validation**: File types and sizes are validated
5. **Token Encryption**: Vector index tokens are encrypted in the database

## Performance Considerations

The agent creation flow includes several performance optimizations:

1. **Chunked Uploads**: Large files are uploaded in chunks
2. **Background Processing**: File processing is done in the background
3. **Progress Tracking**: Users can see upload and processing progress
4. **Debouncing**: Form inputs are debounced to reduce API calls

## User Experience

The agent creation flow includes several UX enhancements:

1. **Progress Indicators**: For file uploads and processing
2. **Validation Feedback**: Immediate feedback on form errors
3. **Auto-Save**: Form data is automatically saved
4. **Responsive Design**: Works on mobile and desktop devices
