import React, { useState, useCallback, useEffect } from 'react';
import { EnhancedTextarea } from '@/components/enhanced-textarea';
import { QuickMessagesEditor } from './quick-messages-editor';
import { EnhancedModelSelector } from './enhanced-model-selector';
import WebsiteCrawlInput from './website-crawl-input';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import type { UseFormReturn } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2, Check, X, FileText } from 'lucide-react';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import type { FormValues } from './types';

// FileEntry type definition
interface FileEntry {
  id: string;
  name: string;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  file?: File;
  url?: string;
  draftAgentId?: string;
  size?: number;
  progress?: number;
}

// Response type interface
interface UploadResponse {
  files: {
    name: string;
    url?: string;
    status: 'completed' | 'error';
    error?: string;
    type?: string;
    size?: string;
  }[];
  vectorIndexGenerated?: boolean;
}

interface AgentAppearanceFormProps {
  form?: UseFormReturn<FormValues, any, FormValues>;
  quickMessages: string[];
  setQuickMessages: (messages: string[]) => void;
  onSaveDraft?: () => Promise<any>;
  planLimits?: {
    plan?: {
      name: string;
      tier: string;
    };
  };
  draftAgentId?: string | null; // Add draftAgentId prop
  setIsUploadingFiles?: (isUploading: boolean) => void;
  isEditMode?: boolean; // Add isEditMode prop
}

// Helper function to format file size
const formatFileSize = (bytes: number | string): string => {
  const size = typeof bytes === 'string' ? Number.parseInt(bytes) : bytes;
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / (1024 * 1024)).toFixed(1)} MB`;
};

// Calculate total size of all files
const calculateTotalSize = (files: FileEntry[]): string => {
  if (!files || files.length === 0) return '0 B';
  
  const totalBytes = files.reduce((total, file) => {
    const fileSize = file.size ? (typeof file.size === 'string' ? Number.parseInt(file.size, 10) : file.size) : 0;
    return total + fileSize;
  }, 0);
  
  return formatFileSize(totalBytes);
};

// Website URL validation is now handled in the WebsiteCrawlInput component

export function AgentAppearanceForm({
  form: formProp,
  quickMessages,
  setQuickMessages,
  onSaveDraft,
  planLimits,
  draftAgentId, // Add parameter
  setIsUploadingFiles,
  isEditMode,
}: AgentAppearanceFormProps) {
  const formContext = useFormContext<FormValues>();
  const form = formProp || formContext;

  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isDeletingFile, setIsDeletingFile] = useState<string | null>(null);
  const [fileToDelete, setFileToDelete] = useState<FileEntry | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [fileSizeError, setFileSizeError] = useState<string | null>(null);
  const [websiteUrlError, setWebsiteUrlError] = useState<string | null>(null);

  // Constants for file size limits
  const MAX_FILE_SIZE_HOBBY = 10 * 1024 * 1024; // 10MB for hobby plan
  const MAX_FILE_SIZE_PRO = 100 * 1024 * 1024; // 100MB for pro plan

  // Log draftAgentId and files on component mount
  useEffect(() => {
    const draftAgentId = localStorage.getItem('draftAgentId');

    const files = form.getValues('files');
  }, [form]);

  // Update form files when progress changes
  useEffect(() => {
    if (Object.keys(uploadProgress).length > 0) {
      const currentFiles = form.getValues('files') || [];

      const hasProgressChanged = currentFiles.some(file =>
        file.progress !== (uploadProgress[file.id] || file.progress)
      );

      if (hasProgressChanged) {
        const updatedFiles = currentFiles.map((file) => ({
          ...file,
          progress: uploadProgress[file.id] || file.progress,
        }));

        form.setValue('files', updatedFiles, { shouldDirty: true });
      }
    }
  }, [uploadProgress, form]);

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      event.preventDefault();

      const files = event.target.files;
      if (!files || files.length === 0) return;

      // Clear any previous file size errors
      setFileSizeError(null);

      // Calculate total size of existing and new files
      const existingFiles = form.getValues('files') || [];
      const existingFilesSize = existingFiles.reduce((total, file) => total + (file.size || 0), 0);
      const newFilesSize = Array.from(files).reduce((total, file) => total + file.size, 0);
      const totalSize = existingFilesSize + newFilesSize;
      
      // Check plan and enforce appropriate file size limit
      const isHobbyPlan = planLimits?.plan?.name?.toLowerCase() === 'hobby' || !planLimits?.plan;
      const isProPlan = planLimits?.plan?.name?.toLowerCase() === 'pro';
      
      if (isHobbyPlan && totalSize > MAX_FILE_SIZE_HOBBY) {
        setFileSizeError(`Hobby plan only allows up to 10MB of files. Please upgrade to Pro for larger file uploads.`);
        event.target.value = '';
        return; // Early return to prevent upload process
      }
      
      if (isProPlan && totalSize > MAX_FILE_SIZE_PRO) {
        setFileSizeError(`Pro plan only allows up to 100MB of files. Please <a href="https://buildthatidea.com/pricing" target="_blank" rel="noopener noreferrer" style="text-decoration: underline;">contact us</a> to upgrade to Enterprise plan for larger file uploads.`);
        event.target.value = '';
        return; // Early return to prevent upload process
      }

      setIsUploading(true);
      // Update parent component state if available
      if (setIsUploadingFiles) {
        setIsUploadingFiles(true);
      }

      try {
        // Save draft if needed to get an agent ID
        let agentId = draftAgentId;
        
        // If no draftAgentId was passed or available in props, call onSaveDraft
        if (!agentId && onSaveDraft) {
          // console.log('No agentId found in props, triggering draft save to get one');
          const draftAgent = await onSaveDraft();

          if (!draftAgent) {
            toast.error('Unable to prepare for file upload. Please try again.');
            event.target.value = '';
            setIsUploading(false);
            return;
          }
          
          // Try to get the agent ID from localStorage if not available from draftAgent
          agentId = draftAgent.id;
        }
        
        // If we still don't have an agent ID, check localStorage
        if (!agentId) {
          // console.log('Getting agent ID from localStorage');
          agentId = localStorage.getItem('draftAgentId');
        }
        
        if (!agentId) {
          toast.error('Unable to upload files. Please try again in a moment.');
          event.target.value = '';
          setIsUploading(false);
          return;
        }
        
        // console.log(`Using agent ID for file upload: ${agentId}`);

        const newProgressState: Record<string, number> = {};

        const tempFileEntries: FileEntry[] = Array.from(files).map((file) => {
          const id = crypto.randomUUID();
          newProgressState[id] = 0; 

          return {
            id,
            name: file.name,
            status: 'uploading' as const,
            size: file.size,
            progress: 0,
            file,
          };
        });

        setUploadProgress((prev) => ({ ...prev, ...newProgressState }));

        const existingFiles = form.getValues('files') || [];
        form.setValue('files', [...existingFiles, ...tempFileEntries]);

        const uploadPromises = tempFileEntries.map(async (fileEntry) => {
          try {
            // Special handling for markdown files - ensure correct MIME type
            let fileType = fileEntry.file?.type;
            if (fileEntry.file?.name.endsWith('.md') && (!fileType || fileType === '')) {
              fileType = 'text/markdown';
              // console.log('Setting MIME type for markdown file:', fileEntry.file.name, 'to text/markdown');
            }
            
            const requestBody = {
              fileName: fileEntry.file?.name,
              fileType: fileType,
              fileSize: fileEntry.file?.size,
              agentId: agentId,
            };
            
            const presignedUrlResponse = await fetch('/api/agents/generate-presigned-url', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(requestBody),
            });

            // // Add debugging for markdown files
            // if (fileEntry.file?.name.endsWith('.md')) {
            //   console.log('Uploading markdown file:', {
            //     fileName: fileEntry.file?.name,
            //     fileType: fileEntry.file?.type,
            //     fileSize: fileEntry.file?.size,
            //     agentId: agentId,
            //     requestBody: JSON.stringify({
            //       fileName: fileEntry.file?.name,
            //       fileType: fileEntry.file?.type,
            //       fileSize: fileEntry.file?.size,
            //       agentId: agentId,
            //     })
            //   });
            // }

            if (!presignedUrlResponse.ok) {
              const errorText = await presignedUrlResponse.text();
              throw new Error(`Failed to get presigned URL: ${presignedUrlResponse.status} - ${errorText}`);
            }

            const presignedData = await presignedUrlResponse.json();
            const { presignedUrl, fileUrl } = presignedData;

            return new Promise<{
              fileEntry: FileEntry;
              response: { url: string; status: 'completed' | 'error'; name: string; error?: string; vectorIndexGenerated?: boolean; };
            }>((resolve, reject) => {
              const xhr = new XMLHttpRequest();

              xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                  const percentComplete = Math.round((event.loaded / event.total) * 100);
                  
                  setUploadProgress((prev) => ({
                    ...prev,
                    [fileEntry.id]: percentComplete,
                  }));
                }
              };

              xhr.onload = () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                  
                  setUploadProgress((prev) => ({
                    ...prev,
                    [fileEntry.id]: 100,
                  }));
                  
                  // Successfully uploaded to S3, resolve with file info
                  // We'll update the knowledge base with all files after all uploads complete
                  resolve({
                    fileEntry,
                    response: {
                      url: fileUrl,
                      status: 'completed',
                      name: fileEntry.name,
                      vectorIndexGenerated: false, // Will be determined later
                    },
                  });
                } else {
                  resolve({
                    fileEntry,
                    response: {
                      url: '',
                      status: 'error',
                      name: fileEntry.name,
                      error: `Failed to upload to S3: ${xhr.status} ${xhr.statusText}`,
                    },
                  });
                }
              };

              xhr.onerror = () => {
                resolve({
                  fileEntry,
                  response: {
                    url: '',
                    status: 'error',
                    name: fileEntry.name,
                    error: 'Network error during upload',
                  },
                });
              };

              xhr.onabort = () => {
                resolve({
                  fileEntry,
                  response: {
                    url: '',
                    status: 'error',
                    name: fileEntry.name,
                    error: 'Upload was aborted',
                  },
                });
              };

              xhr.open('PUT', presignedUrl);

              xhr.setRequestHeader('Content-Type', fileEntry.file?.type || 'application/octet-stream');

              xhr.send(fileEntry.file);
            });
          } catch (error) {
            return {
              fileEntry,
              response: {
                status: 'error' as const,
                name: fileEntry.name,
                error: error instanceof Error ? error.message : 'Unknown error',
                url: '', 
                vectorIndexGenerated: false, 
              },
            };
          }
        });

        const results = await Promise.all(uploadPromises);

        const completedFiles = form.getValues('files');

        const needsUpdate = results.some(result => {
          const file = completedFiles.find(f => f.id === result.fileEntry.id);
          return file && file.status === 'uploading';
        });

        if (needsUpdate) {
          const finalFiles = completedFiles.map((file) => {
            const result = results.find((r) => r.fileEntry.id === file.id);
            if (result) {
              const uploadedFile = result.response;

              return {
                ...file,
                status: uploadedFile.status as 'completed' | 'error',
                url: uploadedFile.url,
                error: uploadedFile.error,
                progress: 100,
              };
            }
            return file;
          });

          form.setValue('files', finalFiles);

          const newProgressState = { ...uploadProgress };
          results.forEach(result => {
            delete newProgressState[result.fileEntry.id];
          });
          setUploadProgress(newProgressState);
          
          // Register all completed files with the upload-knowledgebase endpoint
          // This ensures all files are registered in a single operation
          try {
            const successfulUploads = results
              .filter(r => r.response.status === 'completed' && r.response.url)
              .map(r => ({
                name: r.response.name,
                url: r.response.url,
                type: r.fileEntry.file?.type,
                size: r.fileEntry.file?.size?.toString(),
              }));
              
            if (successfulUploads.length > 0) {
              const draftAgentId = localStorage.getItem('draftAgentId');
              
              // console.log(`Registering ${successfulUploads.length} files with knowledge base`, {
              //   files: successfulUploads.map(f => f.name)
              // });
              
              const knowledgebaseResponse = await fetch('/api/agents/upload-knowledgebase', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  agentId: agentId, // Use our local agentId variable
                  files: successfulUploads,
                }),
              });
              
              if (!knowledgebaseResponse.ok) {
                throw new Error(`Failed to register files with API: ${knowledgebaseResponse.status}`);
              }
              
              const knowledgebaseData = await knowledgebaseResponse.json();
              
              // Check if vector index was generated
              if (knowledgebaseData.vectorIndexAvailable) {
                toast.success('Knowledge base index created successfully');
              } else {
                toast.success('Files uploaded successfully');
              }
            }
          } catch (error) {
            console.error('Error registering files with knowledge base:', error);
            toast.error('Files uploaded but could not be registered with knowledge base');
            // Continue but show error message
          }
          
          // Trigger a draft save after files have been successfully processed
          // if (onSaveDraft) {
          //   try {
          //     // console.log('Triggering draft save after file upload');
          //     await onSaveDraft();
          //   } catch (saveError) {
          //     console.error('Error saving draft after file upload:', saveError);
          //     // Don't show error toast here as it would be confusing since the upload succeeded
          //   }
          // }
        }
      } catch (error) {
        toast.error('Failed to upload files');

        const failedFiles = form.getValues('files');
        const updatedFiles = failedFiles.map((file) => {
          if (file.status === 'uploading') {
            return {
              ...file,
              status: 'error' as const,
              error: 'Upload failed',
            };
          }
          return file;
        });
        form.setValue('files', updatedFiles);
      } finally {
        setIsUploading(false);
        // Update parent component state if available
        if (setIsUploadingFiles) {
          setIsUploadingFiles(false);
        }
        event.target.value = '';
      }
    },
    [form, onSaveDraft, uploadProgress, planLimits, MAX_FILE_SIZE_HOBBY, MAX_FILE_SIZE_PRO, draftAgentId, setIsUploadingFiles],
  );

  const removeFile = useCallback(
    async (fileId: string) => {
      const currentFiles = form.getValues('files') || [];
      const fileToRemove = currentFiles.find(file => file.id === fileId);

      setIsDeletingFile(fileId);

      try {
        if (fileToRemove?.url && fileToRemove.status === 'completed') {
          // Use the draftAgentId from props first, then try localStorage
          let agentId = draftAgentId;
          if (!agentId) {
            agentId = localStorage.getItem('draftAgentId');
          }
          
          if (!agentId) {
            toast.error('Unable to delete file from server');
            return;
          }

          // Determine if we're on the create page
          const isCreatePage = window.location.pathname === '/create';
          
          const response = await fetch('/api/agents/delete-knowledgebase-file', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agentId: agentId,
              fileUrl: fileToRemove.url,
              isCreatePage: isCreatePage, // Add this parameter to tell the API which path to take
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            toast.error('Failed to delete file from server');
            return;
          }

          if (window.location.pathname === '/create') {
            toast.success('File removed successfully');
          } else {
            toast.success('File removed from knowledge base');
          }
        }

        const updatedFiles = currentFiles.filter((file) => file.id !== fileId);
        form.setValue('files', updatedFiles);

        setUploadProgress((prev) => {
          const newState = { ...prev };
          delete newState[fileId];
          return newState;
        });

        if (window.location.pathname === '/create') {
          const saveDraft = (window as any).saveDraftAgent;
          if (typeof saveDraft === 'function') {
            try {
              // console.log('Calling global saveDraftAgent after file removal');
              saveDraft();
            } catch (error) {
              console.error('Error calling global saveDraftAgent:', error);
            }
          }
        }
      } finally {
        setIsDeletingFile(null);
        setFileToDelete(null);
        setShowDeleteConfirm(false);
      }
    },
    [form],
  );

  return (
    <div className="space-y-6">
      {/* Model Selector */}
      <div className="space-y-2">
        <FormLabel>AI Model</FormLabel>
        <div className="mb-4">
          <EnhancedModelSelector
            value={form.watch("model")}
            onChange={(value) => form.setValue("model", value, { shouldValidate: true })}
          />
        </div>
      </div>

      {/* Instructions Section */}
      <FormField
        control={form.control}
        name="instruction"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium">Instructions <span className="text-orange-500">*</span></FormLabel>

            <FormControl>
              <EnhancedTextarea
                placeholder="E.g., You are a financial advisor with expertise in personal finance, investment strategies, and retirement planning. Provide clear, actionable advice tailored to each user's financial situation..."
                className="min-h-[120px] resize-none"
                value={field.value}
                onChange={(e) => field.onChange(e.target.value)}
                agentName={form.watch('agentName')}
                selectedChatModel={form.watch('model')}
                onSave={(value) => field.onChange(value)}
              />
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Website Crawl Input for RAG */}
      <WebsiteCrawlInput form={form} onSaveDraft={onSaveDraft} isEditMode={isEditMode} />

      {/* Knowledge Base Section */}
      <div className="space-y-4">
        <FormLabel className="text-sm font-medium">Knowledge Base</FormLabel>
        <p className="text-sm text-muted-foreground">
          Upload documents to train your agent with domain-specific knowledge.
        </p>

        <div className="flex flex-col space-y-4">
          {/* File Upload Area */}
          <div className="border border-dashed border-border rounded-lg p-6 bg-muted/50 hover:bg-muted transition-colors duration-200">
            <div className="flex flex-col items-center justify-center text-center">
              <div className="mb-3 rounded-full bg-primary/10 p-3">
                <svg
                  className="size-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-sm font-medium mb-1">
                Drag and drop files or click to upload
              </h3>
              <p className="text-xs text-muted-foreground mb-3">
                Supports PDF, TXT, DOCX, and MD files (max 10MB per file)
              </p>
              <Button
                type="button"
                variant="secondary"
                size="sm"
                className="relative overflow-hidden"
                disabled={isUploading}
                title="Upload files to train your agent"
              >
                <div className="flex items-center gap-2">
                  {isUploading ? (
                    <>
                      <Loader2 className="size-4" />
                      <span>Uploading...</span>
                    </>
                  ) : (
                    <>
                      <svg
                        className="size-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                      <span>Select Files</span>
                    </>
                  )}
                  <input
                    type="file"
                    multiple
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    onChange={handleFileUpload}
                    disabled={isUploading}
                    accept=".pdf,.txt,.doc,.docx,.md"
                  />
                </div>
              </Button>
            </div>
          </div>
          
          {/* File Size Error Message */}
          {fileSizeError && (
            <div className="mt-2 p-3 bg-destructive/10 border border-destructive rounded-md">
              <div className="flex items-start gap-2">
                <svg 
                  className="size-4 text-destructive mt-0.5 shrink-0" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <div>
                  <p className="text-sm font-medium text-destructive" dangerouslySetInnerHTML={{ __html: fileSizeError }} />
                </div>
              </div>
            </div>
          )}

          {/* Uploaded Files List */}
          {form.watch('files')?.length > 0 && (
            <div className="space-y-2 mt-2">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Uploaded Files</h3>
                <span className="text-xs text-muted-foreground">
                  {form.watch('files').length} file
                  {form.watch('files').length !== 1 ? 's' : ''} 
                  ({calculateTotalSize(form.watch('files'))})
                </span>
              </div>
              {form.watch('files').map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 bg-background rounded-md border border-border"
                >
                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <svg
                        className="size-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                    </div>

                    <div className="flex flex-col min-w-0 flex-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium truncate">
                          {file.name}
                        </span>
                        <span className="text-xs text-muted-foreground ml-2 whitespace-nowrap">
                          {file.size && formatFileSize(file.size)}
                        </span>
                      </div>

                      {file.status === 'uploading' && (
                        <div className="w-full mt-1">
                          <div className="h-1 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-primary transition-all duration-300"
                              style={{
                                width: `${uploadProgress[file.id] || 0}%`,
                              }}
                            />
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <span className="text-xs text-muted-foreground">
                              Uploading...{' '}
                              {uploadProgress[file.id]?.toFixed(0) || 0}%
                            </span>
                          </div>
                        </div>
                      )}

                      {file.status === 'error' && (
                        <span className="text-xs text-destructive flex items-center gap-1 mt-1">
                          <X className="size-4" />
                          {file.error || 'Upload failed'}
                        </span>
                      )}

                      {file.status === 'completed' && (
                        <span className="text-xs text-green-500 flex items-center gap-1 mt-1">
                          <Check className="size-4" />
                          Upload complete
                        </span>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setFileToDelete(file.id);
                      setShowDeleteConfirm(true);
                    }}
                    disabled={file.status === 'uploading' || isDeletingFile === file.id}
                    className="h-8 w-8 rounded-full"
                  >
                    {isDeletingFile === file.id ? (
                      <Loader2 className="size-4 animate-spin" />
                    ) : (
                      <X className="size-4" />
                    )}
                    <span className="sr-only">Remove file</span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Quick Messages Section */}
      <div>
        <QuickMessagesEditor
          quickMessages={quickMessages}
          setQuickMessages={setQuickMessages}
        />
      </div>

      {/* Confirmation Dialog for File Deletion */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => {
          if (fileToDelete) {
            removeFile(fileToDelete);
          }
        }}
        title="Remove File"
        description={
          window.location.pathname === '/create'
            ? "Are you sure you want to remove this file? This action cannot be undone."
            : "Are you sure you want to remove this file from your agent's knowledge base? Your agent will no longer have access to its content. This action cannot be undone."
        }
        confirmText={isDeletingFile ? "Removing..." : "Remove"}
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  );
}
