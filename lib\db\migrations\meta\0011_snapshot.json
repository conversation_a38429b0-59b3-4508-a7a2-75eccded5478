{"id": "1e0eaf17-441d-4552-a1d8-35987bbf32b5", "prevId": "ce81e29b-c2a5-4c16-89c9-8bd9b448af95", "version": "7", "dialect": "postgresql", "tables": {"public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "agent_name": {"name": "agent_name", "type": "text", "primaryKey": false, "notNull": true}, "instruction": {"name": "instruction", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "jsonb", "primaryKey": false, "notNull": false}, "monetization": {"name": "monetization", "type": "jsonb", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "jsonb", "primaryKey": false, "notNull": false}, "quick_messages": {"name": "quick_messages", "type": "jsonb", "primaryKey": false, "notNull": false}, "files": {"name": "files", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"agents_slug_unique": {"name": "agents_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "agentId": {"name": "agentId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Chat_userId_User_id_fk": {"name": "Chat_userId_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Chat_agentId_agents_id_fk": {"name": "Chat_agentId_agents_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "agents", "columnsFrom": ["agentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.CreatorPlans": {"name": "CreatorPlans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "monthly_price": {"name": "monthly_price", "type": "integer", "primaryKey": false, "notNull": true}, "yearly_price": {"name": "yearly_price", "type": "integer", "primaryKey": false, "notNull": true}, "tokens_allowed": {"name": "tokens_allowed", "type": "integer", "primaryKey": false, "notNull": true}, "knowledge_base_size": {"name": "knowledge_base_size", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "agents_allowed": {"name": "agents_allowed", "type": "integer", "primaryKey": false, "notNull": true}, "top_up_tokens": {"name": "top_up_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "top_up_knowledge_base": {"name": "top_up_knowledge_base", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "top_up_token_price": {"name": "top_up_token_price", "type": "integer", "primaryKey": false, "notNull": false}, "top_up_knowledge_base_price": {"name": "top_up_knowledge_base_price", "type": "integer", "primaryKey": false, "notNull": false}, "embeddable_widget": {"name": "embeddable_widget", "type": "boolean", "primaryKey": false, "notNull": false}, "monetization_included": {"name": "monetization_included", "type": "boolean", "primaryKey": false, "notNull": false}, "crypto_and_fiat_payments": {"name": "crypto_and_fiat_payments", "type": "boolean", "primaryKey": false, "notNull": false}, "analytics": {"name": "analytics", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "support_type": {"name": "support_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "other_features": {"name": "other_features", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Document": {"name": "Document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Document_userId_User_id_fk": {"name": "Document_userId_User_id_fk", "tableFrom": "Document", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Document_id_createdAt_pk": {"name": "Document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}}, "public.Message_v2": {"name": "Message_v2", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parts": {"name": "parts", "type": "json", "primaryKey": false, "notNull": true}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "prompt_tokens": {"name": "prompt_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "completion_tokens": {"name": "completion_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "total_tokens": {"name": "total_tokens", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Message_v2_chatId_Chat_id_fk": {"name": "Message_v2_chatId_Chat_id_fk", "tableFrom": "Message_v2", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Message_chatId_Chat_id_fk": {"name": "Message_chatId_Chat_id_fk", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Suggestion": {"name": "Suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "documentId": {"name": "documentId", "type": "uuid", "primaryKey": false, "notNull": true}, "documentCreatedAt": {"name": "documentCreatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "originalText": {"name": "originalText", "type": "text", "primaryKey": false, "notNull": true}, "suggestedText": {"name": "suggestedText", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isResolved": {"name": "isResolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Suggestion_userId_User_id_fk": {"name": "Suggestion_userId_User_id_fk", "tableFrom": "Suggestion", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk": {"name": "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk", "tableFrom": "Suggestion", "tableTo": "Document", "columnsFrom": ["documentId", "documentCreatedAt"], "columnsTo": ["id", "createdAt"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Suggestion_id_pk": {"name": "Suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.UserSubscriptions": {"name": "UserSubscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agentId": {"name": "agentId", "type": "uuid", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "allowed_free_messages": {"name": "allowed_free_messages", "type": "integer", "primaryKey": false, "notNull": true}, "is_paid": {"name": "is_paid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "subscription_type": {"name": "subscription_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"UserSubscriptions_agentId_agents_id_fk": {"name": "UserSubscriptions_agentId_agents_id_fk", "tableFrom": "UserSubscriptions", "tableTo": "agents", "columnsFrom": ["agentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "UserSubscriptions_userId_User_id_fk": {"name": "UserSubscriptions_userId_User_id_fk", "tableFrom": "UserSubscriptions", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Vote_v2": {"name": "Vote_v2", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_v2_chatId_Chat_id_fk": {"name": "Vote_v2_chatId_Chat_id_fk", "tableFrom": "Vote_v2", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_v2_messageId_Message_v2_id_fk": {"name": "Vote_v2_messageId_Message_v2_id_fk", "tableFrom": "Vote_v2", "tableTo": "Message_v2", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_v2_chatId_messageId_pk": {"name": "Vote_v2_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}}, "public.Vote": {"name": "Vote", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_chatId_Chat_id_fk": {"name": "Vote_chatId_Chat_id_fk", "tableFrom": "Vote", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_messageId_Message_id_fk": {"name": "Vote_messageId_Message_id_fk", "tableFrom": "Vote", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_chatId_messageId_pk": {"name": "Vote_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}