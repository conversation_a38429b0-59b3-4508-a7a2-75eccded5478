{"version": "7", "dialect": "postgresql", "entries": [{"idx": 0, "version": "7", "when": 1728598022383, "tag": "0000_keen_devos", "breakpoints": true}, {"idx": 1, "version": "7", "when": 1730207363999, "tag": "0001_sparkling_blue_marvel", "breakpoints": true}, {"idx": 2, "version": "7", "when": 1730725226313, "tag": "0002_wandering_riptide", "breakpoints": true}, {"idx": 3, "version": "7", "when": 1733403031014, "tag": "0003_cloudy_glorian", "breakpoints": true}, {"idx": 4, "version": "7", "when": 1733945232355, "tag": "0004_odd_slayback", "breakpoints": true}, {"idx": 5, "version": "7", "when": 1741934630596, "tag": "0005_wooden_whistler", "breakpoints": true}, {"idx": 6, "version": "7", "when": 1744722264877, "tag": "0006_true_mach_iv", "breakpoints": true}, {"idx": 7, "version": "7", "when": 1744733368424, "tag": "0007_wandering_madripoor", "breakpoints": true}, {"idx": 8, "version": "7", "when": 1744740192826, "tag": "0008_romantic_stranger", "breakpoints": true}, {"idx": 9, "version": "7", "when": 1744777935873, "tag": "0009_clean_hammerhead", "breakpoints": true}, {"idx": 10, "version": "7", "when": 1744849880254, "tag": "0010_fair_weapon_omega", "breakpoints": true}, {"idx": 11, "version": "7", "when": 1745041530448, "tag": "0011_rainy_psynapse", "breakpoints": true}, {"idx": 12, "version": "7", "when": 1745331263000, "tag": "0012_alter_logo_type", "breakpoints": true}, {"idx": 18, "version": "7", "when": 1745331264000, "tag": "0018_drop_monetization", "breakpoints": true}, {"idx": 22, "version": "7", "when": 1745331268000, "tag": "0022_add_price_access_columns", "breakpoints": true}, {"idx": 23, "version": "7", "when": 1745331269000, "tag": "0023_create_creator_subscriptions", "breakpoints": true}, {"idx": 24, "version": "7", "when": 1745331270000, "tag": "0027_simplify_token_columns", "breakpoints": true}, {"idx": 25, "version": "7", "when": 1745331271000, "tag": "0028_add_creator_balance_columns", "breakpoints": true}, {"idx": 26, "version": "7", "when": 1745331272000, "tag": "0029_add_timestamps_to_creator_balance", "breakpoints": true}, {"idx": 27, "version": "7", "when": 1745331273000, "tag": "0030_add_left_columns_to_creator_balance", "breakpoints": true}, {"idx": 28, "version": "7", "when": 1745331274000, "tag": "0031_add_userid_to_agents", "breakpoints": true}, {"idx": 29, "version": "7", "when": 1745331275000, "tag": "0032_add_subscription_status", "breakpoints": true}, {"idx": 30, "version": "7", "when": 1745331276000, "tag": "0035_rename_creator_balance_add_expiry", "breakpoints": true}, {"idx": 36, "version": "0036", "when": 1714068314000, "tag": "0036_add_pending_feature_generation", "breakpoints": true}, {"idx": 37, "version": "0037", "when": 1714068414000, "tag": "0037_replace_user_ratings_with_model", "breakpoints": true}, {"idx": 24, "version": "7", "when": 1745331270000, "tag": "0024_create_stripe_events", "breakpoints": true}]}