import { config } from 'dotenv';
import { randomUUID } from 'crypto';
import { eq } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { agents } from './schema';
import fs from 'fs';
import path from 'path';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  console.error('❌ POSTGRES_URL is not defined in environment variables');
  process.exit(1);
}

// Create database client after environment variables are loaded
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

async function seedAgents() {
  try {
    console.log('⏳ Seeding agents from converted JSON file...');
    // biome-ignore lint: Forbidden non-null assertion.
    console.log(`Using database URL: ${process.env.POSTGRES_URL!.split('@')[1]}`); // Log only the host part for security

    // Read the agents data from the converted JSON file
    const agentsDataPath = path.join(process.cwd(), 'converted-agents.json');
    
    if (!fs.existsSync(agentsDataPath)) {
      console.error(`❌ File not found: ${agentsDataPath}`);
      console.log('🔄 Run "npx tsx lib/db/convert-agents.ts" first to create the converted file');
      return;
    }
    
    const agentsData = JSON.parse(fs.readFileSync(agentsDataPath, 'utf8'));
    console.log(`Found ${agentsData.length} agents to seed`);

    // Clear existing agents if requested
    const shouldClearExisting = process.env.CLEAR_EXISTING_AGENTS === 'true';
    if (shouldClearExisting) {
      console.log('🗑️ Clearing existing agents...');
      await db.delete(agents);
    }
    
    // Insert each agent
    for (const agent of agentsData) {
      // Check if agent already exists
      const existingAgents = await db.select({ slug: agents.slug }).from(agents).where(
        eq(agents.slug, agent.slug)
      );

      if (existingAgents.length > 0) {
        console.log(`⏭️ Agent '${agent.agentName}' (${agent.slug}) already exists, skipping.`);
        continue;
      }

      console.log(`🌱 Seeding agent: ${agent.agentName} (${agent.slug})`);
      
      await db.insert(agents).values({
        id: randomUUID(),
        slug: agent.slug,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: agent.email,
        agentName: agent.agentName,
        instruction: agent.instruction,
        description: agent.description,
        status: agent.status || 'active',
        visibility: agent.visibility,
        monetization: agent.monetization,
        logo: agent.logo,
        quickMessages: agent.quickMessages,
        files: agent.files || []
      });
      
      console.log(`✅ Agent '${agent.agentName}' seeded successfully!`);
    }

    console.log('🎉 All agents seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding agents:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
  }
}

// Run the seed function
seedAgents().then(() => {
  console.log('✅ Seed process completed');
  process.exit(0);
});
