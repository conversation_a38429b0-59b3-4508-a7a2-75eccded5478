import { sql } from 'drizzle-orm';
import { pgTable, uuid, text, timestamp } from 'drizzle-orm/pg-core';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

export async function up(db: PostgresJsDatabase) {
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS "payout_settings" (
      "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      "userId" uuid NOT NULL REFERENCES "User"("id") ON DELETE CASCADE,
      "paypal_email" text NOT NULL,
      "created_at" timestamp DEFAULT now(),
      "updated_at" timestamp DEFAULT now()
    );
    
    CREATE INDEX IF NOT EXISTS "payout_settings_user_id_idx" ON "payout_settings" ("userId");
  `);
}

export async function down(db: PostgresJsDatabase) {
  await db.execute(sql`
    DROP TABLE IF EXISTS "payout_settings";
  `);
}
