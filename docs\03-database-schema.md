# Database Schema

## Overview

The BuildThatIdea platform uses PostgreSQL as its primary database, with Drizzle ORM providing a type-safe interface for database operations. The schema is defined in `/lib/db/schema.ts` and includes tables for users, agents, chats, messages, subscriptions, payments, and more.

## Core Tables

### User Table

The `User` table stores information about all users of the platform:

```typescript
export const user = pgTable('User', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  email: varchar('email', { length: 64 }).notNull(),
  password: varchar('password', { length: 64 }),
  type: varchar('type', { enum: ['user', 'creator', 'both'] }).notNull().default('user'),
  verified: boolean('verified').notNull().default(false),
  verification_date: timestamp('verification_date'),
  referral_code_used: varchar('referral_code_used', { length: 64 }),
});
```

Key fields:
- `id`: Unique identifier for the user
- `email`: User's email address (used for login)
- `password`: Hashed password
- `type`: User type (user, creator, or both)
- `verified`: Whether the email has been verified
- `verification_date`: When the email was verified
- `referral_code_used`: Referral code used during registration

### Agents Table

The `agents` table stores information about AI agents created on the platform:

```typescript
export const agents = pgTable('agents', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  slug: text('slug').notNull().unique(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  userId: uuid('userId').notNull().references(() => user.id),
  agentName: text('agent_name').notNull(),
  instruction: text('instruction'),
  description: text('description'),
  status: varchar('status', { enum: ['active', 'inactive', 'draft'] }).notNull().default('draft'),
  visibility: varchar('visibility', { enum: ['public', 'private'] }).notNull().default('public'),
  model: text('model'),
  logo: varchar('logo', { length: 2048 }),
  quickMessages: jsonb('quick_messages'),
  files: jsonb('files'),
  accessLevel: varchar('access_level', { enum: ['free', 'subscription', 'lifetime'] }).notNull().default('free'),
  price: real('price'),
  endpoint: text('endpoint'),
  token: text('token'),
});
```

Key fields:
- `id`: Unique identifier for the agent
- `slug`: URL-friendly identifier for the agent
- `userId`: Reference to the creator's user ID
- `agentName`: Display name of the agent
- `instruction`: System instructions for the agent
- `description`: Public description of the agent
- `status`: Current status (active, inactive, draft)
- `visibility`: Whether the agent is public or private
- `model`: AI model used by the agent
- `accessLevel`: Whether the agent is free, subscription-based, or lifetime access
- `price`: Price for paid agents
- `endpoint`: Vector database endpoint for knowledge retrieval
- `token`: Encrypted token for vector database access

### Chat Table

The `Chat` table stores information about chat sessions:

```typescript
export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull(),
  title: text('title').notNull(),
  userId: uuid('userId').notNull().references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] }).notNull().default('private'),
  agentId: uuid('agentId').notNull().references(() => agents.id),
});
```

Key fields:
- `id`: Unique identifier for the chat
- `createdAt`: When the chat was created
- `title`: Title of the chat
- `userId`: Reference to the user who created the chat
- `visibility`: Whether the chat is public or private
- `agentId`: Reference to the agent being chatted with

### Message Table

The `message` table stores individual messages in chats:

```typescript
export const message = pgTable('Message_v2', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId').notNull().references(() => chat.id),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull(),
  tokens: integer('tokens'),
});
```

Key fields:
- `id`: Unique identifier for the message
- `chatId`: Reference to the chat the message belongs to
- `role`: Role of the message sender (user or assistant)
- `parts`: JSON content of the message
- `attachments`: Any attachments to the message
- `createdAt`: When the message was created
- `tokens`: Number of tokens used by the message

## Subscription and Payment Tables

### UserSubscriptions Table

The `UserSubscriptions` table tracks user subscriptions to agents:

```typescript
export const userSubscriptions = pgTable('UserSubscriptions', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  agentId: uuid('agentId').notNull().references(() => agents.id),
  userId: uuid('userId').notNull().references(() => user.id),
  status: statusEnum('status').notNull().default('active'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  expiresAt: timestamp('expiresAt'),
  cancelAt: timestamp('cancelAt'),
  canceledAt: timestamp('canceledAt'),
  currentPeriodStart: timestamp('currentPeriodStart'),
  currentPeriodEnd: timestamp('currentPeriodEnd'),
  stripeSubscriptionId: text('stripeSubscriptionId'),
  stripePriceId: text('stripePriceId'),
  stripeCustomerId: text('stripeCustomerId'),
  subscriptionType: subscriptionTypeEnum('subscriptionType').default('monthly'),
});
```

### CreatorSubscriptions Table

The `CreatorSubscriptions` table tracks creator subscriptions to the platform:

```typescript
export const creatorSubscriptions = pgTable('CreatorSubscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').notNull().references(() => user.id, { onDelete: 'cascade' }),
  planId: uuid('planId').notNull().references(() => creatorPlans.id),
  status: subscriptionStatusEnum('status').notNull().default('active'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  expiresAt: timestamp('expiresAt'),
  stripeSubscriptionId: text('stripeSubscriptionId'),
  stripePriceId: text('stripePriceId'),
  stripeCustomerId: text('stripeCustomerId'),
  subscriptionType: subscriptionTypeEnum('subscriptionType').default('monthly'),
});
```

### Payment Tables

The platform includes several tables for tracking payments:

- `creatorPayments`: Tracks payments to creators
- `userPayments`: Tracks payments from users
- `payoutSettings`: Stores creator payout information
- `stripeEvents`: Logs Stripe webhook events
- `stripeCustomers`: Maps users to Stripe customer IDs

## Knowledge Base Tables

### Agent Knowledge Files

The `agentKnowledgeFiles` table tracks files uploaded to agent knowledge bases:

```typescript
export const agentKnowledgeFiles = pgTable("agent_knowledge_files", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  agentId: uuid("agent_id").notNull().references(() => agents.id, { onDelete: 'cascade' }),
  fileName: text("file_name").notNull(),
  fileUrl: text("file_url").notNull(),
  fileSize: integer("file_size"),
  fileType: text("file_type"),
  status: text("status").default("pending"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  processingError: text("processing_error"),
  chunkCount: integer("chunk_count").default(0),
  vectorized: boolean("vectorized").default(false),
});
```

Key fields:
- `id`: Unique identifier for the file
- `agentId`: Reference to the agent the file belongs to
- `fileName`: Name of the uploaded file
- `fileUrl`: URL where the file is stored (typically in S3)
- `fileSize`: Size of the file in bytes
- `fileType`: MIME type of the file
- `status`: Processing status (pending, processing, completed, error)
- `chunkCount`: Number of chunks the file was split into for vectorization
- `vectorized`: Whether the file has been vectorized

## Other Tables

### Agent Profiles

The `agentProfiles` table stores additional information about agents:

```typescript
export const agentProfiles = pgTable('agent_profiles', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  agentId: uuid('agent_id').notNull().references(() => agents.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  bio: text('bio'),
  expertise: text('expertise'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
```

### Referral Codes

The `referralCodes` table tracks referral codes for the platform:

```typescript
export const referralCodes = pgTable("referral_codes", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  code: varchar("code", { length: 64 }).notNull().unique(),
  used_count: integer("used_count").default(0),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
});
```

## Database Relationships

The database schema includes several relationships between tables:

1. **User to Agents**: One-to-many relationship (a user can create multiple agents)
2. **User to Chats**: One-to-many relationship (a user can have multiple chats)
3. **Agent to Chats**: One-to-many relationship (an agent can be in multiple chats)
4. **Chat to Messages**: One-to-many relationship (a chat can have multiple messages)
5. **Agent to Knowledge Files**: One-to-many relationship (an agent can have multiple knowledge files)
6. **User to Subscriptions**: One-to-many relationship (a user can have multiple subscriptions)

## Database Migrations

The database schema is managed through Drizzle ORM migrations, which are stored in the `/migrations` directory. Migrations are applied using the `db:migrate` script defined in `package.json`.

## Indexes and Performance

The database schema includes indexes on frequently queried fields to improve performance:

1. Primary keys on all tables
2. Foreign key relationships for referential integrity
3. Unique constraints on fields like `slug` in the `agents` table
4. Timestamps for tracking creation and updates

## Data Types

The schema uses a variety of PostgreSQL data types:

- `uuid`: For unique identifiers
- `varchar`: For string fields with length constraints
- `text`: For longer text fields
- `timestamp`: For date and time fields
- `boolean`: For true/false fields
- `integer`: For numeric fields
- `real`: For decimal numbers
- `jsonb`: For structured JSON data
- `json`: For JSON data without indexing

## Enums

The schema defines several PostgreSQL enums for constrained field values:

```typescript
export const subscriptionTypeEnum = pgEnum('subscription_type', ['trial', 'monthly', 'yearly']);
export const statusEnum = pgEnum('status', ['active', 'inactive', 'pending', 'cancelled']);
export const subscriptionStatusEnum = pgEnum('creator_subscription_status', ['active', 'inactive']);
```
