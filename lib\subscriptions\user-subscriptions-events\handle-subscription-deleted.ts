import { markUserSubscriptionInactive, updateCancellationRequest, findActiveUserSubscription } from '@/lib/db/subscription-utils';

export async function handleDeletedSubscriptionForUser(
  userId: string,
) {
  // Find the active subscription for this user
  console.log("Finding active subscription for user:", userId);
  const existingSubscription = await findActiveUserSubscription(userId);

  if (!existingSubscription) {
    console.log('No active subscription found to cancel');
    return;
  }

  try {
    // Update the subscription status to cancelled
    console.log('Updating subscription status to inactive...');
    const updatedSubscription = await markUserSubscriptionInactive(existingSubscription.id);
    if (!updatedSubscription) {
      throw new Error('Failed to update subscription status');
    }
    console.log('Successfully updated status of subscription:', updatedSubscription.id);

    // Update the cancellation request status if it exists
    const updatedRequest = await updateCancellationRequest(existingSubscription.id);
    if (updatedRequest) {
      console.log('Successfully updated cancellation request:', updatedRequest.id);
    } else {
      console.log('No cancellation request found for this subscription');
    }

  } catch (error) {
    console.error('Error updating subscription or cancellation request:', error);
    throw error;
  }

  console.log('Subscription cancelled:', {
    userId,
    subscriptionId: existingSubscription.id,
  });
}
