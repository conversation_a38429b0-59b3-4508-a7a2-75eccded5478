'use client';

import React, { useState, useCallback, useEffect, useRef, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { formSchema, type FormValues, type FileEntry } from '@/components/agent-form/types';
import { useToast } from '@/components/ui/use-toast';
import { MultiStepForm } from '@/components/agent-form/multi-step-form';
import { BasicInfoStep } from '@/components/agent-form/steps/basic-info-step';
import { ConfigurationStep } from '@/components/agent-form/steps/configuration-step';
import { CapabilitiesStep } from '@/components/agent-form/steps/capabilities-step';
import { MonetizationStep } from '@/components/agent-form/steps/monetization-step';
import { Use<PERSON>, <PERSON><PERSON>, Settings2, DollarSign } from 'lucide-react';
import { debounce } from 'lodash';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

// Using the form schema and types from our shared file

// Define the steps for our multi-step form
const formSteps = [
  {
    id: 'basic-info',
    title: 'Basic Information',
    description: 'Provide your agent details',
    icon: <User className="size-4" />,
  },
  {
    id: 'capabilities',
    title: 'Capabilities',
    description: 'Define what your agent can do',
    icon: <Bot className="size-4" />,
  },
  {
    id: 'configuration',
    title: 'Configuration',
    description: 'Configure model and behavior',
    icon: <Settings2 className="size-4" />,
  },
  {
    id: 'monetization',
    title: 'Monetization',
    description: 'Set pricing for your agent',
    icon: <DollarSign className="size-4" />,
  },
];

export default function EditAgentPage() {
  return (
    <Suspense fallback={<EditPageLoading />}>
      <EditAgentContent />
    </Suspense>
  );
}

// Loading fallback component
function EditPageLoading() {
  return (
    <div className="bg-background min-h-screen flex items-center justify-center lg:p-6">
      <div className="w-full max-w-7xl lg:px-4 xl:px-6 h-full">
        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border overflow-hidden">
          <div className="p-6 space-y-6">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function EditAgentContent() {
  // URL and routing
  const router = useRouter();
  const searchParams = useSearchParams();
  const agentId = searchParams.get('id');
  const { data: session, status } = useSession();
  const { toast } = useToast();

  // Page state management
  const [pageState, setPageState] = useState<'loading' | 'ready' | 'error'>('loading');
  const [agentData, setAgentData] = useState<any>(null);
  const [isActiveAgent, setIsActiveAgent] = useState(false);
  const [originalSlug, setOriginalSlug] = useState<string>('');
  const [originalFiles, setOriginalFiles] = useState<any[]>([]);
  const dataFetchedRef = useRef(false);

  // Enhanced error handling with retry mechanism
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Form state
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      agentName: '',
      slug: '',
      instruction: '',
      description: '',
      model: 'gpt-4o',
      isPublic: true,
      isPaid: false,
      pricingType: 'subscription',
      price: '0',
      accessLevel: 'free',
      quickMessages: [],
      visibility: 'public',
      files: [],
      websiteUrl: '',
      selectedLinks: [],
    },
  });

  // Slug validation state
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [slugExists, setSlugExists] = useState(false);
  const [isOwnDraft, setIsOwnDraft] = useState(false);

  // Logo upload state
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingDraft, setIsSavingDraft] = useState(false);

  // New loading states for button disable conditions
  const [isImprovingInstruction, setIsImprovingInstruction] = useState(false);
  const [isUploadingLogoState, setIsUploadingLogoState] = useState(false);

  // Draft agent state
  const [draftAgentId, setDraftAgentId] = useState<string | null>(null);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  // Plan limits state
  const [planLimits, setPlanLimits] = useState<{
    canCreate: boolean;
    message: string;
    agentsLeft: number;
    totalAgents: number;
    currentAgentCount: number;
    plan?: any;
  } | null>(null);

  const [isPlanCheckLoading, setIsPlanCheckLoading] = useState(false);
  // File upload state
  const [isUploadingFiles, setIsUploadingFiles] = useState<boolean>(false);

  // Authentication and agent ID validation
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/verify');
      return;
    }

    if (status === 'authenticated' && !agentId) {
      toast({
        title: 'No agent selected',
        description: 'No agent ID provided for editing',
        variant: 'destructive',
      });
      router.push('/dashboard/agents');
      return;
    }
  }, [status, agentId, router, toast]);

  // Fetch agent data function
  const fetchAgentData = useCallback(async () => {
    if (!session?.user?.email || !agentId) {
      setPageState('error');
      return;
    }

    try {
      setPageState('loading');
      const response = await fetch(`/api/agents?id=${agentId}`);

      if (!response.ok) {
        console.error(`Error loading agent: ${response.status} ${response.statusText}`);
        setPageState('error');
        return;
      }

      const data = await response.json();

      if (!data.agent) {
        setPageState('error');
        return;
      }

      const agent = data.agent;
      
      // Store original data for comparison
      setOriginalSlug(agent.slug);
      setIsActiveAgent(agent.status === 'active');
      setAgentData(agent);

      // Process files from database format to FileEntry format
      let processedFiles: FileEntry[] = [];
      if (agent.files) {
        try {
          const filesData = typeof agent.files === 'string' 
            ? JSON.parse(agent.files) 
            : agent.files;
          
          processedFiles = Array.isArray(filesData)
            ? filesData.map((file) => ({
                id: crypto.randomUUID(),
                name: file.file_name || file.name || 'Unknown file',
                status: 'completed' as const,
                url: file.url,
                size: file.file_size ? parseInt(file.file_size) : 0,
                progress: 100,
              }))
            : [];
        } catch (error) {
          console.error('Error processing files from agent data:', error);
        }
      }
      setOriginalFiles(processedFiles);

      // Populate form with agent data
      form.reset({
        id: agent.id,
        agentName: agent.agentName || '',
        slug: agent.slug || '',
        description: agent.description || '',
        instruction: agent.instruction || '',
        model: agent.model || 'gpt-4o',
        isPublic: agent.visibility === 'public',
        isPaid: agent.accessLevel !== 'free',
        pricingType: 'subscription',
        price: agent.price ? String(agent.price) : '0',
        accessLevel: agent.accessLevel === 'lifetime' ? 'subscription' : agent.accessLevel || 'free',
        visibility: agent.visibility || 'private',
        files: processedFiles,
        websiteUrl: agent.selectedLinks?.[0]?.website || '',
        selectedLinks: agent.selectedLinks || [],
        quickMessages: Array.isArray(agent.quickMessages) ? agent.quickMessages : [],
      });

      // Set logo preview and S3 URL if available
      if (agent.logo) {
        setLogoPreview(agent.logo);
        setLogoUrl(agent.logo);
      }

      // Store agent ID for file uploads
      localStorage.setItem('draftAgentId', agent.id);
      setDraftAgentId(agent.id);

      setPageState('ready');
    } catch (error) {
      console.error('Error loading agent:', error);
      setPageState('error');
    }
  }, [session?.user?.email, agentId, form]);

  // Load agent data on component mount
  useEffect(() => {
    if (
      status === 'authenticated' &&
      session?.user?.email &&
      agentId &&
      !dataFetchedRef.current
    ) {
      dataFetchedRef.current = true;
      fetchAgentData();
    } else if (status !== 'loading' && !dataFetchedRef.current) {
      dataFetchedRef.current = true;
      setPageState('error');
    }
  }, [status, session, agentId, fetchAgentData]);

  // Retry mechanism for failed data loading
  const retryFetchAgentData = useCallback(async () => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      toast({
        title: `Retrying... (${retryCount + 1}/${maxRetries})`,
        description: 'Attempting to reload agent data.',
      });
      dataFetchedRef.current = false; // Reset to allow refetch
      await fetchAgentData();
    } else {
      toast({
        title: 'Failed to load agent',
        description: 'Unable to load agent data after multiple attempts. Please try again later.',
        variant: 'destructive',
      });
    }
  }, [retryCount, fetchAgentData, toast]);

  // Reset retry count on successful load
  useEffect(() => {
    if (pageState === 'ready') {
      setRetryCount(0);
    }
  }, [pageState]);

  // Set document title
  useEffect(() => {
    document.title = agentData?.agentName 
      ? `Edit ${agentData.agentName} | BuildThatIdea`
      : 'Edit Agent | BuildThatIdea';
  }, [agentData?.agentName]);

  // Ensure form validation is properly connected
  useEffect(() => {
    // Set up form validation mode for real-time feedback
    form.clearErrors();
  }, [form]);

  // Check if slug is available - modified for edit mode
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const checkSlugAvailability = useCallback(
    debounce(async (slug: string) => {
      try {
        if (!session?.user?.email || !slug) {
          setSlugExists(false);
          setIsCheckingSlug(false);
          setIsOwnDraft(false);
          return true;
        }

        // Don't check if we're in edit mode and the slug hasn't changed
        if (isActiveAgent && slug === originalSlug) {
          setSlugExists(false);
          setIsCheckingSlug(false);
          return true;
        }

        setIsCheckingSlug(true);
        const response = await fetch(
          `/api/agents/check-slug?slug=${slug}&userEmail=${session.user.email}&agentId=${agentId || ''}`,
        );
        const data = await response.json();

        setIsOwnDraft(data.isOwnDraft || false);

        if (data.isOwnDraft && data.draftAgentId) {
          setDraftAgentId(data.draftAgentId);
        }

        // If the slug exists but belongs to the current agent, it's available for use
        const isAvailable = !data.exists || data.isOwnDraft;
        setSlugExists(!isAvailable);
        setIsCheckingSlug(false);

        return isAvailable;
      } catch (error) {
        console.error('Error checking slug availability:', error);
        setSlugExists(false);
        setIsCheckingSlug(false);
        return true;
      }
    }, 500),
    [session, setIsCheckingSlug, setSlugExists, setIsOwnDraft, setDraftAgentId, isActiveAgent, originalSlug, agentId],
  );

  // REMOVED: Draft saving functionality - not used in edit mode
  // Edit mode only saves on final submission, no intermediate saves

  // Check plan limits
  const checkPlanLimits = useCallback(async () => {
    if (!session?.user?.email) return;

    setIsPlanCheckLoading(true);

    try {
      const response = await fetch('/api/agents/check-plan-limits');
      if (!response.ok) {
        throw new Error('Failed to check plan limits');
      }
      const data = await response.json();
      setPlanLimits(data);
    } catch (error) {
      console.error('Error checking plan limits:', error);
    } finally {
      setIsPlanCheckLoading(false);
    }
  }, [session?.user?.email]);

  // Function to determine submit button text based on plan status
  const getSubmitButtonText = () => {
    if (!planLimits) return isActiveAgent ? 'Update Agent' : 'Launch Agent';

    // For active agents, always show Update Agent
    if (isActiveAgent) {
      return 'Update Agent';
    }

    // For draft agents, show same options as create page
    if (planLimits.currentAgentCount === 0 && !planLimits.plan) {
      return 'Start your free trial';
    } else if (!planLimits.canCreate) {
      if ((planLimits.currentAgentCount ?? 0) >= 3) {
        return 'Upgrade to enterprise plan';
      } else {
        return 'Upgrade to pro plan';
      }
    }

    return 'Launch Agent';
  };

  // Function to initiate the free trial checkout (only for draft agents)
  const initiateFreeTrial = async () => {
    if (isActiveAgent) return; // No trial needed for active agents
    
    try {
      const formValues = form.getValues();
      const response = await fetch('/api/agents/free-trial-hobby', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId: draftAgentId,
          agentSlug: formValues.slug || 'edited-agent',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create free trial checkout');
      }

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        toast({
          title: 'Error',
          description: 'Unable to start free trial. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error initiating free trial:', error);
      toast({
        title: 'Error',
        description: 'Failed to start free trial. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle form submission - modified for edit mode
  const handleSubmit = async () => {
    if (!session?.user?.email) return;

    // Check the current button text to determine the action
    const buttonText = getSubmitButtonText();
    
    // Handle different upgrade scenarios (only for draft agents)
    if (!isActiveAgent) {
      if (buttonText === 'Start your free trial') {
        initiateFreeTrial();
        return;
      } else if (buttonText === 'Upgrade to enterprise plan') {
        window.open('https://buildthatidea.typeform.com/to/dX0HyMyI', '_blank');
        return;
      } else if (buttonText === 'Upgrade to pro plan') {
        router.push('/billing');
        return;
      }
    }

    // Continue with normal agent update for 'Update Agent' or 'Launch Agent' buttons

    // Check if files are still uploading
    const files = form.getValues('files') || [];
    const fileUploadInProgress = (
      (Array.isArray(files) &&
        files.some((file) => file.status === 'uploading')) ||
      isUploadingFiles
    );

    if (fileUploadInProgress) {
      toast({
        title: 'Files uploading',
        description: 'Please wait for all files to finish uploading before updating the agent.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const data = form.getValues();
      
      // Validate required fields with edit-specific messages
      if (!data.agentName || data.agentName.length < 4) {
        toast({
          title: 'Validation Error',
          description: 'Agent name must be at least 4 characters',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      if (!data.slug || data.slug.length < 4) {
        toast({
          title: 'Validation Error', 
          description: isActiveAgent 
            ? 'URL path must be at least 4 characters. Active agents require valid URLs.'
            : 'URL path must be at least 4 characters',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      if (!data.instruction || data.instruction.length < 10) {
        toast({
          title: 'Validation Error',
          description: 'Instructions must be at least 10 characters',
          variant: 'destructive', 
        });
        setIsSubmitting(false);
        return;
      }

      if (!data.description || data.description.length < 10) {
        toast({
          title: 'Validation Error',
          description: 'Description must be at least 10 characters',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Check if slug exists and is not the original (edit-specific validation)
      if (data.slug !== originalSlug && slugExists) {
        toast({
          title: 'URL not available',
          description: isActiveAgent 
            ? 'This URL path is already taken. Active agents cannot use duplicate URLs.'
            : 'This URL path is already taken. Please choose another.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Additional validation for active agents
      if (isActiveAgent) {
        // Prevent certain changes that could break live agents
        if (data.isPaid !== (agentData?.accessLevel !== 'free')) {
          toast({
            title: 'Invalid Change',
            description: 'Cannot change monetization settings for active agents. This ensures consistency for existing users.',
            variant: 'destructive',
          });
          setIsSubmitting(false);
          return;
        }
      }

      // For draft agents, verify user has an active plan before proceeding with launch
      if (!isActiveAgent && planLimits && !planLimits.canCreate) {
        // Check if this is a new user without any plan
        if (planLimits.currentAgentCount === 0 && !planLimits.plan) {
          toast({
            title: 'Starting free trial',
            description: 'Starting your free trial...',
          });
          initiateFreeTrial();
        } else if ((planLimits.currentAgentCount ?? 0) >= 3) {
          toast({
            title: 'Enterprise plan needed',
            description: 'Redirecting to enterprise plan form...',
          });
          window.open(
            'https://buildthatidea.typeform.com/to/dX0HyMyI',
            '_blank',
          );
        } else {
          toast({
            title: 'Plan limit reached',
            description: `You've reached your limit of ${planLimits.totalAgents} agent${planLimits.totalAgents !== 1 ? 's' : ''}. Redirecting to upgrade...`,
          });
          router.push('/billing');
        }
        setIsSubmitting(false);
        return;
      }

      // Process files to ensure they're in the correct format
      let formFiles = data.files;
      if (typeof formFiles === 'string') {
        try {
          formFiles = JSON.parse(formFiles);
        } catch (e) {
          console.error('Error parsing files string in handleSubmit:', e);
          formFiles = [];
        }
      } else if (!Array.isArray(formFiles)) {
        formFiles = [];
      }

      const processedFiles = formFiles
        ? formFiles
            .filter((file) => file.status === 'completed')
            .map((file) => ({
              url: file.url,
              file_name: file.name,
              file_size: file.size ? file.size.toString() : undefined,
            }))
        : [];

      // Handle deleted files (compare with original files)
      const currentFileUrls = processedFiles.map(f => f.url);
      const deletedFiles = originalFiles.filter(
        originalFile => !currentFileUrls.includes(originalFile.url)
      );

      // Process deleted links (URLs with 'to-delete' status)
      const deletedUrls: string[] = [];
      if (data.selectedLinks && Array.isArray(data.selectedLinks)) {
        data.selectedLinks.forEach((website) => {
          if (website.links && Array.isArray(website.links)) {
            website.links.forEach((link: { status: string; url: string; }) => {
              if (link.status === 'to-delete') {
                deletedUrls.push(link.url);
              }
            });
          }
        });
      }

      // Clean up 'to-delete' links before saving
      const cleanedLinks = data.selectedLinks?.map((website) => ({
        ...website,
        links: website.links?.filter((link: { status: string; }) => link.status !== 'to-delete') || []
      })) || [];

      // Ensure price is always a valid string
      let validatedData = { ...data, selectedLinks: cleanedLinks };
      if (validatedData.isPaid) {
        validatedData.price = validatedData.price
          ? validatedData.price.toString()
          : '0';
        if (!validatedData.price.trim() || isNaN(Number(validatedData.price))) {
          validatedData.price = '0';
        }
      } else {
        validatedData.price = '0';
      }

      // *** PHASE 1: Early Vector Setup (Only if needed) ***
      let vectorConfig = {};
      
      // Check if we need vector infrastructure
      const hasWebsite = validatedData.websiteUrl?.trim();
      const hasFiles = processedFiles.length > 0;
      const hasNewFiles = processedFiles.length !== originalFiles.length;
      const needsVectorSetup = hasWebsite || hasFiles || hasNewFiles;
      
      if (needsVectorSetup) {
        // console.log('🔧 Phase 1: Setting up vector infrastructure...');
        try {
          const indexResponse = await fetch('/api/agents/get-index', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agentId: agentId,
            }),
          });

          if (indexResponse.ok) {
            const indexData = await indexResponse.json();
            if (indexData.endpoint && indexData.token) {
              vectorConfig = {
                endpoint: indexData.endpoint,
                token: indexData.token,
              };
              // console.log('✅ Vector infrastructure ready');
            }
          } else {
            // console.log('⚠️ Vector setup failed, continuing without vector config');
          }
        } catch (error) {
          console.error('Error setting up vector infrastructure:', error);
          // console.log('⚠️ Vector setup failed, continuing without vector config');
        }
      } else {
        // console.log('ℹ️ Phase 1: Skipping vector setup (no files or URLs to process)');
      }

      // *** PHASE 2: Content Cleanup ***
      // console.log('🧹 Phase 2: Cleaning up removed content...');
      
      // Call delete-embeddings API if files have been deleted
      if (deletedFiles.length > 0) {
        try {
          // console.log('Deleting embeddings for removed files:', deletedFiles);
          const deleteResponse = await fetch('/api/agents/delete-embeddings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agentId: agentId,
            }),
          });

          if (!deleteResponse.ok) {
            console.error('Failed to delete embeddings for removed files');
          }
        } catch (error) {
          console.error('Error deleting embeddings:', error);
        }
      }

      // Handle deleted URLs in batch
      if (deletedUrls.length > 0) {
        try {
          const deleteUrlResponse = await fetch('/api/fire-crawl/delete-url', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              urls: deletedUrls,
              agentId: agentId,
            }),
          });

          if (deleteUrlResponse.ok) {
            const deleteResult = await deleteUrlResponse.json();
            if (deleteResult.totalDeletedChunks > 0) {
              // console.log(`✅ Successfully cleaned up embeddings for ${deleteResult.totalDeletedChunks} URL chunks`);
            }
          } else {
            console.error('Failed to delete URL embeddings');
          }
        } catch (error) {
          console.error('Error deleting URL embeddings:', error);
        }
      }

      // *** PHASE 3: Database Update with Vector Config ***
      // console.log('💾 Phase 3: Updating agent in database...');
      
      // Prepare the final submission data with vector config
      const finalSubmitData = {
        ...vectorConfig, // Include vector configuration from Phase 1 (BEFORE other fields to prevent override)
        id: agentId, // Use the agent ID from URL
        agentName: validatedData.agentName,
        slug: validatedData.slug,
        description: validatedData.description,
        instruction: validatedData.instruction,
        model: validatedData.model,
        isPublic: validatedData.isPublic,
        isPaid: validatedData.isPaid,
        pricingType: validatedData.pricingType,
        price: validatedData.price,
        accessLevel: validatedData.accessLevel,
        visibility: validatedData.visibility,
        quickMessages: validatedData.quickMessages || [],
        isDraft: false, // Always set to false (either update or launch)
        status: 'active', // Set to active (same as create page)
        logo: logoUrl || null, // Ensure logo is properly set
        files: processedFiles,
        websiteUrl: validatedData.websiteUrl,
        selectedLinks: cleanedLinks,
      };

      // console.log('📤 Submitting agent data for edit:', finalSubmitData);

      // Always use PUT for edit mode since we're updating an existing agent
      const response = await fetch(`/api/agents?id=${agentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(finalSubmitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update agent');
      }

      const responseData = await response.json();
      // console.log('✅ Agent successfully updated in database');

      // *** PHASE 4: Content Processing ***
      // console.log('🔄 Phase 4: Processing new content...');
      
      // Use the variables from Phase 1: needsVectorSetup, hasWebsite, hasFiles, hasNewFiles
      if (needsVectorSetup) {
        try {
          // Handle website scraping if URL exists
          if (hasWebsite) {
            const selectedUrls: string[] = [];
            if (cleanedLinks && Array.isArray(cleanedLinks)) {
              cleanedLinks.forEach((website) => {
                if (website.links && Array.isArray(website.links)) {
                  website.links.forEach((link: { status: string; url: string; }) => {
                    if (link.status === 'selected') {
                      selectedUrls.push(link.url);
                    }
                  });
                }
              });
            }

            if (selectedUrls.length > 0) {
              // console.log('🕸️ Starting website scraping for updated agent:', selectedUrls);
              fetch('/api/fire-crawl/batch-scrape', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  url: validatedData.websiteUrl,
                  agentId: agentId,
                  urls: selectedUrls,
                  limit: 100,
                }),
              });
            }
          }

          // Handle file embeddings if files exist or have been updated
          if (hasFiles || hasNewFiles) {
            // console.log('📁 Starting file embeddings for updated agent:', agentId);
            fetch('/api/agents/initiate-embeddings', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                agentId: agentId,
              }),
            });
          }

          // Trigger agent profile regeneration for significant updates
          if (isActiveAgent) {
            // console.log('🔄 Triggering agent profile regeneration for active agent');
            fetch(`/api/agents/profile?agentId=${agentId}&forceGenerate=true`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
            });
          }
        } catch (error) {
          console.error('Error in post-update processing:', error);
        }
      }
      
      // *** PHASE 5: Clean up embeddings for deleted files ***
      try {
        const currentFileUrls = processedFiles.map(f => f.url);
        const deletedFiles = originalFiles.filter(
          originalFile => !currentFileUrls.includes(originalFile.url)
        );

        if (deletedFiles.length > 0) {
          // console.log('🧹 Cleaning up embeddings for deleted files:', deletedFiles.length);
          const deleteResponse = await fetch('/api/agents/delete-embeddings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agentId: agentId,
            }),
          });

          if (deleteResponse.ok) {
            const deleteResult = await deleteResponse.json();
            if (deleteResult.successfulDeletions > 0) {
              // console.log(`✅ Successfully cleaned up embeddings for ${deleteResult.successfulDeletions} deleted files`);
            }
          } else {
            console.error('Failed to clean up embeddings for deleted files');
          }
        }
      } catch (error) {
        console.error('Error cleaning up embeddings for deleted files:', error);
        // Don't block the success flow for embedding cleanup failures
      }
      
      // console.log('✅ All phases completed successfully');

      // Success toast and navigation
      toast({
        title: isActiveAgent ? 'Agent updated successfully' : 'Agent launched successfully',
        description: isActiveAgent 
          ? 'Your agent has been updated.'
          : 'Your agent is now live and ready to use.',
      });

      // Navigation based on original agent status
      if (isActiveAgent) {
        // For active agents, redirect to dashboard
        router.push('/dashboard');
      } else {
        // For newly launched agents, redirect to success page
        localStorage.setItem(
          'createdAgentData',
          JSON.stringify({
            id: responseData.agent.id,
            name: responseData.agent.agentName,
            slug: responseData.agent.slug,
          }),
        );
        router.push(`/create/success?agent=${responseData.agent.slug}`);
      }

    } catch (error) {
      console.error('Error updating agent:', error);
      toast({
        title: 'Error updating agent',
        description:
          error instanceof Error ? error.message : 'Failed to update agent',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load plan limits on component mount (for draft agents)
  useEffect(() => {
    if (session?.user?.email) {
      checkPlanLimits();
    }
  }, [session?.user?.email, checkPlanLimits]);

  // Check for trial success parameter in URL (only relevant for draft agents)
  useEffect(() => {
    if (isActiveAgent) return; // Skip trial handling for active agents
    
    const params = new URLSearchParams(window.location.search);
    const trialStatus = params.get('trial');

    if (trialStatus === 'success') {
      toast({
        title: 'Free trial activated!',
        description: 'Your 2-week free trial of the Hobby Plan has been activated. You can now launch your agent.',
      });

      // Remove the trial parameter from URL
      const newUrl = window.location.pathname + (agentId ? `?id=${agentId}` : '');
      window.history.replaceState({}, document.title, newUrl);

      // Refresh plan limits
      checkPlanLimits();
    } else if (trialStatus === 'canceled') {
      toast({
        title: 'Trial canceled',
        description: "Trial signup was canceled. You can try again when you're ready.",
        variant: 'destructive',
      });

      // Remove the trial parameter from URL
      const newUrl = window.location.pathname + (agentId ? `?id=${agentId}` : '');
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [checkPlanLimits, isActiveAgent, agentId, toast]);

  // Handle slug change - disabled for edit mode
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'slug' && value.slug) {
        checkSlugAvailability(value.slug as string);
      }

      // DISABLED: Auto-generate slug from agent name in edit mode
      // We preserve existing slugs to maintain agent URLs
      // Only enable slug validation, not auto-generation
    });

    return () => subscription.unsubscribe();
  }, [form, checkSlugAvailability]);

  // Handler for upgrading to Pro plan (only for draft agents)
  const handleUpgradeToPro = async () => {
    if (isActiveAgent) return; // No upgrade needed for active agents
    router.push('/billing');
  };

  // Handler for upgrading to Enterprise (only for draft agents)
  const handleUpgradeToEnterprise = () => {
    if (isActiveAgent) return; // No upgrade needed for active agents
    window.open('https://buildthatidea.typeform.com/to/dX0HyMyI', '_blank');
  };

  return (
    <div className="bg-background min-h-screen flex items-center justify-center">
      <div className="w-full max-w-7xl px-4 sm:px-5 lg:px-6">
        {pageState === 'loading' ? (
          <EditPageLoading />
        ) : pageState === 'error' ? (
          <div className="flex flex-col items-center justify-center min-h-[calc(100vh-80px)]">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">
                Unable to load agent data
              </h2>
              <p className="text-muted-foreground mb-4">
              {!agentId 
              ? 'No agent ID provided for editing'
              : 'Please check if the agent exists or try again later.'
              }
              </p>
              <div className="flex gap-2">
              <Button
                  onClick={() => router.push('/dashboard/agents')}
              >
                  Back to Agents
                    </Button>
                    {retryCount < maxRetries && agentId && (
                      <Button
                        variant="outline"
                        onClick={retryFetchAgentData}
                      >
                        Retry ({retryCount}/{maxRetries})
                      </Button>
                    )}
                  </div>
            </div>
          </div>
        ) : status !== 'authenticated' ? (
          <div className="flex flex-col items-center justify-center min-h-[calc(100vh-80px)]">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">
                Please sign in to edit agents
              </h2>
              <p className="text-muted-foreground mb-4">
                You need to be signed in to edit AI agents
              </p>
              <Button onClick={() => router.push('/verify')}>
                Sign In
              </Button>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form>              
              <div className="bg-card dark:bg-card lg:rounded-lg shadow-sm border-0 lg:border border-border overflow-hidden min-h-screen lg:min-h-[calc(100vh-3rem)] lg:h-[calc(100vh-3rem)]">
                <MultiStepForm
                  steps={formSteps}
                  onComplete={handleSubmit}
                  onSaveStep={undefined} // No draft saving in edit mode
                  isSubmitting={isSubmitting}
                  isSavingDraft={false} // Never saving drafts in edit mode
                  submitButtonText={getSubmitButtonText()}
                  showBackToLogin={true}
                  logoPreview={logoPreview}
                  draftAgentId={draftAgentId}
                  isEditMode={true}
                  isUploadingLogo={isUploadingLogoState}
                  isCheckingSlug={isCheckingSlug}
                  isUploadingFiles={isUploadingFiles}
                  isImprovingInstruction={isImprovingInstruction}
                  planLimits={planLimits}
                  onStartFreeTrial={isActiveAgent ? undefined : initiateFreeTrial}
                  onUpgradeToEnterprise={isActiveAgent ? undefined : handleUpgradeToEnterprise}
                  onUpgradeToPro={isActiveAgent ? undefined : handleUpgradeToPro}
                  onImprovingInstructionChange={setIsImprovingInstruction}
                  onUploadingLogoChange={setIsUploadingLogoState}
                  quickMessages={form.watch('quickMessages') || []}
                  setQuickMessages={(messages) => form.setValue('quickMessages', messages)}
                >
                  <BasicInfoStep
                    form={form}
                    isCheckingSlug={isCheckingSlug}
                    slugExists={slugExists}
                    isOwnDraft={isOwnDraft}
                    logoPreview={logoPreview}
                    setLogoPreview={setLogoPreview}
                    setLogoFile={setLogoFile}
                    setLogoUrl={setLogoUrl}
                    isEditMode={true}
                  />
                  <CapabilitiesStep
                    form={form}
                    draftAgentId={draftAgentId}
                    setIsUploadingFiles={setIsUploadingFiles}
                    planLimits={planLimits}
                    isEditMode={true}
                  />
                  <ConfigurationStep
                    form={form}
                  />
                  <MonetizationStep
                    form={form}
                    isEditMode={true}
                    isActiveAgent={isActiveAgent}
                  />
                </MultiStepForm>
              </div>
            </form>
          </Form>
        )}
      </div>
    </div>
  );
}
