'use client';

import type { Attachment, UIMessage } from 'ai';
import cx from 'classnames';
import type React from 'react';
import {
  useRef,
  useEffect,
  useState,
  useCallback,
  type Dispatch,
  type SetStateAction,
  type ChangeEvent,
  memo,
  useMemo,
} from 'react';
import { toast } from 'sonner';
import { useWindowSize } from 'usehooks-ts';
import { useSession } from 'next-auth/react';

import { ArrowUpIcon, StopIcon, PaperclipIcon } from './icons';
import { ChevronDown } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { PreviewAttachment } from './preview-attachment';
import { SuggestedActions } from './suggested-actions';

import equal from 'fast-deep-equal';
import type { UseChatHelpers } from '@ai-sdk/react';
import type { Agent } from '@/lib/db/schema';
import { useAuth } from './auth-context';
import { useSubscription } from '@/context/subscription-context';
import { useScrollToBottom } from '@/hooks/use-scroll-to-bottom';

// Maximum file size for uploads (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

function PureMultimodalInput({
  chatId,
  input,
  setInput,
  status,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  append,
  handleSubmit,
  className,
  agent,
  remainingMessages,
  disabled,
  isLoading = false,
  onUpgrade,
}: {
  chatId: string;
  input: UseChatHelpers['input'];
  setInput: UseChatHelpers['setInput'];
  status: UseChatHelpers['status'];
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  append: UseChatHelpers['append'];
  handleSubmit: UseChatHelpers['handleSubmit'];
  className?: string;
  agent?: Agent;
  remainingMessages?: number;
  disabled?: boolean;
  isLoading?: boolean;
  onUpgrade?: () => void;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { width } = useWindowSize();
  const { data: session } = useSession();
  const { showAuthModal, refreshSession } = useAuth();
  const { isProForAgent } = useSubscription();

  // Use the scroll to bottom hook
  const { isAtBottom, scrollToBottom } = useScrollToBottom();

  // Track client-side rendering to avoid hydration errors
  const [isClient, setIsClient] = useState(false);

  // Calculate word count
  const wordCount = useMemo(() => {
    return input.trim() === '' ? 0 : input.trim().split(/\s+/).length;
  }, [input]);

  // Initialize state without useLocalStorage to avoid hydration issues
  const [localStorageInput, setLocalStorageInput] = useState('');
  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Initialize localStorage after component mounts
  useEffect(() => {
    // Only access localStorage in the browser
    if (typeof window !== 'undefined') {
      const savedInput = localStorage.getItem('input') || '';
      setLocalStorageInput(savedInput);
    }
  }, []);

  // Update localStorage when input changes
  useEffect(() => {
    if (typeof window !== 'undefined' && localStorageInput !== '') {
      localStorage.setItem('input', localStorageInput);
    }
  }, [localStorageInput]);

  useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, []);

  // Scroll to bottom when status changes (e.g., when a new message is received)
  useEffect(() => {
    scrollToBottom();
  }, [status, scrollToBottom]);

  const adjustHeight = () => {
    if (textareaRef.current) {
      // Allow dynamic height adjustment while typing
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
    }
  };

  const resetHeight = () => {
    if (textareaRef.current) {
      // Reset to fixed height after sending
      textareaRef.current.style.height = '70px';
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value;
      // Prefer DOM value over localStorage to handle hydration
      const finalValue = domValue || localStorageInput || '';
      setInput(finalValue);
      adjustHeight();
    }
    // Only run once after hydration
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const countWords = (text: string): number => {
    // Remove leading/trailing whitespace and split by whitespace
    return text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
  };

  // Function to check if text exceeds character limit for the last word
  const isLastWordTooLong = (text: string, wordLimit: number): boolean => {
    const words = text.trim().split(/\s+/);
    if (words.length !== wordLimit) return false;

    // Check if the last word is being extended beyond reasonable length
    // Using 30 characters as a reasonable maximum for a single word
    return words[wordLimit - 1].length > 15;
  };

  // Function to truncate text to a specific word count while preserving formatting
  const truncateToWordLimit = (text: string, limit: number): string => {
    if (countWords(text) <= limit) return text;
  
    // Find the position where the word limit is reached
    let wordCount = 0;
    let pos = 0;
  
    // Use regex to match words and whitespace
    const wordRegex = /\S+/g;
    let match: RegExpExecArray | null;
  
    // Find each word and count until we reach the limit
    while (true) {
      match = wordRegex.exec(text);
      if (match === null) break;
      
      wordCount++;
      if (wordCount > limit) {
        pos = match.index;
        break;
      }
    }
  
    // If we found the position, truncate at that point
    return pos > 0 ? text.substring(0, pos) : text;
  };

  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = event.target.value;
    const wordCount = countWords(newText);

    // Check if user is trying to extend the last word beyond reasonable length
    if (
      wordCount === 800 &&
      isLastWordTooLong(newText, 800) &&
      newText.length > input.length
    ) {
      toast.error('You have reached the 800 word limit');
      return;
    }

    // If text is under word limit or user is deleting text
    if (wordCount <= 800 || newText.length < input.length) {
      setInput(newText);
      adjustHeight();
    } else {
      // If pasting text that exceeds the limit, truncate to 800 words
      const truncatedText = truncateToWordLimit(newText, 800);
      setInput(truncatedText);
      adjustHeight();
      toast.info('Text has been truncated to 800 words');
    }
  };

  const uploadFile = async (file: File) => {
    // Client-side validation for file size
    if (file.size > MAX_FILE_SIZE) {
      toast.error(
        `File size exceeds the ${MAX_FILE_SIZE / 1024 / 1024}MB limit!`,
      );
      return undefined;
    }

    // Client-side validation for file type - only allow JPEG and PNG
    const allowedTypes = ['image/jpeg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Only JPEG and PNG images are allowed!');
      return undefined;
    }

    try {
      // Step 1: Request a pre-signed URL from our API
      const presignedUrlResponse = await fetch('/api/files/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      if (!presignedUrlResponse.ok) {
        const errorData = await presignedUrlResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      // Step 2: Get the pre-signed URL and file information
      const { presignedUrl, fileUrl, key } = await presignedUrlResponse.json();

      // Step 3: Upload the file directly to S3 using the pre-signed URL
      const uploadResponse = await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload to S3: ${uploadResponse.statusText}`);
      }

      // Step 4: Return the file information as properly typed Attachment
      // For PDFs, ensure we specify the correct content type for AI processing
      if (file.type === 'application/pdf') {
        return {
          url: fileUrl,
          name: key.split('/').pop() || file.name,
          contentType: 'application/pdf',
          // Add file path and metadata to help AI recognize this as a document
          file_path: key,
          mime_type: 'application/pdf',
        } as Attachment;
      } else {
        // For images and other types
        return {
          url: fileUrl,
          name: key.split('/').pop() || file.name,
          contentType: file.type,
        } as Attachment;
      }
    } catch (error: any) {
      console.error('File upload error:', error);
      toast.error(error.message || 'Failed to upload file, please try again!');
      return null;
    }
  };

  const handleFileChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);

      setUploadQueue(files.map((file) => file.name));

      try {
        const uploadPromises = files.map((file) => uploadFile(file));
        const uploadedAttachments = await Promise.all(uploadPromises);

        // Filter out null and undefined values
        const successfullyUploadedAttachments = uploadedAttachments.filter(
          (attachment): attachment is Attachment =>
            attachment !== undefined && attachment !== null,
        );

        setAttachments((currentAttachments) => [
          ...currentAttachments,
          ...successfullyUploadedAttachments,
        ]);
      } catch (error) {
        console.error('Error uploading files!', error);
      } finally {
        setUploadQueue([]);
      }
    },
    [setAttachments],
  );

  const submitForm = useCallback(() => {
    // Check if user is authenticated - use a more reliable check
    const isAuthenticated = !!session?.user;

    if (!isAuthenticated) {
      if (showAuthModal) {
        showAuthModal(() => {
          refreshSession();
          submitForm();
        });
        return;
      } else {
        toast.error('Please sign in to send messages!');
        return;
      }
    }

    // If we're in /chat/[id], don't change the URL
    if (!chatId.startsWith('chat_')) {
      // If we're in /agent/[slug], redirect to /agent/[slug]/[id]
      if (agent?.slug) {
        const url = `/agent/${agent.slug}/${chatId}`;
        window.history.pushState({}, '', url);
      }
    } else {
      // For regular chats, update the URL
      window.history.replaceState({}, '', `/chat/${chatId}`);
    }

    // Submit the form with attachments
    handleSubmit(undefined, {
      experimental_attachments: attachments,
    });

    // Reset state after submission
    setAttachments([]);
    setLocalStorageInput('');
    resetHeight();

    // Focus the textarea on desktop
    if (width && width > 768) {
      textareaRef.current?.focus();
    }
  }, [
    session,
    attachments,
    handleSubmit,
    setAttachments,
    setLocalStorageInput,
    width,
    chatId,
    showAuthModal,
    refreshSession,
    agent?.slug,
  ]);

  return (
    <div className="relative w-full flex flex-col gap-4">
      <AnimatePresence>
        {!isAtBottom && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            className="absolute left-1/2 bottom-28 -translate-x-1/2 z-50"
          >
            <Button
              data-testid="scroll-to-bottom-button"
              className="rounded-full"
              size="icon"
              variant="outline"
              onClick={(event) => {
                event.preventDefault();
                scrollToBottom();
              }}
            >
              <ChevronDown className="size-4" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Only render file input on client-side to avoid hydration errors */}
      {isClient && (
        <input
          type="file"
          className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
          ref={fileInputRef}
          multiple
          accept="image/jpeg,image/png"
          onChange={handleFileChange}
          tabIndex={-1}
        />
      )}

      {/* Only render attachment previews on client-side */}
      {isClient && attachments.length > 0 && (
        <div
          data-testid="attachments-preview"
          className="flex flex-row gap-2 overflow-x-scroll items-end mb-4 pt-3 px-3"
        >
          {attachments.map((attachment) => (
            <PreviewAttachment
              key={attachment.url}
              attachment={attachment}
              onRemove={() => {
                setAttachments((current) =>
                  current.filter((a) => a.url !== attachment.url),
                );
              }}
            />
          ))}
        </div>
      )}

      {/* Only render upload queue on client-side */}
      {isClient && uploadQueue.length > 0 && (
        <div
          data-testid="upload-queue-preview"
          className="flex flex-row gap-2 overflow-x-scroll items-end mb-4"
        >
          {uploadQueue.map((filename) => (
            <PreviewAttachment
              key={filename}
              attachment={{
                url: '',
                name: filename,
                contentType: '',
              }}
              isUploading={true}
            />
          ))}
        </div>
      )}

      <div className="relative">
        {messages.length === 0 && (
          <SuggestedActions
            chatId={chatId}
            agent={agent}
            append={append}
            setInput={setInput}
            disabled={disabled || isLoading || status !== 'ready'}
          />
        )}

        <div className="relative">
          {/* Main text input area with reduced bottom padding */}
          <Textarea
            data-testid="multimodal-input"
            ref={textareaRef}
            placeholder="Send a message..."
            value={input}
            onChange={handleInput}
            name="message"
            disabled={
              disabled ||
              (isLoading && !!session) ||
              (agent?.accessLevel !== 'free' &&
                remainingMessages === 0 &&
                !!session)
            }
            className={cx(
              'min-h-[24px] h-[70px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-none rounded-t-2xl !text-base bg-muted pb-2 dark:border-zinc-700 border-b-0 focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none',
              className,
            )}
            rows={2}
            autoFocus
            onKeyDown={(event) => {
              if (
                event.key === 'Enter' &&
                !event.shiftKey &&
                !event.nativeEvent.isComposing
              ) {
                event.preventDefault();

                if (status !== 'ready') {
                  toast.error(
                    'Please wait for the model to finish its response!',
                  );
                } else {
                  submitForm();
                }
              }
            }}
          />

          {/* Integrated toolbar - height adjusted to match original total height */}
          <div className="flex items-center justify-between px-3 py-1 h-[34px] bg-muted dark:bg-zinc-800 border border-t-0 dark:border-zinc-700 rounded-b-2xl">
            {/* Left side of toolbar - attachment button */}
            <div className="flex items-center">
              {isClient && (
                <Button
                  data-testid="attachments-button"
                  className="rounded-md p-[7px] h-fit dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200"
                  onClick={(event) => {
                    event.preventDefault();
                    fileInputRef.current?.click();
                  }}
                  disabled={status !== 'ready' || disabled}
                  variant="ghost"
                >
                  <PaperclipIcon size={14} />
                </Button>
              )}
            </div>

            {/* Right side of toolbar - word count and send button */}
            <div className="flex items-center gap-3">
              {/* Word count indicator */}
              <div className="text-xs text-muted-foreground">
                <span
                  className={wordCount >= 800 ? 'text-red-500 font-medium' : ''}
                >
                  {wordCount}/800 words
                </span>
              </div>

              {/* Send/Stop button */}
              <div>
                {status === 'submitted' ? (
                  <StopButton stop={stop} setMessages={setMessages} />
                ) : (
                  <SendButton
                    input={input}
                    submitForm={submitForm}
                    uploadQueue={uploadQueue}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function PureStopButton({
  stop,
  setMessages,
}: {
  stop: () => void;
  setMessages: UseChatHelpers['setMessages'];
}) {
  return (
    <Button
      data-testid="stop-button"
      className="rounded-full p-1 sm:p-1.5 h-fit border dark:border-zinc-600"
      onClick={(event) => {
        event.preventDefault();
        stop();
        setMessages((messages) => messages);
      }}
    >
      <StopIcon size={12} />
    </Button>
  );
}

const StopButton = memo(PureStopButton);

function PureSendButton({
  submitForm,
  input,
  uploadQueue,
}: {
  submitForm: () => void;
  input: string;
  uploadQueue: Array<string>;
}) {
  return (
    <Button
      data-testid="send-button"
      className="rounded-full p-1 sm:p-1.5 h-fit border dark:border-zinc-600"
      onClick={(event) => {
        event.preventDefault();
        submitForm();
      }}
      disabled={input.length === 0 || uploadQueue.length > 0}
    >
      <ArrowUpIcon size={12} />
    </Button>
  );
}

const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
  if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length)
    return false;
  if (prevProps.input !== nextProps.input) return false;
  return true;
});

export const MultimodalInput = memo(
  PureMultimodalInput,
  (prevProps, nextProps) => {
    if (prevProps.input !== nextProps.input) return false;
    if (prevProps.status !== nextProps.status) return false;
    if (!equal(prevProps.attachments, nextProps.attachments)) return false;
    if (prevProps.agent?.id !== nextProps.agent?.id) return false;
    if (prevProps.remainingMessages !== nextProps.remainingMessages)
      return false;

    return true;
  },
);
