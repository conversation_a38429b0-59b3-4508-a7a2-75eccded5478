import React from 'react';
import { Badge } from './ui/badge';
import { SparklesIcon } from 'lucide-react';

interface ProBadgeProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ProBadge({ size = 'md', className = '' }: ProBadgeProps) {
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-sm px-2 py-0.5',
    lg: 'text-base px-2.5 py-1',
  };

  const iconSizes = {
    sm: 'h-2.5 w-2.5',
    md: 'h-3 w-3',
    lg: 'h-4 w-4',
  };

  return (
    <Badge 
      variant="outline" 
      className={`
        bg-gradient-to-r from-amber-200 to-amber-300 
        dark:from-amber-700/40 dark:to-amber-500/40 
        text-amber-800 dark:text-amber-300 
        border-amber-300 dark:border-amber-700/60
        font-medium flex items-center gap-1
        ${sizeClasses[size]} ${className}
      `}
    >
      <SparklesIcon className={iconSizes[size]} />
      <span>PRO</span>
    </Badge>
  );
}
