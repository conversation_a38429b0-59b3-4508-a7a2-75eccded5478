import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, <PERSON>rk<PERSON> } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface InstructionImproverProps {
  agentName: string;
  instruction: string;
  onImprovedInstruction: (improvedInstruction: string) => void;
  model?: string;
}

export function InstructionImprover({
  agentName,
  instruction,
  onImprovedInstruction,
  model = "chat-model", // Default to chat-model if not specified
}: InstructionImproverProps) {
  const [isImproving, setIsImproving] = useState(false);
  const [improvedText, setImprovedText] = useState("");
  const { toast } = useToast();

  const improveInstructions = async () => {
    if (!instruction || instruction.trim().length < 10) {
      toast({
        title: "Instructions too short",
        description: "Please provide more detailed instructions to improve.",
        variant: "destructive",
      });
      return;
    }

    setIsImproving(true);
    setImprovedText("");

    try {
      const response = await fetch("/api/preview/instructions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          selectedChatModel: model,
          agentName,
          agentInstructions: instruction,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to improve instructions");
      }

      if (!response.body) {
        throw new Error("Response body is null");
      }

      // Process the streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let improvedInstruction = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        improvedInstruction += chunk;
        setImprovedText(improvedInstruction);
      }

      // Update the parent component with the improved instruction
      onImprovedInstruction(improvedInstruction);
    } catch (error) {
      console.error("Error improving instructions:", error);
      toast({
        title: "Error",
        description: "Failed to improve instructions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsImproving(false);
    }
  };

  return (
    <div className="mt-2">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={improveInstructions}
        disabled={isImproving || !instruction || instruction.trim().length < 10}
        className="flex items-center gap-2 text-xs"
      >
        {isImproving ? (
          <>
            <Loader2 className="h-3 w-3 animate-spin" />
            Improving...
          </>
        ) : (
          <>
            <Sparkles className="h-3 w-3" />
            Enhance Instructions with AI
          </>
        )}
      </Button>
      {improvedText && (
        <div className="mt-2 text-xs text-muted-foreground">
          <p>AI has enhanced your instructions. You can edit them further if needed.</p>
        </div>
      )}
    </div>
  );
}
