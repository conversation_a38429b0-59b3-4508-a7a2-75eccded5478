import { tool } from 'ai';
import { z } from 'zod';
import { retrieveRelevantContent } from '@/lib/embeddings/retriever';

/**
 * Tool for retrieving relevant content from the agent's knowledge base
 * This tool MUST be called for ALL user questions before responding
 */
export const getKnowledge = tool({
  description:
    "REQUIRED: You MUST call this tool for EVERY question before responding. This tool retrieves relevant information from the agent's knowledge base that you should use in your answer. After calling this tool and getting the information, respond to the user's question directly without revealing that you used this tool. Simply incorporate the information as if you already knew it. Do not use phrases like 'Based on information' or add any introductory text. Just answer the question directly.",
  parameters: z.object({
    query: z.string().describe('The search query to find relevant information'),
    agentId: z
      .string()
      .describe('The ID of the agent whose knowledge base to search'),
    limit: z
      .number()
      .optional()
      .describe('Maximum number of results to return (default: 5)'),
  }),
  execute: async ({ query, agentId, limit = 5 }) => {
    const startTime = Date.now();
    try {
      // Log when the tool is being used
      console.log('=== KNOWLEDGE TOOL CALLED ===');
      console.log(`Query: "${query}"`);
      console.log(`Agent ID: ${agentId}`);
      console.log(`Requested limit: ${limit}`);
      console.log('=============================');

      // Use the retrieveRelevantContent function to get relevant content
      const relevantContent = await retrieveRelevantContent(
        query,
        agentId,
        limit,
        0.5, // Default similarity threshold
      );

      // Log the retrieved data
      console.log('=== KNOWLEDGE RETRIEVED ===');
      console.log(`Found ${relevantContent.length} relevant chunks`);
      relevantContent.forEach((content, index) => {
        console.log(`Result ${index + 1}:`);
        // Log a preview of the content (first 100 characters)
        console.log(
          `${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`,
        );
      });
      console.log('===========================');

      // Return the results in a minimal format that's less disruptive if shown in the UI
      if (relevantContent.length === 0) {
        return 'I don\'t have specific information about that. I\'ll answer based on my general knowledge.';
      }

      // Add a reminder to each content chunk to not mention the knowledge base
      const processedContent = relevantContent.map(content => {
        return `[CRITICAL INSTRUCTION: USE THIS INFORMATION IN YOUR ANSWER, but NEVER mention that it came from a knowledge base or that you used a tool. NEVER say "After calling the getKnowledge tool" or anything about retrieving information. NEVER mention the agentId. Answer DIRECTLY as if you already knew this information without any preamble.] ${content}`;
      });

      // Benchmark: Log the time taken for the tool execution
      const endTime = Date.now();
      console.log(`KNOWLEDGE TOOL EXECUTION TIME: ${endTime - startTime}ms`);

      // Return the processed content
      return processedContent;
    } catch (error) {
      // Log detailed error information
      console.error('=== KNOWLEDGE TOOL ERROR ===');
      console.error(`Query: "${query}"`);
      console.error(`Agent ID: ${agentId}`);
      console.error('Error details:', error);
      console.error('============================');

      // Return a simple error message that doesn't reveal the knowledge base
      return `I don't have specific information about that. I'll answer based on my general knowledge.`;
    }
  },
});
