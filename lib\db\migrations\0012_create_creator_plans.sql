CREATE TABLE IF NOT EXISTS "CreatorPlans" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "name" varchar(64) NOT NULL,
  "monthly_price" integer NOT NULL,
  "yearly_price" integer NOT NULL,
  "tokens_allowed" integer NOT NULL,
  "knowledge_base_size" varchar(10) NOT NULL,
  "agents_allowed" integer NOT NULL,
  "top_up_tokens" integer,
  "top_up_knowledge_base" varchar(10),
  "top_up_token_price" integer,
  "top_up_knowledge_base_price" integer,
  "embeddable_widget" boolean,
  "monetization_included" boolean,
  "crypto_and_fiat_payments" boolean,
  "analytics" varchar CHECK ("analytics" IN ('Basic', 'Advanced')),
  "support_type" varchar CHECK ("support_type" IN ('Email', 'Priority', 'Dedicated')),
  "other_features" json NOT NULL,
  "created_at" timestamp DEFAULT now(),
  "updated_at" timestamp DEFAULT now()
);
