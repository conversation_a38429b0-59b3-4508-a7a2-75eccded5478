import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  console.error('❌ POSTGRES_URL is not defined in environment variables');
  process.exit(1);
}

// Create database client after environment variables are loaded
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

async function runAgentProfilesMigration() {
  try {
    console.log('⏳ Starting migration: Creating agent_profiles table');
    // biome-ignore lint: Forbidden non-null assertion.
    console.log(`Using database URL: ${process.env.POSTGRES_URL!.split('@')[1]}`); // Log only the host part for security
    
    // Create agent_profiles table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "agent_profiles" (
        "id" UUID PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "agent_id" UUID NOT NULL REFERENCES "agents"("id") ON DELETE CASCADE,
        "rating" REAL DEFAULT 4.5,
        "total_chats" INTEGER DEFAULT 0,
        "datasets" INTEGER DEFAULT 0,
        "user_ratings" INTEGER DEFAULT 0,
        "features" JSONB,
        "created_at" TIMESTAMP DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now()
      )
    `);
    console.log('✅ Created agent_profiles table');

    // Create index for faster lookups by agent_id
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "agent_profiles_agent_id_idx" ON "agent_profiles" ("agent_id")
    `);
    console.log('✅ Created index on agent_id column');

    console.log('🎉 Migration completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
    process.exit(0);
  }
}

// Run the migration function
runAgentProfilesMigration().then(() => {
  console.log('✅ Agent profiles migration process completed');
});
