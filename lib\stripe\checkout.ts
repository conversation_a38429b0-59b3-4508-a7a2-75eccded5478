import Stripe from 'stripe';
import { getOrCreateAgentProduct } from './product';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-03-31.basil',
});

export interface CreateCheckoutSessionParams {
  agentId: string;
  agentName: string;
  description?: string;
  userId: string;
  agentSlug: string;
  priceAmount?: number;
  currency?: string;
  interval?: 'month' | 'year';
}

export async function createAgentCheckoutSession(params: CreateCheckoutSessionParams) {
  try {
    // First, create or get the product and price
    const { priceId } = await getOrCreateAgentProduct({
      agentId: params.agentId,
      agentName: params.agentName,
      description: params.description || `Subscription for ${params.agentName}`,
      priceAmount: params.priceAmount || 999, // Default to $9.99 in cents
      interval: params.interval || 'month',
      currency: params.currency || 'usd',
    });

    // Create the checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/create/success?agent=${params.agentSlug}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/create`,
      metadata: {
        agentId: params.agentId,
        userId: params.userId,
      },
    });

    return {
      sessionId: session.id,
      sessionUrl: session.url,
      priceId,
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
}
