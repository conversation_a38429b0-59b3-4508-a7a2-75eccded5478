# Payment System Overview

## Overview

The platform implements a comprehensive payment system that enables monetization for both the platform and agent creators. The payment system handles subscriptions, one-time payments, creator payouts, and financial reporting. This document details the complete payment infrastructure and workflows.

## Payment Models

The platform supports multiple payment models:

1. **Free Agents**: Available to all users without payment
2. **Subscription Agents**: Requiring a recurring monthly or yearly subscription
3. **Lifetime Access Agents**: Requiring a one-time payment for permanent access

## Technology Stack

- **Stripe**: Primary payment processor for all transactions
- **PostgreSQL**: Database for storing payment and subscription records
- **Drizzle ORM**: ORM for database interactions
- **Next.js API Routes**: Backend endpoints for payment processing
- **React**: Frontend components for payment UI

## Database Schema

The payment system uses several database tables:

### UserSubscriptions

Tracks user subscriptions to paid agents:

```typescript
export const userSubscriptions = pgTable('UserSubscriptions', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  agentId: uuid('agentId').notNull().references(() => agents.id),
  userId: uuid('userId').notNull().references(() => user.id),
  status: statusEnum('status').notNull().default('active'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  expiresAt: timestamp('expiresAt'),
  cancelAt: timestamp('cancelAt'),
  canceledAt: timestamp('canceledAt'),
  currentPeriodStart: timestamp('currentPeriodStart'),
  currentPeriodEnd: timestamp('currentPeriodEnd'),
  stripeSubscriptionId: text('stripeSubscriptionId'),
  stripePriceId: text('stripePriceId'),
  stripeCustomerId: text('stripeCustomerId'),
  subscriptionType: subscriptionTypeEnum('subscriptionType').default('monthly'),
});
```

### CreatorSubscriptions

Tracks creator subscriptions to the platform:

```typescript
export const creatorSubscriptions = pgTable('CreatorSubscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').notNull().references(() => user.id, { onDelete: 'cascade' }),
  planId: uuid('planId').notNull().references(() => creatorPlans.id),
  status: subscriptionStatusEnum('status').notNull().default('active'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  expiresAt: timestamp('expiresAt'),
  stripeSubscriptionId: text('stripeSubscriptionId'),
  stripePriceId: text('stripePriceId'),
  stripeCustomerId: text('stripeCustomerId'),
  subscriptionType: subscriptionTypeEnum('subscriptionType').default('monthly'),
});
```

### Payment Records

Tables for tracking payments:

```typescript
export const creatorPayments = pgTable('creator_payments', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').notNull().references(() => creatorSubscriptions.id, { onDelete: 'cascade' }),
  amount: integer('amount').notNull(),
  status: text('status').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  paidAt: timestamp('paid_at'),
});

export const userPayments = pgTable('user_payments', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').notNull().references(() => userSubscriptions.id, { onDelete: 'cascade' }),
  amount: integer('amount').notNull(),
  status: text('status').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  paidAt: timestamp('paid_at'),
});
```

### Stripe Integration Tables

Tables for Stripe integration:

```typescript
export const stripeEvents = pgTable('stripe_events', {
  id: text('id').primaryKey().notNull(),
  type: text('type').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  data: jsonb('data'),
});

export const stripeCustomers = pgTable('stripe_customers', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  stripeCustomerId: text('stripe_customer_id').notNull().unique(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

## Payment Flows

### 1. User Subscription Flow

When a user subscribes to a paid agent:

1. **Checkout Initiation**: User clicks "Subscribe" on an agent page
2. **Stripe Checkout**: User is redirected to Stripe Checkout
3. **Payment Processing**: User completes payment on Stripe
4. **Webhook Notification**: Stripe sends webhook notification to the platform
5. **Subscription Creation**: Platform creates subscription record
6. **Access Granting**: User gains access to the agent

### 2. Lifetime Access Flow

When a user purchases lifetime access to an agent:

1. **Checkout Initiation**: User clicks "Buy Lifetime Access" on an agent page
2. **Stripe Checkout**: User is redirected to Stripe Checkout
3. **Payment Processing**: User completes one-time payment on Stripe
4. **Webhook Notification**: Stripe sends webhook notification to the platform
5. **Access Record Creation**: Platform creates lifetime access record
6. **Access Granting**: User gains permanent access to the agent

### 3. Creator Payout Flow

When a creator receives payment for their agents:

1. **Revenue Calculation**: System calculates creator revenue
2. **Payout Scheduling**: Payouts are scheduled according to platform policy
3. **Payout Processing**: Payouts are processed through the configured method
4. **Record Updating**: Payment records are updated with payout status
5. **Notification**: Creator receives notification of payout

## Stripe Integration

### Product and Price Creation

When a creator sets up a paid agent, the system creates Stripe products and prices:

```typescript
export async function getOrCreateAgentProduct({
  agentId,
  agentName,
  description,
  priceAmount,
  interval = 'month',
  currency = 'usd',
}: CreateAgentProductParams) {
  try {
    // Check if the product already exists
    const existingProduct = await stripe.products.search({
      query: `metadata['agentId']:'${agentId}'`,
    });

    if (existingProduct.data.length > 0) {
      // Product exists, get the associated price
      const product = existingProduct.data[0];
      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
      });

      // Return the first active price
      if (prices.data.length > 0) {
        return {
          productId: product.id,
          priceId: prices.data[0].id,
        };
      }

      // No active price found, create a new one
      const newPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: priceAmount,
        currency,
        recurring: interval === 'once' ? undefined : {
          interval: interval === 'year' ? 'year' : 'month',
        },
      });

      return {
        productId: product.id,
        priceId: newPrice.id,
      };
    }

    // Create a new product
    const product = await stripe.products.create({
      name: agentName,
      description: description || `Subscription for ${agentName}`,
      metadata: {
        agentId,
      },
    });

    // Create a price for the product
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: priceAmount,
      currency,
      recurring: interval === 'once' ? undefined : {
        interval: interval === 'year' ? 'year' : 'month',
      },
    });

    return {
      productId: product.id,
      priceId: price.id,
    };
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}
```

### Checkout Session Creation

The system creates Stripe checkout sessions for payments:

```typescript
export async function createAgentCheckoutSession(params: CreateCheckoutSessionParams) {
  try {
    // First, create or get the product and price
    const { priceId } = await getOrCreateAgentProduct({
      agentId: params.agentId,
      agentName: params.agentName,
      description: params.description || `Subscription for ${params.agentName}`,
      priceAmount: params.priceAmount || 999, // Default to $9.99 in cents
      interval: params.interval || 'month',
      currency: params.currency || 'usd',
    });

    // Create the checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/create/success?agent=${params.agentSlug}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/create`,
      metadata: {
        agentId: params.agentId,
        userId: params.userId,
      },
    });

    return {
      sessionId: session.id,
      sessionUrl: session.url,
      priceId,
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
}
```

### Webhook Handling

The system processes Stripe webhooks to update subscription status:

```typescript
// Simplified webhook handler
export async function POST(req: Request) {
  const body = await req.text();
  const signature = req.headers.get('stripe-signature') as string;
  
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
    
    // Store the event in the database
    await db.insert(stripeEvents).values({
      id: event.id,
      type: event.type,
      data: event.data.object,
    });
    
    // Handle different event types
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      // Handle other event types
    }
    
    return new Response(JSON.stringify({ received: true }));
  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(
      JSON.stringify({ error: 'Webhook signature verification failed' }),
      { status: 400 }
    );
  }
}
```

## Revenue Sharing Model

The platform implements a revenue sharing model between the platform and creators:

1. **Platform Fee**: Percentage of subscription revenue retained by the platform
2. **Creator Revenue**: Remaining percentage paid to the creator
3. **Payout Schedule**: Typically monthly, after a holding period
4. **Minimum Payout**: Minimum amount required for payout processing

## Subscription Management

Users and creators can manage their subscriptions through the platform:

1. **Subscription Viewing**: See active subscriptions and details
2. **Cancellation**: Cancel recurring subscriptions
3. **Upgrade/Downgrade**: Change subscription plans
4. **Payment Method Update**: Update payment methods
5. **Billing History**: View past payments and invoices

## Payout Management

Creators can manage their payouts through the platform:

1. **Payout Method Setup**: Configure payout methods (e.g., PayPal)
2. **Payout History**: View past payouts
3. **Revenue Analytics**: See revenue trends and projections
4. **Tax Information**: Manage tax information for payouts

## Security Considerations

The payment system includes several security measures:

1. **PCI Compliance**: Using Stripe for payment processing to maintain PCI compliance
2. **Data Encryption**: Encrypting sensitive payment data
3. **Access Control**: Restricting access to payment information
4. **Audit Logging**: Logging all payment-related actions
5. **Fraud Prevention**: Implementing fraud detection measures

## Error Handling

The payment system includes comprehensive error handling:

1. **Payment Failures**: Handling failed payments gracefully
2. **Webhook Failures**: Retrying failed webhook processing
3. **Subscription Errors**: Managing subscription state inconsistencies
4. **Payout Failures**: Handling failed payouts
5. **Notification**: Alerting users and administrators of payment issues

## Testing and Sandbox Environment

The payment system includes a testing environment:

1. **Stripe Test Mode**: Using Stripe test mode for development
2. **Test Cards**: Using Stripe test cards for payment testing
3. **Webhook Testing**: Testing webhook handling with Stripe CLI
4. **End-to-End Testing**: Testing complete payment flows

## Reporting and Analytics

The payment system includes reporting and analytics features:

1. **Revenue Reports**: Overall platform revenue
2. **Creator Reports**: Individual creator revenue
3. **Subscription Metrics**: Active subscriptions, churn rate, etc.
4. **Payment Success Rate**: Tracking payment success and failure rates
5. **Payout Reports**: Tracking payouts to creators
