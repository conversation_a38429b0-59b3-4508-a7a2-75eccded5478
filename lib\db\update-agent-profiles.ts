import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';
import { agents, agentProfiles, chat, message, agentKnowledgeFiles } from '../db/schema';
import { eq, count, sum } from 'drizzle-orm';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  console.error('❌ POSTGRES_URL is not defined in environment variables');
  process.exit(1);
}

// Create database client after environment variables are loaded
const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

export async function updateAgentProfiles() {
  try {
    console.log('⏳ Starting agent profiles update job');
    console.log(`Using database URL: ${process.env.POSTGRES_URL.split('@')[1]}`); // Log only the host part for security
    
    // Get all agents
    const allAgents = await db.select().from(agents);
    console.log(`Found ${allAgents.length} agents to process`);
    
    for (const agent of allAgents) {
      console.log(`Processing agent: ${agent.agentName} (${agent.id})`);
      
      // Count total chats for this agent (we'll keep this for reference)
      const chatResult = await db
        .select({ count: count() })
        .from(chat)
        .where(eq(chat.agentId, agent.id));
      const chatCount = chatResult[0]?.count || 0;
      
      // Count total messages for this agent (this is what we'll use for totalChats)
      const messageResult = await db
        .select({ count: count() })
        .from(message)
        .innerJoin(chat, eq(message.chatId, chat.id))
        .where(eq(chat.agentId, agent.id));
      const totalChats = messageResult[0]?.count || 0; // Using message count as totalChats
      
      // Count datasets (knowledge files) for this agent - using total file size
      const knowledgeFiles = await db
        .select({
          fileSize: agentKnowledgeFiles.fileSize
        })
        .from(agentKnowledgeFiles)
        .where(eq(agentKnowledgeFiles.agentId, agent.id));
      
      // Calculate total file size by parsing each file size string to a number
      let totalFileSize = 0;
      for (const file of knowledgeFiles) {
        // Parse the file size, handling various formats (e.g., "1024", "1.5 MB")
        try {
          const sizeStr = file.fileSize.toString();
          // Simple numeric string
          if (/^\d+$/.test(sizeStr)) {
            totalFileSize += parseInt(sizeStr, 10);
          } 
          // Size with MB, KB, etc.
          else if (sizeStr.includes('MB')) {
            const size = parseFloat(sizeStr.replace('MB', '').trim());
            totalFileSize += size * 1024 * 1024; // Convert MB to bytes
          }
          else if (sizeStr.includes('KB')) {
            const size = parseFloat(sizeStr.replace('KB', '').trim());
            totalFileSize += size * 1024; // Convert KB to bytes
          }
          else if (sizeStr.includes('GB')) {
            const size = parseFloat(sizeStr.replace('GB', '').trim());
            totalFileSize += size * 1024 * 1024 * 1024; // Convert GB to bytes
          }
          else {
            // Default to treating as bytes
            totalFileSize += parseInt(sizeStr, 10) || 0;
          }
        } catch (error) {
          console.warn(`Could not parse file size: ${file.fileSize}`);
        }
      }
      
      // Convert to KB for a more manageable number
      const datasets = Math.round(totalFileSize / 1024);
      
      // Check if profile exists
      const existingProfile = await db
        .select()
        .from(agentProfiles)
        .where(eq(agentProfiles.agentId, agent.id));
      
      if (existingProfile.length > 0) {
        // Update existing profile - only update metrics, not features
        await db
          .update(agentProfiles)
          .set({
            totalChats,
            datasets,
            // Keep existing rating and features, update model if available
            model: agent.model === 'chat-model' ? 'gpt-4o' : (agent.model || 'gpt-4o'),
            updatedAt: new Date(),
          })
          .where(eq(agentProfiles.agentId, agent.id));
        console.log(`Updated profile metrics for agent: ${agent.agentName}`);
      } else {
        // Create new profile with empty features
        // Features should be generated when agent becomes active, not here
        await db
          .insert(agentProfiles)
          .values({
            agentId: agent.id,
            rating: 0, // Default rating
            totalChats,
            datasets,
            model: agent.model === 'chat-model' ? 'gpt-4o' : (agent.model || 'gpt-4o'), // Use agent's model or default to gpt-4o
            features: [], // Leave empty for AI generation
            pendingFeatureGeneration: true, // Mark as pending feature generation
          });
        console.log(`Created new profile for agent: ${agent.agentName}`);
      }
    }
    
    console.log('🎉 Agent profiles update completed successfully');
  } catch (error) {
    console.error('❌ Agent profiles update failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
    process.exit(0);
  }
}

// Run the update function
updateAgentProfiles().then(() => {
  console.log('✅ Agent profiles update process completed');
});
