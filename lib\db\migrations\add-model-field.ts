import { sql } from 'drizzle-orm';
import { pgTable, text } from 'drizzle-orm/pg-core';

export async function up(db: any) {
  // Add model field to agents table
  await db.execute(sql`ALTER TABLE agents ADD COLUMN IF NOT EXISTS model TEXT`);

  // Update existing agents to move model from monetization to the new field
  await db.execute(sql`
    UPDATE agents
    SET model = monetization->>'model'
    WHERE monetization->>'model' IS NOT NULL
  `);

  // Clear the model field from the monetization JSON
  await db.execute(sql`
    UPDATE agents
    SET monetization = jsonb_set(
      monetization,
      '{model}',
      'null'::jsonb,
      true
    ) - 'model'
    WHERE monetization->>'model' IS NOT NULL
  `);
}

export async function down(db: any) {
  // Move model back to monetization field
  await db.execute(sql`
    UPDATE agents
    SET monetization = jsonb_set(
      COALESCE(monetization, '{}'::jsonb),
      '{model}',
      to_jsonb(model),
      true
    )
    WHERE model IS NOT NULL
  `);

  // Drop the model column
  await db.execute(sql`ALTER TABLE agents DROP COLUMN IF EXISTS model`);
}
