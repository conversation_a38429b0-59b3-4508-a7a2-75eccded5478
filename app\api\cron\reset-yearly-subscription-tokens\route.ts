import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { sql } from '@vercel/postgres';
import { subscriptionBalance } from '@/lib/db/schema';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(request: Request) {
  console.log('🔄 Starting yearly subscription token reset cron job...');
  try {
    // Verify the request is from a trusted source (e.g., Vercel Cron)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      console.log('❌ Authorization failed: Invalid cron secret');
      return new NextResponse('Unauthorized', { status: 401 });
    }
    console.log('✅ Authorization successful');
    // Get current date without time component for comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find active yearly subscriptions that expire today
    // Find yearly subscriptions that need to be reset
    console.log('📊 Finding active yearly subscriptions...');
    const { rows: subscriptionsToReset } = await sql.query(`
      SELECT id FROM "CreatorSubscriptions"
      WHERE subscription_type = 'yearly'
      AND status = 'active'
    `);

    

    // Update tokensLeft for found subscriptions
    if (subscriptionsToReset.length > 0) {
      // Update subscription balance for each subscription
      for (const subscription of subscriptionsToReset) {
        // Update CreatorSubscriptions
        await sql.query(`
          UPDATE "CreatorSubscriptions"
          SET expiry_date = DATE_TRUNC('day', $1::date + INTERVAL '1 month'),
              updated_at = NOW()
          WHERE id = $2
          AND subscription_type = 'yearly'
          AND status = 'active'
        `, [today.toISOString(), subscription.id]);

        // Update SubscriptionBalance
        await sql.query(`
          UPDATE "SubscriptionBalance"
          SET expiry_date = DATE_TRUNC('day', $1::date + INTERVAL '1 month'),
              updated_at = NOW(),
              "tokensLeft" = "total_tokens"
          WHERE subscription_id = $2
          AND expiry_date = $1::date
        `, [today.toISOString(), subscription.id]);
      }

      console.log('✅ Successfully reset yearly subscription tokens and updated expiry dates');

      return NextResponse.json({
        success: true,
        message: `Reset tokens for ${subscriptionsToReset.length} yearly subscriptions`,
        updatedSubscriptions: subscriptionsToReset.map(s => (s as { id: string }).id)
      });
    }

    return NextResponse.json({
      success: true,
      message: 'No yearly subscriptions need token reset today'
    });

  } catch (error) {
    console.error('Error in yearly subscription token reset:', error);
    return NextResponse.json(
      { error: 'Failed to reset yearly subscription tokens' },
      { status: 500 }
    );
  }
}
