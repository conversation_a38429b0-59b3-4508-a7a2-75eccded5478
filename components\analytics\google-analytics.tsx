'use client';

import { useEffect, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

// Google Analytics configuration
// Handle both measurement ID (G-XXXXXX) and property ID (numeric) formats
const GA_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '';
const GA_MEASUREMENT_ID = GA_ID.startsWith('G-') ? GA_ID : `G-${GA_ID}`;

// Inner component that uses useSearchParams
function GoogleAnalyticsContent() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Only run on client-side and in production
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production' && GA_MEASUREMENT_ID) {
      // Load Google Analytics script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
      document.head.appendChild(script);

      // Initialize Google Analytics
      window.dataLayer = window.dataLayer || [];
      function gtag(...args: any[]) {
        window.dataLayer.push(args);
      }
      gtag('js', new Date());
      gtag('config', GA_MEASUREMENT_ID, {
        send_page_view: false, // We'll handle page views manually
      });

      // Track initial page view
      gtag('event', 'page_view', {
        page_path: pathname,
        page_search: searchParams.toString(),
      });

      // Clean up on unmount
      return () => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      };
    }
  }, []);

  // Track page changes
  useEffect(() => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production' && GA_MEASUREMENT_ID) {
      function gtag(...args: any[]) {
        window.dataLayer.push(args);
      }
      
      gtag('event', 'page_view', {
        page_path: pathname,
        page_search: searchParams.toString(),
      });
    }
  }, [pathname, searchParams]);

  // This component doesn't render anything
  return null;
}

// Export the component wrapped in Suspense
export function GoogleAnalytics() {
  return (
    <Suspense fallback={null}>
      <GoogleAnalyticsContent />
    </Suspense>
  );
}

// Add TypeScript declaration
declare global {
  interface Window {
    dataLayer: any[];
  }
}
