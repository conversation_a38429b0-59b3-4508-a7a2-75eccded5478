'use client';

import { useToast } from './use-toast';
import { cn } from '@/lib/utils';
import { X } from 'lucide-react';
import { useEffect } from 'react';

export function Toaster() {
  const { toasts, dismiss } = useToast();

  return (
    <div className="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]">
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          toast={toast}
          onDismiss={() => dismiss(toast.id)}
        />
      ))}
    </div>
  );
}

function Toast({
  toast,
  onDismiss,
}: {
  toast: ReturnType<typeof useToast>['toasts'][number];
  onDismiss: () => void;
}) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss();
    }, 5000);

    return () => clearTimeout(timer);
  }, [onDismiss]);

  return (
    <div
      className={cn(
        'mb-2 flex w-full items-center justify-between rounded-md border p-4 shadow-lg',
        {
          'bg-destructive text-destructive-foreground': toast.variant === 'destructive',
          'bg-background text-foreground': toast.variant !== 'destructive',
        }
      )}
    >
      <div className="flex flex-col gap-1">
        {toast.title && <div className="font-semibold">{toast.title}</div>}
        {toast.description && <div className="text-sm">{toast.description}</div>}
      </div>
      <button
        onClick={onDismiss}
        className="ml-4 rounded-full p-1 hover:bg-muted"
      >
        <X size={16} />
      </button>
    </div>
  );
}
