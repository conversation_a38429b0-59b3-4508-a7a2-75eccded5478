import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

export function AgentFormSkeleton() {
  return (
    <div className="space-y-8">
      {/* Basic info section skeleton */}
      <Card className="border shadow-sm">
        <CardContent className="pt-6 space-y-4">
          <Skeleton className="h-6 w-40 mb-2" />
          
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Appearance section skeleton */}
      <Card className="border shadow-sm">
        <CardContent className="pt-6 space-y-4">
          <Skeleton className="h-6 w-40 mb-2" />
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <Skeleton className="h-10 w-40" />
            </div>
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Monetization section skeleton */}
      <Card className="border shadow-sm">
        <CardContent className="pt-6 space-y-4">
          <Skeleton className="h-6 w-40 mb-2" />
          
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Button skeleton */}
      <div className="flex justify-end">
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  );
}

// A simpler skeleton specifically for the edit page
export function EditFormSkeleton() {
  return (
    <Card className="border shadow-sm">
      <CardContent className="pt-6 space-y-6">
        {/* Agent name and info skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </div>
        
        {/* Form fields skeleton */}
        <div className="space-y-6">
          <div className="space-y-2">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
          
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
          
          <div className="space-y-2">
            <Skeleton className="h-5 w-28" />
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
        
        {/* Save button skeleton */}
        <div className="flex justify-end">
          <Skeleton className="h-10 w-32" />
        </div>
      </CardContent>
    </Card>
  );
}
