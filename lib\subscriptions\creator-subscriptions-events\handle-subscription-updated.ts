import { db } from '@/lib/db';
import { creatorSubscriptions } from '@/lib/db/schema';
import { and, eq, ne } from 'drizzle-orm';
import Stripe from 'stripe';
import { findPlanByName, createSubscriptionBalanceWithUsage, createSubscription, updateOtherSubscriptionsInactive, findExistingSubscription, updateSubscription } from '@/lib/db/subscription-utils';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil'
});

export async function handleUpdatedSubscriptionForCreator(
  userId: string,
  subscription: Stripe.Subscription,
  previous: Partial<Stripe.Subscription> | undefined,
  schedule?: Stripe.SubscriptionSchedule
) {

  console.log('schedule', schedule)
  // Get expiry date from subscription or schedule
  let expiryDate: string;
  let nextProduct;
  let effectiveDate;
  const newPriceId = subscription.items.data[0].price.id;
  const newProductId = subscription.items.data[0].price.product;
  let oldPriceId;
  let oldProductId;
  if (previous){
    oldPriceId = previous.items?.data[0]?.price?.id;
    oldProductId = previous.items?.data[0]?.price?.product;
  }

  const isPriceOrProductChanged = newPriceId !== oldPriceId || newProductId !== oldProductId;

  const price = await stripe.prices.retrieve(newPriceId);
  const product = await stripe.products.retrieve(price.product as string);


  const now = Math.floor(Date.now() / 1000);
if (schedule){
  const nextPhase = schedule.phases.find(phase => phase.start_date > now);
if (nextPhase) {
  const nextPriceId = nextPhase.items[0].price;
  const nextPrice = await stripe.prices.retrieve(nextPriceId as string);
   nextProduct = await stripe.products.retrieve(nextPrice.product as string);

}


}

  
  

  const stripeSubscription = subscription as unknown as {
    current_period_end?: number;
    schedule?: {
      current_phase?: {
        end_date: number;
      };
    };
  };

  if (stripeSubscription.current_period_end) {
    expiryDate = new Date(stripeSubscription.current_period_end * 1000).toISOString();
  } else if (stripeSubscription.schedule?.current_phase?.end_date) {
    // If we're handling a schedule update, use the schedule's end date
    expiryDate = new Date(stripeSubscription.schedule.current_phase.end_date * 1000).toISOString();
    
    // Find the future phase for effective date
    if (schedule) {
      const futurePhase = schedule.phases.find(
        (phase) => phase.start_date > Math.floor(Date.now() / 1000) // timestamps are in seconds
      );
      
      if (futurePhase) {
        effectiveDate = new Date(futurePhase.start_date * 1000);
      }
    }
  } else {
    // Fallback to current date + interval
    const endDate = new Date();
    if (price.recurring?.interval === 'year') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    } else {
      endDate.setMonth(endDate.getMonth() + 1);
    }
    expiryDate = endDate.toISOString();
  }
    
  const subscriptionType = price.recurring?.interval === 'year' ? 'yearly' : 'monthly';

   // Check if this is a scheduled change or an immediate upgrade
   const hasSchedule = stripeSubscription.schedule?.current_phase !== undefined || subscription.schedule !== null;
   const isScheduledChange = (hasSchedule || subscription.pending_update !== null) && isPriceOrProductChanged;
   const isImmediateUpgrade = !hasSchedule && !subscription.pending_update && previous?.items && isPriceOrProductChanged;
 
   console.log('Change type:', {
     isScheduledChange,
     isImmediateUpgrade,
   });

  if (isScheduledChange) {  // Case 1: Subscription plan changed upgraded or downgraded and scheduled for future
    if (nextProduct) {
       // use the planFinder method here
      const scheduledPlan = await findPlanByName(nextProduct.name);
      if (!scheduledPlan) {
        console.error('Plan not found for product:', nextProduct.name);
        throw new Error('Plan not found');
      }
      const newSubscription = await createSubscription({
        userId,
        planId: scheduledPlan.id,  // Use the plan ID from our database instead of Stripe's product ID
        subscription_type: subscriptionType,
        expiry_date: expiryDate,
        effective_date: effectiveDate,
        status: 'scheduled' as const
      })
      // Create new subscription
     if (newSubscription) {
      console.log('Created new subscription with scheduled status:', { id: newSubscription.id, userId });
     }
    }
    
    
    
  }

  if (isImmediateUpgrade) {
     // use the planFinder method here
    const newPlan = await findPlanByName(product.name);
    if (!newPlan) {
      console.error('Plan not found for product:', product.name);
      throw new Error('Plan not found');
    }
    const inactiveSubscriptions = await updateOtherSubscriptionsInactive(userId, newPlan.id);
          if (inactiveSubscriptions.length > 0) {
        console.log('Marked other subscriptions as inactive for user:', userId);
      }
  }
}