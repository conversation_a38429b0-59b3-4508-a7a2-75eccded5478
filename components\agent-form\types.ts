import * as z from "zod";

// FileEntry interface for uploaded files
export interface FileEntry {
  id: string;
  name: string;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  file?: File;
  url?: string;
  draftAgentId?: string;
  size?: number;
  progress?: number;
}

// Form schema - Updated with proper defaults to fix TypeScript resolver errors
export const formSchema = z.object({
  id: z.string().optional(),
  agentName: z.string().min(4, "Agent name must be at least 4 characters"),
  slug: z.string().min(4, "URL must be at least 4 characters").regex(/^[a-z0-9]+$/, "URL can only contain lowercase letters and numbers"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  instruction: z.string().min(10, "Instructions must be at least 10 characters"),
  model: z.string().min(1, "Please select a model").default("gpt-4o"),
  isPublic: z.boolean().default(true),
  isPaid: z.boolean().default(false),
  pricingType: z.enum(["subscription", "lifetime"]).optional(),
  price: z.string().default("0"),
  accessLevel: z.enum(["free", "subscription", "lifetime"]).default("free"),
  quickMessages: z.array(z.string()).default([]),
  logo: z.string().optional(),
  websiteUrl: z.string().url("Please enter a valid URL").or(z.literal("")).default(""),
  visibility: z.string().optional(),
  files: z.array(z.any()).default([]),
  selectedLinks: z.array(z.any()).default([]),
  status: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional()
});

// Form values type
export type FormValues = z.infer<typeof formSchema>;
