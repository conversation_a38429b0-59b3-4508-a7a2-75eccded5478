import { config } from 'dotenv';
import { eq, sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { agents } from './schema';
import path from 'path';
import agentData from './converted-agents.json';

// Load environment variables first
config({
  path: '.env.local',
});

// Make sure POSTGRES_URL is available
if (!process.env.POSTGRES_URL) {
  console.error('❌ POSTGRES_URL is not defined in environment variables');
  process.exit(1);
}

// Create database client after environment variables are loaded
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

async function updateAgentModels() {
  try {
    console.log('⏳ Starting agent model update...');
    // biome-ignore lint: Forbidden non-null assertion.
    console.log(`Using database URL: ${process.env.POSTGRES_URL!.split('@')[1]}`); // Log only the host part for security
    
    // First, migrate existing data from monetization.model to model field
    console.log('🔄 Migrating existing model values from monetization field...');
    await db.execute(sql`
      UPDATE agents
      SET model = monetization->>'model'
      WHERE monetization->>'model' IS NOT NULL
    `);
    console.log('✅ Migrated existing model values from monetization field');

    // Clean up the model field from monetization
    console.log('🧹 Removing model field from monetization JSON...');
    await db.execute(sql`
      UPDATE agents
      SET monetization = monetization - 'model'
      WHERE monetization ? 'model'
    `);
    console.log('✅ Removed model field from monetization JSON');

    // Update agents with new model values from our JSON data
    console.log('🔄 Updating agents with new model values...');
    for (const agent of agentData) {
      if (agent.slug && agent.model) {
        await db
          .update(agents)
          .set({ model: agent.model })
          .where(eq(agents.slug, agent.slug));
        console.log(`✅ Updated agent ${agent.slug} with model ${agent.model}`);
      }
    }

    console.log('🎉 Agent model update completed successfully!');
  } catch (error) {
    console.error('❌ Agent model update failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
    process.exit(0);
  }
}

// Run the update function
updateAgentModels().then(() => {
  console.log('✅ Update process completed');
});
