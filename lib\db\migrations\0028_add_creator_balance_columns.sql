-- Drop userId column and add subscription_id foreign key
ALTER TABLE "CreatorBalance" DROP COLUMN IF EXISTS "userId";
ALTER TABLE "CreatorBalance" ADD COLUMN IF NOT EXISTS "subscription_id" UUID;

-- Add foreign key constraint
ALTER TABLE "CreatorBalance" ADD CONSTRAINT "creator_balance_subscription_id_fkey"
    FOREIGN KEY ("subscription_id") REFERENCES "CreatorSubscriptions" ("id")
    ON DELETE CASCADE;

-- Add new columns to CreatorBalance table
ALTER TABLE "CreatorBalance" ADD COLUMN IF NOT EXISTS "total_tokens" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "CreatorBalance" ADD COLUMN IF NOT EXISTS "total_agents" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "CreatorBalance" ADD COLUMN IF NOT EXISTS "total_knowledgebase" INTEGER NOT NULL DEFAULT 0;
