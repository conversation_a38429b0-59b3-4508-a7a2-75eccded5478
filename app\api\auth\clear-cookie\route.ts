import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Create a response that will clear cookies
    const response = NextResponse.json({ success: true });
    
    // List of common auth-related cookies to clear
    const cookiesToClear = [
      'next-auth.session-token',
      'next-auth.callback-url',
      'next-auth.csrf-token',
      '__Secure-next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Host-next-auth.csrf-token',
      'dashboardVerified',
      'user-session',
      'auth-token'
    ];
    
    // Clear all cookies by setting them to expire
    cookiesToClear.forEach((cookieName: string) => {
      response.cookies.set(cookieName, '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        expires: new Date(0), // Set to epoch time (1970-01-01) to delete
        path: '/',
      });
    });
    
    console.log('All cookies cleared via API');
    return response;
  } catch (error) {
    console.error('Error clearing cookies:', error);
    return NextResponse.json({ success: false, error: 'Failed to clear cookies' }, { status: 500 });
  }
}
