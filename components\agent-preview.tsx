import Image from 'next/image';
import { MessageSquare, User, Sparkles } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface AgentPreviewProps {
  agentName: string;
  description: string;
  logo: string | null;
  isPaid: boolean;
  price: string;
  email: string;
  quickMessages?: string[];
}

export const AgentPreview = ({
  agentName = 'Agent Name',
  description = 'Agent description will appear here',
  logo = null,
  isPaid = false,
  price = '0',
  email = '<EMAIL>',
  quickMessages = []
}: AgentPreviewProps) => {
  // Extract name from email (part before @)
  const creatorName = email 
    ? email.includes('@') 
      ? email.split('@')[0] 
      : email
    : 'creator';

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-6 bg-background border rounded-lg shadow-sm">
      <div className="flex flex-col items-center max-w-[320px]">
        {/* Agent Logo - Squared with rounded corners */}
        <div className="mb-5">
          <div className="relative size-[88px] overflow-hidden rounded-lg border">
            {logo ? (
              <Image 
                src={logo} 
                alt={agentName}
                fill
                className="object-cover"
              />
            ) : (
              <div className="size-full bg-muted flex items-center justify-center">
                <span className="text-2xl font-medium text-muted-foreground">
                  {agentName ? agentName.charAt(0).toUpperCase() : 'A'}
                </span>
              </div>
            )}
          </div>
        </div>
        
        {/* Agent Name and Badge */}
        <div className="mb-5 flex items-center justify-center gap-2">
          <h2 className="text-[28px] font-semibold tracking-tight">
            {agentName || 'Agent Name'}
          </h2>
          
          {isPaid && (
            <Badge 
              className="bg-green-500/10 text-green-700 dark:text-green-500 hover:bg-green-500/20"
            >
              ${parseFloat(price || '0').toFixed(2)}
            </Badge>
          )}
        </div>
        
        {/* Greeting and Description - Centered with optimal width */}
        <div className="text-center max-w-[320px] mb-6">
          <div className="text-base text-muted-foreground">
            {description 
              ? `${description.split('.')[0].toLowerCase()}.` 
              : 'How can I help you today?'}
          </div>
        </div>
        
        {/* Quick Messages */}
        {quickMessages.length > 0 && (
          <div className="w-full flex flex-wrap gap-2 mb-6">
            {quickMessages.map((message, index) => (
              <div 
                key={index}
                className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center cursor-pointer hover:bg-secondary/80 transition-colors"
              >
                {message}
              </div>
            ))}
          </div>
        )}
        
        {/* Metadata - Subtle and centered */}
        <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <User className="size-4" />
            <span>{creatorName}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <MessageSquare className="size-4" />
            <span>0 chats</span>
          </div>
        </div>
        
        {/* Preview Label */}
        <div className="mt-8 flex items-center gap-2 text-xs text-muted-foreground border border-dashed border-muted-foreground/30 rounded-full px-3 py-1">
          <Sparkles className="h-3 w-3" />
          <span>Preview Mode</span>
        </div>
      </div>
    </div>
  );
};
