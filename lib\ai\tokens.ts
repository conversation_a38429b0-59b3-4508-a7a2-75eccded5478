import { encode } from 'gpt-tokenizer';

// Map of model names to their encoding types
const MODEL_TO_ENCODING: Record<string, string> = {
  'gpt-3.5-turbo': 'cl100k_base',
  'gpt-4': 'cl100k_base',
  'gpt-4-turbo': 'cl100k_base',
  'gpt-4o': 'cl100k_base',
  'claude-3-opus-20240229': 'cl100k_base', // Using OpenAI's encoding as an approximation
  'claude-3-sonnet-20240229': 'cl100k_base',
  'claude-3-haiku-20240307': 'cl100k_base',
  // 'gemini-pro': 'cl100k_base', // Using OpenAI's encoding as an approximation
  // 'mistral-small': 'cl100k_base', // Using OpenAI's encoding as an approximation
  // 'mistral-medium': 'cl100k_base',
  // 'mistral-large': 'cl100k_base',
  // Add more models as needed
};

// Default encoding for unknown models
const DEFAULT_ENCODING = 'cl100k_base';

/**
 * Count tokens in a string for a specific model
 * @param text The text to count tokens for
 * @param model The model to count tokens for
 * @returns The number of tokens
 */
export function countTokens(text: string, model: string = 'gpt-4'): number {
  // If the model is a Claude model but doesn't have a version suffix, add the default version
  if (model.startsWith('claude-3-opus') && !model.includes('2024')) {
    model = 'claude-3-opus-20240229';
  } else if (model.startsWith('claude-3-sonnet') && !model.includes('2024')) {
    model = 'claude-3-sonnet-20240229';
  } else if (model.startsWith('claude-3-haiku') && !model.includes('2024')) {
    model = 'claude-3-haiku-20240307';
  }

  try {
    // Encode the text and return the token count
    // Note: encode() doesn't take encoding type as a parameter in this version
    // It uses cl100k_base by default which works for both OpenAI and Claude models
    const tokens = encode(text);
    return tokens.length;
  } catch (error) {
    console.error('Error counting tokens:', error);
    // Fallback to a rough character-based estimate
    return Math.ceil(text.length / 4);
  }
}

/**
 * Count tokens in a message for a specific model
 * @param message The message to count tokens for
 * @param model The model to count tokens for
 * @returns The number of tokens
 */
export function countMessageTokens(
  message: { role: string; content: string | string[] },
  model: string = 'gpt-4'
): number {
  try {
    // Convert content array to string if needed
    const content = Array.isArray(message.content) 
      ? message.content.join(' ') 
      : message.content;
    
    // Count tokens in the message content
    const contentTokens = countTokens(content, model);
    
    // Add tokens for message format (role, etc.) - this is an approximation
    // Different models have different message format overheads
    const roleTokens = 3; // ~3 tokens for role
    
    return contentTokens + roleTokens;
  } catch (error) {
    console.error('Error counting message tokens:', error);
    return 0;
  }
}

/**
 * Count tokens in a conversation for a specific model
 * @param messages The messages to count tokens for
 * @param model The model to count tokens for
 * @returns An object with promptTokens, completionTokens, and totalTokens
 */
export function countConversationTokens(
  messages: Array<{ role: string; content: string | string[] }>,
  model: string = 'gpt-4'
): { promptTokens: number; completionTokens: number; totalTokens: number } {
  try {
    let promptTokens = 0;
    let completionTokens = 0;
    
    // Count tokens for each message
    for (const message of messages) {
      const tokens = countMessageTokens(message, model);
      
      if (message.role === 'assistant') {
        completionTokens += tokens;
      } else {
        promptTokens += tokens;
      }
    }
    
    // Add tokens for conversation format (varies by model)
    // This is an approximation based on OpenAI's documentation
    const formatTokens = 3; // ~3 tokens for conversation format
    promptTokens += formatTokens;
    
    const totalTokens = promptTokens + completionTokens;
    
    return { promptTokens, completionTokens, totalTokens };
  } catch (error) {
    console.error('Error counting conversation tokens:', error);
    return { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
  }
}

/**
 * Convert UI message format to the format used by token counters
 * @param message UI message
 * @returns Message in token counter format
 */
export function convertUIMessageForTokenCounting(message: any): { role: string; content: string } {
  // Handle different message formats
  if (message.parts) {
    // Handle message with parts array
    const content = Array.isArray(message.parts) 
      ? message.parts.map(part => typeof part === 'string' ? part : JSON.stringify(part)).join(' ')
      : typeof message.parts === 'string' 
        ? message.parts 
        : JSON.stringify(message.parts);
    
    return {
      role: message.role,
      content
    };
  } else if (message.content) {
    // Handle message with content field
    const content = Array.isArray(message.content)
      ? message.content.join(' ')
      : message.content;
    
    return {
      role: message.role,
      content
    };
  }
  
  // Fallback for unknown formats
  return {
    role: message.role,
    content: typeof message === 'string' ? message : JSON.stringify(message)
  };
}
