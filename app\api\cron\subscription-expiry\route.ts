import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { userSubscriptions } from '@/lib/db/schema';
import { and, eq, lt, sql } from 'drizzle-orm';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(request: Request) {
  console.log("Cron Job started to reset the expired subscriptions")
  try {
    // Verify the request is from a trusted source (e.g., Vercel Cron)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get current date at start of day (midnight) in UTC
    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);

    // Update expired subscriptions
    const result = await db
      .update(userSubscriptions)
      .set({
        is_paid: false,
        allowed_free_messages: 5,
        subscription_type: null,
        expiry_date: null,
        updated_at: new Date()
      })
      .where(
        and(
          eq(userSubscriptions.is_paid, true),
          lt(userSubscriptions.expiry_date, today)
        )
      )
      .returning();

    return NextResponse.json({
      success: true,
      updatedSubscriptions: result.length
    });
  } catch (error) {
    console.error('Error in subscription expiry cron:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
