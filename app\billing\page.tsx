"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  CreditCard, 
  Crown, 
  Download,
  Calendar,
  Check,
  X,
  ChevronsUpDown,
  Sparkles,
  Plus,
  Zap,
  CheckCircle,
  Loader2
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from 'next/link';
import { toast } from 'sonner';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { PlanCard, Plan } from '@/components/billing/plan-card';

// Interface for CreatorPlan from the database
interface CreatorPlan extends Plan {
  balance?: {
    tokensLeft: number;
    agentsLeft: number;
    knowledgeBaseLeft: number;
    expiryDate: string | null;
  }
}

// Interface for billing history item
interface BillingHistoryItem {
  id: string;
  date: string;
  amount: number;
  status: string;
  plan: string;
}

export default function BillingPage() {
  useEffect(() => {
    document.title = 'Billing | BuildThatIdea';
    
    // Check for subscription success parameter in URL
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const subscriptionStatus = params.get('subscription');
      
      if (subscriptionStatus === 'success') {
        toast.success(
          <div className="flex flex-col gap-2">
            <p className="font-medium">Subscription activated!</p>
            <p className="text-sm">Your subscription has been successfully activated. You can now enjoy all the benefits of your plan.</p>
          </div>,
          { duration: 6000 }
        );
        
        // Remove the subscription parameter from URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
        
        // Refresh subscription data
        fetchSubscriptionData();
      } else if (subscriptionStatus === 'canceled') {
        toast.error(
          <div className="flex flex-col gap-2">
            <p className="font-medium">Subscription canceled</p>
            <p className="text-sm">Your subscription process was canceled. If you need help, please contact support.</p>
          </div>,
          { duration: 6000 }
        );
        
        // Remove the subscription parameter from URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, []);

  const [currentPlan, setCurrentPlan] = useState<CreatorPlan | null>(null);
  const [availablePlans, setAvailablePlans] = useState<CreatorPlan[]>([]);
  const [isEditingPayment, setIsEditingPayment] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasPlan, setHasPlan] = useState(false);
  const [planName, setPlanName] = useState("");
  const [subscribingPlanId, setSubscribingPlanId] = useState<string | null>(null);
  const [billingHistory, setBillingHistory] = useState<BillingHistoryItem[]>([]);
  const [subscriptionType, setSubscriptionType] = useState<'monthly' | 'yearly'>('monthly');
  const [resourceUsage, setResourceUsage] = useState<{
    tokensUsed: number;
    agentsCreated: number;
    knowledgeBaseUsed: string | number;
  }>({
    tokensUsed: 0,
    agentsCreated: 0,
    knowledgeBaseUsed: "0 MB"
  });
  const [activeAgentsCount, setActiveAgentsCount] = useState<number>(0);
  const [qualifiesForTrial, setQualifiesForTrial] = useState(false);

  // Function to fetch active agents count from existing API
  const fetchActiveAgentsCount = async () => {
    try {
      const response = await fetch('/api/agents/all');
      if (response.ok) {
        const data = await response.json();
        // Count only active agents from the response
        const activeCount = data.agents?.filter((agent: any) => agent.status === 'active').length || 0;
        setActiveAgentsCount(activeCount);
        // console.log(`Found ${activeCount} active agents out of ${data.agents?.length || 0} total agents`);
      } else {
        console.error('Failed to fetch agents data');
        setActiveAgentsCount(0);
      }
    } catch (error) {
      console.error('Error fetching agents data:', error);
      setActiveAgentsCount(0);
    }
  };

  // Function to handle subscription
  const handleSubscribe = async (planId: string, subscriptionType: 'monthly' | 'yearly') => {
    try {
      setSubscribingPlanId(planId);
      
      // Get the plan details
      const selectedPlan = availablePlans.find(plan => plan.id === planId);
      if (!selectedPlan) {
        throw new Error('Plan not found');
      }

      // For new users with no plan selecting Hobby, use the trial flow
      const isNewUserHobbyTrial = selectedPlan.name.toLowerCase() === 'hobby' && !currentPlan;
      // console.log("Starting subscription process:", {
      //   planId,
      //   planName: selectedPlan.name,
      //   isNewUserHobbyTrial,
      //   subscriptionType
      // });
      
      // Special case: New user selecting Hobby plan (trial flow)
      if (isNewUserHobbyTrial) {
        // Check if user qualifies for trial
        const trialResponse = await fetch('/api/subscription/hobby-plan', {
          method: 'POST',
        });
        
        const trialData = await trialResponse.json();
        
        if (trialData.qualifiesForTrial) {
          // Redirect to free trial flow
          const response = await fetch('/api/agents/free-trial-hobby', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              planId,
              subscriptionType,
            }),
          });
          
          const data = await response.json();
          if (data.url) {
            window.location.href = data.url;
            return;
          }
        }
      } 
      // For existing users, use the portal session for all plan changes
      else if (currentPlan) {
        // console.log("Opening portal session for plan change");
        
        // Create a portal session
        const response = await fetch('/api/subscription/portal', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        const data = await response.json();
        // console.log("Portal response:", data);
        
        if (data.url) {
          // Redirect to Stripe portal
          // console.log("Redirecting to portal:", data.url);
          window.location.href = data.url;
        } else {
          toast.error('Failed to create portal session');
          console.error('Portal error:', data.error);
        }
      }
      // New users selecting Pro plan (no trial)
      else {
        // Proceed with regular checkout
        // console.log("Creating checkout session for plan:", planId);
        const response = await fetch('/api/creator/subscription/create-checkout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            planId,
            subscriptionType,
          }),
        });
        
        const data = await response.json();
        // console.log("Checkout response:", data);
        
        if (data.url) {
          // Show success message
          toast.success('Redirecting to checkout...');
          
          // Redirect to Stripe checkout
          // console.log("Redirecting to checkout:", data.url);
          window.location.href = data.url;
        } else {
          toast.error('Failed to create checkout session');
          console.error('Checkout error:', data.error);
        }
      }
    } catch (error) {
      toast.error('An error occurred while processing your request');
      console.error('Subscription error:', error);
    } finally {
      setSubscribingPlanId(null);
    }
  };

  // Fetch subscription data from the API
  const fetchSubscriptionData = async () => {
    try {
      setIsLoading(true);
      // console.log("Fetching subscription data...");
      const response = await fetch('/api/billing/user-subscription');
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscription data');
      }
      
      const data = await response.json();
      // console.log("Subscription data received:", data);
      
      // Set state with the fetched data
      setCurrentPlan(data.currentPlan);
      setAvailablePlans(data.availablePlans || []);
      setHasPlan(data.hasPlan || false);
      setSubscriptionType(data.subscriptionType);
      setBillingHistory(data.billingHistory || []);
      
      // console.log("Current plan set to:", data.currentPlan);
      
      // Check if user qualifies for free trial
      if (data.qualifiesForTrial !== undefined) {
        setQualifiesForTrial(data.qualifiesForTrial);
      } else {
        // If not specified, assume new users qualify
        setQualifiesForTrial(!data.hasPlan);
      }
      
      // Set resource usage data
      if (data.resourceUsage) {
        setResourceUsage(data.resourceUsage);
        // console.log("Resource usage data:", data.resourceUsage);
      } else {
        // If no resource usage data is provided, set default values
        // For testing, set very low values to ensure downgrades are allowed
        const defaultUsage = {
          tokensUsed: data.currentPlan?.tokensAllowed ? Math.min(1000, Math.round(data.currentPlan.tokensAllowed * 0.1)) : 0,
          agentsCreated: data.currentPlan?.agentsAllowed ? Math.min(1, Math.round(data.currentPlan.agentsAllowed * 0.1)) : 0,
          knowledgeBaseUsed: data.currentPlan?.knowledgeBaseSize ? 
            (typeof data.currentPlan.knowledgeBaseSize === 'string' ? 
              `${Math.min(1, Number.parseFloat(data.currentPlan.knowledgeBaseSize) * 0.1).toFixed(1)} MB` : 
              Math.min(1024 * 1024, Math.round(data.currentPlan.knowledgeBaseSize * 0.1))
            ) : "0 MB"
        };
        
        setResourceUsage(defaultUsage);
        // console.log("Using default resource usage:", defaultUsage);
      }
      
      if (data.currentPlan) {
        setPlanName(data.currentPlan.name);
      }
      
      // Fetch active agents count for plan validation
      await fetchActiveAgentsCount();
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  // Format date to readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { year: 'numeric', month: 'short', day: 'numeric' }).format(date);
  };

  // Get plan icon based on plan name
  const getPlanIcon = (planName: string) => {
    const name = planName.toLowerCase();
    if (name === 'hobby') {
      return <Sparkles className="h-4 w-4 text-amber-500 mr-2" />;
    } else if (name === 'pro') {
      return <Zap className="h-4 w-4 text-indigo-500 mr-2" />;
    } else {
      return <Crown className="h-4 w-4 text-primary mr-2" />;
    }
  };

  // Format savings percentage
  const getSavingsPercentage = (monthlyPrice: number, yearlyPrice: number) => {
    const monthlyCostForYear = monthlyPrice * 12;
    const savings = monthlyCostForYear - yearlyPrice;
    const percentage = Math.round((savings / monthlyCostForYear) * 100);
    return percentage;
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Billing</h1>
      </div>
      
      <div className="space-y-8">
        {/* Current Plan */}
        <div>
          <h2 className="text-xl font-bold mb-4">Current Plan</h2>
          {isLoading ? (
            <Card className="border shadow-sm">
              <CardContent className="p-6">
                <div className="flex flex-col space-y-3">
                  <div className="h-6 w-24 bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-48 bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                </div>
              </CardContent>
            </Card>
          ) : !hasPlan ? (
            <Card className="border shadow-sm">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-3 py-4">
                  <p className="text-sm text-muted-foreground max-w-md">
                    You don't have an active plan. Select from the available plans below.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : currentPlan && (
            <PlanCard 
              plan={currentPlan}
              currentPlanName={currentPlan.name}
              onSubscribe={handleSubscribe}
              isSubscribing={subscribingPlanId === currentPlan.id}
              isCurrentPlan={true}
              resourceUsage={{
                ...resourceUsage,
                agentsCreated: activeAgentsCount // Use active agents count for plan validation
              }}
              subscriptionType={subscriptionType}
            />
          )}
          
          {/* Available Plans Section */}
          <div className="mt-6">
            <h2 className="text-xl font-bold mb-4">Available Plans</h2>
            
            {isLoading ? (
              <div className="space-y-4">
                <Card className="border shadow-sm overflow-hidden">
                  <div className="p-6 flex flex-col space-y-3">
                    <div className="h-6 w-24 bg-muted animate-pulse rounded"></div>
                    <div className="h-4 w-48 bg-muted animate-pulse rounded"></div>
                    <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                  </div>
                </Card>
                <Card className="border shadow-sm overflow-hidden">
                  <div className="p-6 flex flex-col space-y-3">
                    <div className="h-6 w-24 bg-muted animate-pulse rounded"></div>
                    <div className="h-4 w-48 bg-muted animate-pulse rounded"></div>
                    <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                  </div>
                </Card>
              </div>
            ) : (
              <div className="space-y-4">
                {availablePlans
                  .filter(plan => {
                    // If user has no plan, show only Hobby
                    if (!currentPlan) {
                      return plan.name.toLowerCase() === 'hobby';
                    }
                    // If user has a trial or any plan, show all plans except current
                    return currentPlan.id !== plan.id;
                  })
                  .map((plan) => (
                    <PlanCard 
                      key={plan.id}
                      plan={plan}
                      currentPlanName={currentPlan?.name}
                      onSubscribe={handleSubscribe}
                      isSubscribing={subscribingPlanId === plan.id}
                      isCurrentPlan={false}
                      resourceUsage={{
                        ...resourceUsage,
                        agentsCreated: activeAgentsCount // Use active agents count for plan validation
                      }}
                      subscriptionType={'monthly'}
                      showFreeTrial={!currentPlan && plan.name.toLowerCase() === 'hobby'}
                    />
                  ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Billing History */}
        {hasPlan && billingHistory.length > 0 && (
          <div>
            <h2 className="text-xl font-bold mb-4">Billing History</h2>
            <Card className="border shadow-sm">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Invoice</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {billingHistory.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell className="font-medium">{invoice.id}</TableCell>
                        <TableCell>{formatDate(invoice.date)}</TableCell>
                        <TableCell>${invoice.amount.toFixed(2)}</TableCell>
                        <TableCell>
                          {invoice.status === 'Paid' ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              {invoice.status}
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/20 dark:text-yellow-400">
                              {invoice.status}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>{invoice.plan}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="outline" size="sm" className="h-8 text-xs">
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
